package crossborder

import (
	v2 "code.byted.org/canal/provider/bytefaas/v2"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"context"
)

const (
	USFaaSGetServiceByPSMAndEnvKey = "USFaaSGetServiceByPSMAndEnv"
	USFaaSListAllClustersKey       = "USFaaSListAllClusters"
)

type USFaaSGetServiceByPSMAndEnvReq struct {
	EnvName string
	Psm     string
}

type USFaaSGetServiceByPSMAndEnvResp struct {
	FaaSService *v2.Service
}

type USFaaSListAllClustersReq struct {
	ServiceID string
}

type USFaaSListAllClustersResp struct {
	FaaSCluster []*v2.Cluster
}

func (sdk *CrossborderSDK) USFaaSGetServiceByPSMAndEnv(ctx context.Context, envName, psm string) (*v2.Service, error) {
	resp, err := CallUSRemoteAny[USFaaSGetServiceByPSMAndEnvResp](ctx, USFaaSGetServiceByPSMAndEnvKey, USFaaSGetServiceByPSMAndEnvReq{
		EnvName: envName,
		Psm:     psm,
	})
	if err != nil {
		return nil, erri.Error(err)
	}
	return resp.FaaSService, nil
}

func (sdk *CrossborderSDK) USFaaSListAllClusters(ctx context.Context, serviceID string) ([]*v2.Cluster, error) {
	resp, err := CallUSRemoteAny[USFaaSListAllClustersResp](ctx, USFaaSListAllClustersKey, USFaaSListAllClustersReq{
		ServiceID: serviceID,
	})
	if err != nil {
		return nil, erri.Error(err)
	}
	return resp.FaaSCluster, nil
}
