{"name": "zxm ci test1", "actor": "zxm", "stages": [{"uid": "stage", "name": "codebase_ci", "jobs": [{"uid": "zxm-test", "name": "Function test", "retry_interval_in_sec": 0, "timeout_in_sec": 7200, "working_directory": "/home/<USER>", "runs_on": {"label": "onesite", "docker": {"container": "default"}}, "steps": [{"uid": "zxm-test-step_1", "name": "Test expression", "error_policy": "abort", "use_container_entrypoint": false, "use_container_working_dir": false, "commands": ["echo 'contains(\"123\", \"2\") is ${{ contains(\"123\", \"2\") }}'\n", "echo 'index(\"1234567\", \"2\") is ${{ index(\"1234567\", \"2\") }}'\n", "echo '${{ Event.Name == \"change\" ? Event.Change.ChangedFiles: \"push\" }}'\n", "echo '${{ last(make_strings(\"one\", \"two\", \"three\", \"last\")) }}'\n", "echo 'files under .codebase/ changed? ${{ glob(\".codebase/*\", Event.Change.ChangedFiles) }}'\n"], "envs": {"CI_STEP_ID": "zxm-test-step_1", "CI_STEP_NAME": "Test expression"}}, {"uid": "zxm-test-step_2", "name": "Python clone", "error_policy": "abort", "action": {"uid": "actions_checkout", "repo_name": "", "inputs": {"path": ".", "platform": "gitlab", "ref": "feat/test_zxm", "repository": "hagrid/pipeline"}}, "use_container_entrypoint": false, "use_container_working_dir": false, "envs": {"CI_STEP_ID": "zxm-test-step_2", "CI_STEP_NAME": "Python clone"}}, {"uid": "zxm-test-step_3", "name": "Run shell commands", "error_policy": "abort", "use_container_entrypoint": false, "use_container_working_dir": false, "commands": ["pwd", "whoami", "ls -alh", "date", "echo \"::set-env name=FOOFOO::BARBAR\"\n", "echo \"::add-message level=info:: everything is ok!!\"\n"], "envs": {"CI_STEP_ID": "zxm-test-step_3", "CI_STEP_NAME": "Run shell commands"}}, {"uid": "zxm-test-step_4", "name": "Print cross-step envs", "error_policy": "abort", "use_container_entrypoint": false, "use_container_working_dir": false, "commands": ["echo $FOOFOO", "echo \"::set-output name=FOOFOO::$FOOFOO\""], "envs": {"CI_STEP_ID": "zxm-test-step_4", "CI_STEP_NAME": "Print cross-step envs"}}], "docker": {"containers": [{"name": "default", "image": "hub.byted.org/data-ci-debian", "mounts": [{"name": "tmp", "mount_path": "/tmp", "read_only": false}, {"name": "sslib", "mount_path": "/toutiao/ss_lib/", "read_only": true}, {"name": "ssconf", "mount_path": "/toutiao/ss_conf/", "read_only": true}, {"name": "chadc", "mount_path": "/toutiao/chadc/", "read_only": true}, {"name": "<PERSON><PERSON><PERSON>", "mount_path": "/toutiao/pyutil/", "read_only": true}, {"name": "ssthriftgen", "mount_path": "/toutiao/ss_thrift_gen/", "read_only": true}, {"name": "consul_agent", "mount_path": "/opt/tmp/consul_agent/", "read_only": true}, {"name": "databus_data", "mount_path": "/opt/tmp/databus_data/", "read_only": true}, {"name": "ttlogagent", "mount_path": "/opt/tmp/ttlogagent/", "read_only": true}, {"name": "consul_sock", "mount_path": "/opt/tmp/sock/consul.sock", "read_only": true}, {"name": "opt_tmp", "mount_path": "/opt/tmp_codebase_ci", "read_only": true}, {"name": "workspace", "mount_path": "/home/<USER>", "read_only": false}]}, {"name": "hagrid-docker", "image": "hub.byted.org/data-ci-debian", "mounts": [{"name": "tmp", "mount_path": "/tmp", "read_only": false}, {"name": "sslib", "mount_path": "/toutiao/ss_lib/", "read_only": true}, {"name": "ssconf", "mount_path": "/toutiao/ss_conf/", "read_only": true}, {"name": "chadc", "mount_path": "/toutiao/chadc/", "read_only": true}, {"name": "<PERSON><PERSON><PERSON>", "mount_path": "/toutiao/pyutil/", "read_only": true}, {"name": "ssthriftgen", "mount_path": "/toutiao/ss_thrift_gen/", "read_only": true}, {"name": "consul_agent", "mount_path": "/opt/tmp/consul_agent/", "read_only": true}, {"name": "databus_data", "mount_path": "/opt/tmp/databus_data/", "read_only": true}, {"name": "ttlogagent", "mount_path": "/opt/tmp/ttlogagent/", "read_only": true}, {"name": "consul_sock", "mount_path": "/opt/tmp/sock/consul.sock", "read_only": true}, {"name": "opt_tmp", "mount_path": "/opt/tmp_codebase_ci", "read_only": true}, {"name": "workspace", "mount_path": "/home/<USER>", "read_only": false}]}], "volumes": [{"name": "tmp"}, {"name": "sslib", "host_path": "/opt/tiger/ss_lib/"}, {"name": "ssconf", "host_path": "/opt/tiger/ss_conf/"}, {"name": "chadc", "host_path": "/opt/tiger/chadc/"}, {"name": "<PERSON><PERSON><PERSON>", "host_path": "/opt/tiger/pyutil/"}, {"name": "ssthriftgen", "host_path": "/opt/tiger/ss_thrift_gen/"}, {"name": "consul_agent", "host_path": "/opt/tmp/consul_agent/"}, {"name": "databus_data", "host_path": "/opt/tmp/databus_data/"}, {"name": "ttlogagent", "host_path": "/opt/tmp/ttlogagent/"}, {"name": "consul_sock", "host_path": "/opt/tmp/sock/consul.sock"}, {"name": "opt_tmp", "host_path": "/opt/tmp"}, {"name": "workspace"}]}, "envs": {"CI_REPO_WORKSPACE": "/home/<USER>", "CI_JOB_ID": "zxm-test", "CI_JOB_NAME": "Function test", "CI_JOB_WORKING_DIRECTORY": "/home/<USER>", "CI_JOB_IMAGE": "hub.byted.org/data-ci-debian", "GOMAXPROCS": "6", "CI_WORKING_DIRECTORY": "/home/<USER>", "CI_WORKSPACE": "/home/<USER>"}, "sem_map": null}]}]}