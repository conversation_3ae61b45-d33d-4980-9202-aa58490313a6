package jirasdk

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/bits/hephaestus/pkg/jsons"
)

func TestProjects(t *testing.T) {
	ctx := context.Background()
	email := "jira_admin"
	apiToken := "k#DUJ7bK"
	client := New("https://jira-boe.byted.org")

	t.Run("ListAllProjects", func(t *testing.T) {
		t.SkipNow()

		projects := client.ListAllProjects(ctx, email, apiToken).Must()

		for _, project := range projects {
			fmt.Println(jsons.Stringify(project))
		}
	})

	t.Run("ListRecentProjects", func(t *testing.T) {
		t.SkipNow()

		projects := client.ListRecentProjects(ctx, email, apiToken).Must()

		for _, project := range projects {
			fmt.Println(jsons.Stringify(project))
		}
	})

	t.Run("GetProject", func(t *testing.T) {
		id := "10305"

		project := client.GetProject(ctx, email, apiToken, id).Must()

		fmt.Println(jsons.Stringify(project))
	})
}
