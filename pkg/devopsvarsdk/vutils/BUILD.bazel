load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "vutils",
    srcs = [
        "change_item_helper.go",
        "control_plane_helper.go",
        "env_lane_helper.go",
        "release_ticket_helper.go",
        "variable_helper.go",
        "work_item_helper.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pkg/devopsvarsdk/vutils",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/devinfra/cd/release_ticket/shared:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "//libs/bits_err",
        "//pkg/devopsvarsdk/vconsts",
        "//pkg/devopsvarsdk/vmodel",
        "//pkg/resource/protocol",
        "@com_github_json_iterator_go//:go",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_canal_bytecycle_sdk//change_item",
        "@org_byted_code_canal_bytecycle_sdk//resource",
    ],
)

go_test(
    name = "vutils_test",
    srcs = [
        "change_item_helper_test.go",
        "control_plane_helper_test.go",
        "env_lane_helper_test.go",
        "release_ticket_helper_test.go",
        "variable_helper_test.go",
        "work_item_helper_test.go",
    ],
    embed = [":vutils"],
    deps = [
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/devinfra/cd/release_ticket/shared:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "//pkg/devopsvarsdk/vconsts",
        "//pkg/devopsvarsdk/vmodel",
        "@com_github_ghetzel_testify//suite",
        "@com_github_json_iterator_go//:go",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_byted_code_lang_gg//gslice",
    ],
)
