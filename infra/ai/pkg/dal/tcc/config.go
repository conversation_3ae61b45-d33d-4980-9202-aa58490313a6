package tcc

import (
	"context"
	"encoding/json"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/gopkg/tccclient"
)

type ConfigCenter struct {
	inner *tccclient.ClientV2
}

func NewConfigCenter(inner *tccclient.ClientV2) *ConfigCenter {
	return &ConfigCenter{
		inner: inner,
	}
}

type WorkspaceGreyConfigs struct {
	McpGrepWorkspaceId []uint64 `json:"mcp_grep_workspace_id"`
}

func (c *ConfigCenter) GetGreyConfigs(ctx context.Context) WorkspaceGreyConfigs {
	key := "ai_mcp_chat_workspace_grey"

	getter := c.inner.NewGetter(key, json.Unmarshal, WorkspaceGreyConfigs{})
	value, err := getter(ctx)
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to fetch tcc config").Error(err).KV("key", key).Emit()
		panic(err)
	}
	log.V2.Info().With(ctx).KV("value", value).Emit()

	final := WorkspaceGreyConfigs{}
	_ = json.Unmarshal([]byte(jsons.Stringify(value)), &final)
	v, ok := value.(WorkspaceGreyConfigs)
	if !ok {
		log.V2.Error().With(ctx).Str("failed to cast type").Emit()
		return WorkspaceGreyConfigs{}
	}
	logs.V2.Info().With(ctx).Str("[get_grey_config_final]").KV("value", v).KV("final", final).Emit()
	return final
}
