load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "tos",
    srcs = [
        "anonymous_bytes.go",
        "anonymous_file.go",
        "qrcode.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/store/biz/handler/http/tos",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/store/biz",
        "//infra/store/biz/config",
        "//infra/store/biz/functools",
        "//infra/store/biz/handler",
        "//infra/store/biz/service/tos",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_skip2_go_qrcode//:go-qrcode",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
