package tea

import (
	"context"
	"sync"
	"time"

	"code.byted.org/data/mario_collector"
	"code.byted.org/data/mario_collector/pb_event"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
)

// collector协程安全， 请在进程内复用；
// 如业务单个服务内要上报多种类型的数据，请分别按日志类型初始化&上报

type H map[string]any

var (
	collector *mario_collector.MarioCollector
	initOnce  sync.Once
)

const EventName = "dev_task_meego_trans_node"

func SendTea(ctx context.Context, uid string, params map[string]any) {
	if !env.IsProduct() {
		return
	}
	if collector == nil {
		initOnce.Do(func() {
			mario_collector.Logger.SetLevel(logs.LevelWarn)
			collector = mario_collector.NewMarioCollector(env.PSM(), "4f6dec2d0f396de2eade7ec381183fad852f8175", mario_collector.LOG_TYPE_EVENT)
		})
	}
	user := &pb_event.User{
		UserUniqueId: proto.String(uid),
	}

	var appId uint32 = 440311
	paramsBytes, _ := json.MarshalString(params)
	err := collector.CollectEvent(user, &pb_event.Header{
		AppId: &appId,
	}, &pb_event.Event{
		Event:  proto.String(EventName),
		Time:   proto.Uint32(uint32(time.Now().Unix())),
		Params: proto.String(paramsBytes),
	})
	if err != nil {
		logs.CtxError(ctx, "collect event error: %v", err)
	}
}
