package devtaskservice

import (
	"context"
	"regexp"
	"strconv"
	"sync"

	"code.byted.org/devinfra/hagrid/infra/horizon/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/infra/horizon/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends/devtaskapi"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends/integration"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends/metaapi"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends/releaseticket"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends/spaceapi"
	v2 "code.byted.org/devinfra/hagrid/infra/horizon/pkg/lifecycle/v2"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	cache "github.com/Code-Hex/go-generics-cache"
	"github.com/ucarion/urlpath"
)

type DevTaskService struct {
	urlpatterns    []urlpath.Path
	devtaskapi     devtaskapi.Api
	metaapi        metaapi.Api
	spaceapi       spaceapi.Api
	releaseapi     releaseticket.Api
	integrationapi integration.Api

	commonDevBasic    *cache.Cache[int64, *dev.DevTaskCommonBaiscInfo]
	releaseTicketInfo *cache.Cache[int64, *dev.RelatedReleaseTicketInfo]
	apps              *cache.Cache[string, *meta.AppSimpleInfo]
	larkusers         *cache.Cache[string, *meta.User]
	larkids           *cache.Cache[string, *meta.Id]
}

var _ v2.LarkUrlPreview = &DevTaskService{}

func NewDevTaskService(devtaskApi devtaskapi.Api, metaApi metaapi.Api, spaceApi spaceapi.Api, releaseApi releaseticket.Api, integrationApi integration.Api) *DevTaskService {
	return &DevTaskService{
		devtaskapi:        devtaskApi,
		metaapi:           metaApi,
		spaceapi:          spaceApi,
		releaseapi:        releaseApi,
		integrationapi:    integrationApi,
		commonDevBasic:    cache.New[int64, *dev.DevTaskCommonBaiscInfo](),
		releaseTicketInfo: cache.New[int64, *dev.RelatedReleaseTicketInfo](),
		apps:              cache.New[string, *meta.AppSimpleInfo](),
		larkusers:         cache.New[string, *meta.User](),
		larkids:           cache.New[string, *meta.Id](),
	}
}

func (service *DevTaskService) getBasicInfoFromUrl(ctx context.Context, url string) (int64, int64) {
	re := regexp.MustCompile(`devops/(\d+)/develop/detail/(\d+)`)
	matches := re.FindStringSubmatch(url)
	if len(matches) != 3 {
		return 0, 0
	}
	spaceId, _ := strconv.ParseInt(matches[1], 10, 64)
	devBasicId, _ := strconv.ParseInt(matches[2], 10, 64)
	logs.V2.Info().With(ctx).KVs("space_id", spaceId, "dev_basic_id", devBasicId).Emit()
	return spaceId, devBasicId
}

func (service *DevTaskService) UrlPatternCheck(ctx context.Context, url string) gresult.R[bool] {
	logs.V2.Info().With(ctx).Str("UrlPatternCheck: %s", url).Emit()
	flag := false
	if sp, dv := service.getBasicInfoFromUrl(ctx, url); sp > 0 && dv > 0 {
		flag = true
	}
	return gresult.OK(flag)
}

func (service *DevTaskService) GetUrlPreviewInline(ctx context.Context, url string) gresult.R[string] {
	_, devBasicId := service.getBasicInfoFromUrl(ctx, url)
	adaptor := NewAdaptor(defaultTemplate)
	// 获取 dev 的基础信息
	devBasicInfo, err := service.devtaskapi.GetDevTaskBasicInfo(ctx, devBasicId).Get()
	if err != nil {
		logs.V2.Error().With(ctx).Str("GetDevTaskBasicInfo failed").Error(err).Emit()
		return gresult.Err[string](err)
	}
	return gresult.OK(adaptor.GetInline(devBasicInfo))
}

func (service *DevTaskService) GetUrlPreviewDetail(ctx context.Context, url string) gresult.R[string] {
	// 从 URL 中获取完整的 spaceId, devBasicId
	spaceId, devBasicId := service.getBasicInfoFromUrl(ctx, url)
	adaptor := NewAdaptor(defaultTemplate)

	wg := sync.WaitGroup{}
	wg.Add(4)
	utils.SafeGo(ctx, func() {
		defer wg.Done()
		// 获取 dev 的基础信息
		devBasicInfo, err := service.devtaskapi.GetDevTaskBasicInfo(ctx, devBasicId).Get()
		if err != nil || devBasicInfo == nil || devBasicInfo.BasicInfo == nil {
			logs.V2.Error().With(ctx).Str("GetDevTaskBasicInfo failed").Error(err).Emit()
			return
		}
		adaptor.ReplaceDevBasicInfo(devBasicInfo)
		// 获取人员的 lark 信息
		users := make(map[int][]*meta.Id, 0)
		gslice.ForEach(devBasicInfo.GetMembers(), func(v *dev.DevTaskMember) {
			res, err := service.metaapi.QueryLarkId(ctx, v.GetEmail()).Get()
			if err != nil {
				logs.V2.Error().With(ctx).Str("QueryLarkId failed").Error(err).Emit()
				return
			}
			if users[int(v.GetRole())] == nil {
				users[int(v.GetRole())] = make([]*meta.Id, 0)
			}
			users[int(v.GetRole())] = append(users[int(v.GetRole())], res)
		})
		adaptor.ReplaceDevBasicUsers(users)
		// 获取研发流程配置
		if devBasicInfo.BasicInfo.TeamFlowId == 0 {
			adaptor.ReplaceWorkflowTypeIcon(nil)
			return
		}
		conf, err := service.releaseapi.GetTeamFlowInfo(ctx, devBasicInfo.BasicInfo.TeamFlowId).Get()
		if err != nil || conf == nil || conf.TeamFlowConfig == nil {
			logs.V2.Error().With(ctx).Str("GetTeamFlowInfo failed").Error(err).Emit()
			return
		}
		adaptor.ReplaceWorkflowTypeIcon(conf)
	})

	utils.SafeGo(ctx, func() {
		defer wg.Done()
		// 获取 dev 的阶段信息
		devWorkflowInfo, err := service.devtaskapi.GetDevWorkflow(ctx, devBasicId).Get()
		if err != nil || devWorkflowInfo == nil || devWorkflowInfo.Stages == nil {
			logs.V2.Error().With(ctx).Str("GetDevWorkflow failed").Error(err).Emit()
			return
		}
		adaptor.ReplaceDevStageInfo(devWorkflowInfo)
	})

	utils.SafeGo(ctx, func() {
		defer wg.Done()
		// 获取空间信息
		spaceInfo, err := service.spaceapi.GetSpaceName(ctx, spaceId).Get()
		if err != nil || spaceInfo == nil || spaceInfo.Texts == nil {
			logs.V2.Error().With(ctx).Str("GetSpaceName failed").Error(err).Emit()
			return
		}
		adaptor.ReplaceSpaceInfo(spaceInfo)
	})

	utils.SafeGo(ctx, func() {
		defer wg.Done()
		// 如果有绑定对应的发布单就返回对应的发布单信息
		integration, err := service.integrationapi.GetIntegrationByDevTask(ctx, devBasicId).Get()
		if err != nil || integration == nil || integration.GetIntegrationInfo() == nil {
			logs.V2.Error().With(ctx).Str("GetIntegrationByDevTask failed").Error(err).Emit()
			return
		}
		if integration.GetIntegrationInfo() == nil || integration.GetIntegrationInfo().ReleaseTicketId == 0 {
			adaptor.ReplaceReleaseInfo(nil, nil)
			return
		}
		releaseInfo, err := service.releaseapi.GetReleaseTicket(ctx, integration.GetIntegrationInfo().ReleaseTicketId).Get()
		if err != nil || releaseInfo == nil || releaseInfo.ReleaseTicket == nil {
			adaptor.ReplaceReleaseInfo(nil, nil)
			logs.V2.Error().With(ctx).Str("GetReleaseTicket failed").Error(err).Emit()
			return
		}
		// 获取人员的 lark 信息
		allUser := gslice.Concat(releaseInfo.ReleaseTicket.ReleaseApprovers, releaseInfo.ReleaseTicket.TestApprovers)

		logs.V2.Info().With(ctx).Str("all the user:").Obj(allUser).Emit()
		usersLarkId := make([]string, 0)
		gslice.ForEach(gslice.Uniq(allUser), func(v string) {
			res, err := service.metaapi.QueryLarkId(ctx, v).Get()
			if err != nil || res == nil {
				logs.V2.Error().With(ctx).Str("QueryLarkId failed").Error(err).Emit()
				return
			}
			usersLarkId = append(usersLarkId, res.GetOpenId())
		})
		adaptor.ReplaceReleaseInfo(releaseInfo, usersLarkId)
	})

	wg.Wait()
	return gresult.OK(adaptor.GetFinalTemplate())
}
