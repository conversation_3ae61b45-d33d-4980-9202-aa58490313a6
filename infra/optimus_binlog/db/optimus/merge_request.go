package optimus

import (
	"context"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/model"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/service/database"
	"strconv"
)

func GetMergeRequestInfoInProjectIDAndIID(ctx context.Context, condition [][]interface{}) ([]*model.OptimusGitlabMergerequest, error) {
	mergeRequest := make([]*model.OptimusGitlabMergerequest, 0)

	res := database.Optimus.Master.NewRequest(ctx).Select("*").Where("(project_id,iid) in (?)", condition).Find(&mergeRequest)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return mergeRequest, nil
}

func GetMergeRequestInfoByProjectIDAndIID(ctx context.Context, projectID string, iID int64) (*model.OptimusGitlabMergerequest, error) {
	mergeRequest := &model.OptimusGitlabMergerequest{}

	res := database.Optimus.Master.NewRequest(ctx).Select("*").Where("project_id = ? AND iid = ?", projectID, iID).Find(mergeRequest)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return mergeRequest, nil
}

func GetMergeRequestDependencyByID(ctx context.Context, ID int64) (*model.OptimusGitlabMergeRequestDependency, error) {
	dependency := &model.OptimusGitlabMergeRequestDependency{}

	res := database.Optimus.Master.NewRequest(ctx).Where("id = ?", ID).Find(dependency)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return dependency, nil
}

func GetMergeRequestDependenciesByID(ctx context.Context, ID int64) (*model.OptimusGitlabMergeRequestDependency, error) {
	dependencies := &model.OptimusGitlabMergeRequestDependency{}

	res := database.Optimus.Master.NewRequest(ctx).Where("id = ?", ID).Find(dependencies)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return dependencies, nil
}

func GetMergeRequestDependenciesByOptimusMrID(ctx context.Context, hostID int64) ([]*model.OptimusGitlabMergeRequestDependency, error) {
	dependencies := make([]*model.OptimusGitlabMergeRequestDependency, 0)

	res := database.Optimus.Master.NewRequest(ctx).Where("optimus_mr_id = ?", hostID).Order("id", false).Find(&dependencies)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return dependencies, nil
}

func GetMergeRequestInfoByMrID(ctx context.Context, mrID int64) (*model.OptimusGitlabMergerequest, error) {
	mergeRequest := &model.OptimusGitlabMergerequest{}

	res := database.Optimus.Master.NewRequest(ctx).Where("id = ?", mrID).Find(mergeRequest)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return mergeRequest, nil
}

func GetMergeRequestDependencyByProjectIDAndIID(ctx context.Context, projectID int64, iID int64, useCache bool) (*model.OptimusGitlabMergeRequestDependency, error) {
	dependency := &model.OptimusGitlabMergeRequestDependency{}
	sID := strconv.FormatInt(projectID, 10)
	res := database.Optimus.Master.NewRequest(ctx).Where("project_id = ? AND iid = ? ", sID, iID).Find(dependency)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return dependency, nil
}

func GetMergeRequestDependenciesByHostID(ctx context.Context, hostID int64) ([]*model.OptimusGitlabMergeRequestDependency, error) {
	dependencies := make([]*model.OptimusGitlabMergeRequestDependency, 0)

	res := database.Optimus.Master.NewRequest(ctx).Where("host_id = ?", hostID).Find(&dependencies)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return dependencies, nil
}
