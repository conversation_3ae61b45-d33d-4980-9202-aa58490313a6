package controller

import (
	"code.byted.org/gopkg/dbus"
	"code.byted.org/gopkg/logs"
	"context"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/db/optimus"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/model"
	optimusService "code.byted.org/devinfra/hagrid/infra/optimus_binlog/pkg/service/optimus"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/pkg/utils"
)

const CodeChangeGitlabMergeRequest = "code_change_gitlab_merge_request"

func HandleInsertCodeChangeGitlabMergeRequest(ctx context.Context, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	var after *model.CodeChangeGitlabMergeRequest
	err := utils.UnmarshalColumns(ctx, afterColumns, &after)
	if err != nil {
		logs.CtxWarn(ctx, "failed to unmarshal columns to CodeChangeGitlabMergeRequest:%+v,error:%s", afterColumns, err.Error())
		return nil
	}
	contributionCodeChange, err := optimus.GetContributionCodeChangeByCodeChangeId(ctx, after.CodeChangeId)
	if err != nil {
		logs.CtxError(ctx, "failed to get contribution code change:%s", err.Error())
		return err
	}
	if contributionCodeChange == nil {
		logs.CtxError(ctx, "failed to get contribution code change, not exist:%d", after.CodeChangeId)
		return err
	}
	err = optimusService.DevBasicIdAdaptor(ctx, contributionCodeChange.DevBasicId)
	if err != nil {
		logs.CtxError(ctx, "failed to handle insert dev basic info:%s", err.Error())
		return err
	}
	return nil
}

func HandleUpdateCodeChangeGitlabMergeRequest(ctx context.Context, beforeColumns, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	var after *model.CodeChangeGitlabMergeRequest
	err := utils.UnmarshalColumns(ctx, afterColumns, &after)
	if err != nil {
		logs.CtxWarn(ctx, "failed to unmarshal columns to CodeChangeGitlabMergeRequest:%+v,error:%s", afterColumns, err.Error())
		return nil
	}
	contributionCodeChange, err := optimus.GetContributionCodeChangeByCodeChangeId(ctx, after.CodeChangeId)
	if err != nil {
		logs.CtxError(ctx, "failed to get contribution code change:%s", err.Error())
		return err
	}
	if contributionCodeChange == nil {
		logs.CtxError(ctx, "failed to get contribution code change, not exist:%d", after.CodeChangeId)
		return err
	}
	err = optimusService.DevBasicIdAdaptor(ctx, contributionCodeChange.DevBasicId)
	if err != nil {
		logs.CtxError(ctx, "failed to handle insert dev basic info:%s", err.Error())
		return err
	}
	return nil
}
