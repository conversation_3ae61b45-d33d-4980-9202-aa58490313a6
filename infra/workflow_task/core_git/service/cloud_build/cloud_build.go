package cloud_build

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/infra/workflow_task/core_git/service/http"
	json "github.com/bytedance/sonic"

	"code.byted.org/gopkg/facility/fjson"
)

const (
	CloudBuildTokenURL   = "https://gateway.byted.org/gw/v2/token?email=%<EMAIL>"
	CloudBuildTriggerURL = "https://bits.bytedance.net/openapi/workflow/job/trigger_template"
	CloudBuildStatusURL  = "https://gateway.byted.org/gw/v3/tasks/status?id=%v"
)

func Token(ctx context.Context, username string) (string, error) {
	if username == "" {
		username = "zhangchunsheng"
	}
	url := fmt.Sprintf(CloudBuildTokenURL, username)
	client := http.NewHttpClient()
	_, body, err := client.Get(ctx, url)
	if err != nil {
		return "", err
	}

	type resp struct {
		Data struct {
			Token string `json:"token"`
		} `json:"data"`
	}
	tokenInfo := resp{}
	if !fjson.ConfigCompatibleWithStandardLibrary.Valid([]byte(body)) {
		return "", fmt.Errorf("response %v is not a valid json", body)
	}
	err = json.Unmarshal([]byte(body), &tokenInfo)
	if err != nil {
		return "", err
	}
	return tokenInfo.Data.Token, nil
}

type Input struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type BuildParam struct {
	ID     int64   `json:"id"`
	Inputs []Input `json:"inputs"`
}

type Params struct {
	ID          int64        `json:"id"`
	BuildParams []BuildParam `json:"build_params"`
}

type TriggerParams struct {
	Params   Params `json:"params"`
	Operator string `json:"operator"`
}

func NewTriggerParams(ctx context.Context, gitUrl, sourceBranch, targetBranch string) TriggerParams {

	params := TriggerParams{
		Params{
			int64(4475),
			[]BuildParam{
				{
					2,
					[]Input{
						{
							"MAIN_GIT_URL",
							gitUrl,
						},
						{
							"MAIN_GIT_BRANCH",
							sourceBranch,
						},
					},
				},
				{
					1689,
					[]Input{
						{
							"source_branch_commit",
							sourceBranch,
						},
						{
							"target_branch_commit",
							targetBranch,
						},
					},
				},
			},
		},
		"zhangchunsheng",
	}
	return params
}

func TriggerConflictDetect(ctx context.Context, gitUrl, sourceBranch, targetBranch string) (int64, error) {

	params := NewTriggerParams(ctx, gitUrl, sourceBranch, targetBranch)
	client := http.NewHttpClient()
	_, body, err := client.Post(ctx, CloudBuildTriggerURL, params, map[string]string{})
	if err != nil {
		return 0, err
	}

	type resp struct {
		Code    int64  `json:"code"`
		Message string `json:"message"`
		Data    struct {
			TaskID int64 `json:"taskId"`
		} `json:"data"`
	}
	triggerInfo := resp{}
	if !fjson.ConfigCompatibleWithStandardLibrary.Valid([]byte(body)) {
		return 0, fmt.Errorf("response %v is not a valid json", body)
	}
	err = json.Unmarshal([]byte(body), &triggerInfo)
	if err != nil {
		return 0, err
	}
	if triggerInfo.Message != "ok" {
		return 0, fmt.Errorf("response %v trigger cloud build error", body)
	}
	return triggerInfo.Data.TaskID, nil
}

func ConflictDetectStatus(ctx context.Context, username, gitUrl, sourceBranch, targetBranch string, taskID int64) (int64, error) {

	token, err := Token(ctx, username)
	if err != nil {
		return 0, err
	}

	params := NewTriggerParams(ctx, gitUrl, sourceBranch, targetBranch)
	header := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %v", token),
	}
	client := http.NewHttpClient()
	_, body, err := client.Post(ctx, fmt.Sprintf(CloudBuildStatusURL, taskID), params, header)
	if err != nil {
		return 0, err
	}

	type resp struct {
		Code    int64  `json:"code"`
		Message string `json:"message"`
		Data    struct {
			TaskStatus int64 `json:"task_status"`
		} `json:"data"`
	}
	triggerInfo := resp{}
	if !fjson.ConfigCompatibleWithStandardLibrary.Valid([]byte(body)) {
		return 0, fmt.Errorf("response %v is not a valid json", body)
	}
	err = json.Unmarshal([]byte(body), &triggerInfo)
	if err != nil {
		return 0, err
	}
	if triggerInfo.Message != "success" {
		return 0, fmt.Errorf("response %v get cloud build task status error", body)
	}
	return triggerInfo.Data.TaskStatus, nil
}
