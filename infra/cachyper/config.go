/**
 * @Date: 2023/4/12
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package main

import (
	_ "embed"
	"fmt"
	"code.byted.org/devinfra/hagrid/infra/cachyper/pkg/dal/redis"

	"code.byted.org/gopkg/env"
	"gopkg.in/yaml.v3"
)

type Config struct {
	Redis *redis.Config `yaml:"redis"`
}

func MustInitializeConfig() *Config {
	content := local
	if env.IsProduct() {
		content = prod
	} else if env.IsBoe() {
		content = boe
	}

	config := new(Config)
	if err := yaml.Unmarshal(content, config); err != nil {
		panic(fmt.Errorf("failed unmarshal config: %w", err))
	}
	return config
}

//go:embed conf/local.yaml
var local []byte

//go:embed conf/boe.yaml
var boe []byte

//go:embed conf/prod.yaml
var prod []byte
