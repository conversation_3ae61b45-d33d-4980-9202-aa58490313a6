package business

import (
	"code.byted.org/devinfra/hagrid/infra/code_frozen/consts"
	"code.byted.org/devinfra/hagrid/infra/code_frozen/pkg/model"
	"code.byted.org/gopkg/logs"
	"context"
	"fmt"
	url2 "net/url"
	"strconv"
)

func checkGreyMRCanMerge(ctx context.Context, config *model.VersionConfig, conditions []string, mrInfo *model.MrInfo, greyStatus string, message *model.VersionCanMergeMessage) (bool, *model.VersionCanMergeMessage, error) {
	canMerge := true
	status := model.GreyTicketStatus{
		HaveGreyTicket: "no_need", //true,false,no_need代表没有开启灰度ticket准入，不需要判定是否有ticket
		AllRulesPassed: false,
		NotPassedRules: []string{},
	}
	for _, rule := range conditions {
		switch rule {
		case "qa_passed": //有QA且测试通过
			res, err := MergeCheck.CheckQAReview(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckQAReview err:%v", err)
				return false, message, err
			}
			canMerge = canMerge && res
			if !res {
				status.NotPassedRules = append(status.NotPassedRules, rule)
			}
		case "cr_passed": //CodeReview通过
			res, err := MergeCheck.CheckCodeReview(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckCR err:%v", err)
				return false, message, err
			}
			canMerge = canMerge && res
			if !res {
				status.NotPassedRules = append(status.NotPassedRules, rule)
			}
		case "ppl_passed": //pipeline通过
			res, err := MergeCheck.CheckPipelineStatus(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckPipelineStatus err:%v", err)
				return false, message, err
			}
			canMerge = canMerge && res
			if !res {
				status.NotPassedRules = append(status.NotPassedRules, rule)
			}
		}
	}
	status.AllRulesPassed = canMerge
	message.GreyStatus = &status
	if config.VersionsGrey.Webhook != nil {
		if len(config.VersionsGrey.Webhook.Url) != 0 {
			params := url2.Values{}
			params.Add("mr_id", strconv.FormatInt(mrInfo.MrID, 10))
			params.Add("last_commit_id", mrInfo.Info.LastCommitID)
			params.Add("grey_stage", greyStatus)
			url := fmt.Sprintf("%v?%v", config.VersionsGrey.Webhook.Url, params.Encode())
			customCanMerge, canMergeMessages, err := RequestCustomCanMerge(ctx, url)
			if err != nil {
				return false, message, err
			}
			canMerge = canMerge && customCanMerge
			if !customCanMerge {
				status.NotPassedRules = append(status.NotPassedRules, canMergeMessages...)
			}
		}
	}
	return canMerge, message, nil
}

func checkGreyMR(ctx context.Context, config *model.VersionConfig, mrInfo *model.MrInfo, greyStatus string) (bool, *model.VersionCanMergeMessage, error) {
	canMerge := true
	ruleMatched := false
	notPassedRules := make([]string, 0)
	if config.VersionsGrey.NewRules == nil {
		rules := make([]model.NewGreyRules, 0)
		for _, rule := range config.VersionsGrey.Rules {
			rules = append(rules, model.NewGreyRules{
				Conditions: rule.Conditions,
				MrType:     []string{rule.MrType},
				Stage:      []string{rule.Stage},
				LimitType:  model.LimitType_LIMIT,
			})
		}
	}
	for _, rule := range config.VersionsGrey.NewRules {
		if isRuleMatched(mrInfo, greyStatus, rule) {
			ruleMatched = true
			switch rule.LimitType {
			case model.LimitType_LIMIT:
				rulePassed, notPassed, err := CheckConditionMatch(ctx, mrInfo, rule.Conditions)
				if err != nil {
					logs.CtxError(ctx, "CheckConditionMatch err:%v", err)
					return false, nil, err
				}
				canMerge = canMerge && rulePassed
				notPassedRules = append(notPassedRules, notPassed...)
			case model.LimitType_FORBID:
				canMerge = false
				notPassedRules = []string{consts.NotAllowedMRType}
				message := &model.VersionCanMergeMessage{
					GreyStatus: &model.GreyTicketStatus{
						AllRulesPassed: canMerge,
						NotPassedRules: notPassedRules,
						HaveGreyTicket: model.HaveGreyTicket_NONEED,
					},
				}
				return canMerge, message, nil
			}
		}
	}
	if !ruleMatched {
		if config.VersionsGrey.OtherRule != nil {
			ruleMatched = true
			rulePassed, notPassed, err := CheckConditionMatch(ctx, mrInfo, config.VersionsGrey.OtherRule.Conditions)
			if err != nil {
				logs.CtxError(ctx, "CheckConditionMatch err:%v", err)
				return false, nil, err
			}
			canMerge = canMerge && rulePassed
			notPassedRules = append(notPassedRules, notPassed...)
		}
	}
	if !ruleMatched {
		canMerge = false
		notPassedRules = append(notPassedRules, consts.NotAllowedMRType)
	} else {
		if config.VersionsGrey.Webhook != nil {
			if len(config.VersionsGrey.Webhook.Url) != 0 {
				params := url2.Values{}
				params.Add("mr_id", strconv.FormatInt(mrInfo.MrID, 10))
				params.Add("last_commit_id", mrInfo.Info.LastCommitID)
				params.Add("grey_stage", greyStatus)
				url := fmt.Sprintf("%v?%v", config.VersionsGrey.Webhook.Url, params.Encode())
				customCanMerge, canMergeMessages, err := RequestCustomCanMerge(ctx, url)
				if err != nil {
					return false, nil, err
				}
				canMerge = canMerge && customCanMerge
				if !customCanMerge {
					notPassedRules = append(notPassedRules, canMergeMessages...)
				}
			}
		}
	}
	message := &model.VersionCanMergeMessage{
		GreyStatus: &model.GreyTicketStatus{
			AllRulesPassed: canMerge,
			NotPassedRules: notPassedRules,
			HaveGreyTicket: model.HaveGreyTicket_NONEED,
		},
	}
	return canMerge, message, nil
}

func isRuleMatched(mrInfo *model.MrInfo, greyStatus string, greyRule model.NewGreyRules) bool {
	for _, mrType := range greyRule.MrType {
		for _, stage := range greyRule.Stage {
			if mrInfo.Info.MrType == mrType && greyStatus == stage {
				return true
			}
		}
	}
	return false
}

func CheckConditionMatch(ctx context.Context, mrInfo *model.MrInfo, conditions []string) (bool, []string, error) {
	notPassedRules := make([]string, 0)
	canMerge := true
	if conditions == nil || len(conditions) == 0 {
		notPassedRules = append(notPassedRules, "empty rule")
		return false, notPassedRules, nil
	}
	for _, rule := range conditions {
		switch rule {
		case model.RuleQaNeedAndPassed: //有QA且测试通过
			res, err := MergeCheck.CheckQAReview(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckQAReview err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.RuleCRPassed: //CodeReview通过
			res, err := MergeCheck.CheckCodeReview(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckCR err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.RulePplPassed: //pipeline通过
			res, err := MergeCheck.CheckPipelineStatus(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckPipelineStatus err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.RuleMrNotWip:
			res, err := MergeCheck.CheckMrNotWip(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckMrNotWip err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.RuleNotConflicted:
			res, err := MergeCheck.CheckMrNotConflicted(ctx, mrInfo.MrID)
			if err != nil {
				logs.CtxError(ctx, "CheckMrNotWip err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.ManualReviewPassed:
			res, err := MergeCheck.CheckManualReview(ctx, mrInfo.MrID)
			logs.CtxInfo(ctx, "%d check manual review result: %v, %v", mrInfo.MrID, res, err)
			if err != nil {
				logs.CtxError(ctx, "CheckManualReview err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}

		case model.FeatureCheckPassed:
			res, err := MergeCheck.CheckFeatureCheck(ctx, mrInfo.MrID)
			logs.CtxInfo(ctx, "%d check feature check result: %v, %v", mrInfo.MrID, res, err)
			if err != nil {
				logs.CtxError(ctx, "CheckFeatureCheck err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.AccessCheckPassed:
			res, err := MergeCheck.AccessCheck(ctx, mrInfo.MrID)
			logs.CtxInfo(ctx, "%d check access check result: %v, %v", mrInfo.MrID, res, err)
			if err != nil {
				logs.CtxError(ctx, "AccessCheck err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		case model.AccessLanePassed:
			res, err := MergeCheck.AccessLaneCheck(ctx, mrInfo.MrID)
			logs.CtxInfo(ctx, "%d check access lane check result: %v, %v", mrInfo.MrID, res, err)
			if err != nil {
				logs.CtxError(ctx, "AccessLaneCheck err:%v", err)
				return false, notPassedRules, err
			}
			canMerge = canMerge && res
			if !res {
				notPassedRules = append(notPassedRules, rule)
			}
		}

	}
	return canMerge, notPassedRules, nil
}
