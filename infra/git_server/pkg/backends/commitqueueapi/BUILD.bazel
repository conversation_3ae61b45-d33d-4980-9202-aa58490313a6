load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "commitqueueapi",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/commitqueueapi",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/middleware/restymw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_tidwall_gjson//:gjson",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_lang_gg//gresult",
    ],
)
