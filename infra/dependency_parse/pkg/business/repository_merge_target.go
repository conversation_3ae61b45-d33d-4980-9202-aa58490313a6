package business

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/dependency_parse"
	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/dev_task"
	"code.byted.org/devinfra/hagrid/infra/dependency_parse/pkg/rpc"

	"code.byted.org/gopkg/logs"
)

func MergeTarget(ctx context.Context, devTaskId int64) ([]*dependency_parse.RepositoryMergeTargetResult_, error) {

	// 获取devtask依赖列表
	resp, err := rpc.DevTaskClient.GetDevtaskRepositories(ctx, &dev_task.GetDevtaskRepositoriesRequest{
		DevTaskId: devTaskId,
	})
	if err != nil {
		logs.CtxError(ctx, "get patch err %v", err)
		return nil, err
	}

	app, dependencies, _ := resp.App, resp.Dependencies, resp.PodChanges

	repositories := make([]*dev_task.DevTaskRepository, 0)
	repositories = append(repositories, app)
	repositories = append(repositories, dependencies...)

	arr, err := depsMergeTarget(ctx, repositories)
	if err != nil {
		logs.CtxError(ctx, "get patch err %v", err)
		return arr, err
	}

	return arr, nil
}

// merge target, 需要把lastCommitId改成merge target之后的cid
func depsMergeTarget(ctx context.Context, repositories []*dev_task.DevTaskRepository) ([]*dependency_parse.RepositoryMergeTargetResult_, error) {

	retChannel := make(chan *dependency_parse.RepositoryMergeTargetResult_, len(repositories))
	defer func() {
		close(retChannel)
	}()
	wg := sync.WaitGroup{}

	for idx := range repositories {

		dep := repositories[idx]
		wg.Add(1)
		go func(repo *dev_task.DevTaskRepository) {
			defer wg.Done()

			result, err := DependencyMergeTarget(ctx, repo)
			errMsg := ""
			sourceCid := ""
			mergeCid := ""
			branch := ""
			if err != nil {
				errMsg = err.Error()
			} else {
				sourceCid = result.SourceCommitId
				mergeCid = result.MergeCommitId
				branch = result.ShadowPatch
			}

			retChannel <- &dependency_parse.RepositoryMergeTargetResult_{
				repo.Id,
				branch,
				sourceCid,
				mergeCid,
				errMsg,
			}
		}(dep)
	}

	wg.Wait()

	arr := make([]*dependency_parse.RepositoryMergeTargetResult_, 0)
	for {
		info := <-retChannel
		arr = append(arr, info)
		if len(arr) == len(repositories) {
			break
		}
	}

	err := error(nil)
	for _, info := range arr {
		if info.ErrMsg != "" {
			err = fmt.Errorf("%v", info.ErrMsg)
		}
	}
	return arr, err
}
