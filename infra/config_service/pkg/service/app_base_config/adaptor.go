package app_base_config

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/utils"
	commonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	json "github.com/bytedance/sonic"
)

func AdaptAppBaseConfigToView(ctx context.Context, config *db.AppBaseConfig) (*config_service.DevopsAppBaseConfig, error) {
	directoryOwnerConfig := map[string][]string{}

	if config.DirectoryOwnerConfig != "" {
		err := json.Unmarshal([]byte(config.DirectoryOwnerConfig), &directoryOwnerConfig)
		if err != nil {
			logs.CtxError(ctx, "AdaptAppBaseConfigToView Unmarshal DirectoryOwnerConfig error:%v", err)
			return nil, consts.ErrJsonUnmarshal
		}
	}

	var flutterProjectType *config_service.DevopsFlutterProjectType = nil
	if utils.IsFlutterProjectType(config.FlutterProjectType) {
		val := config_service.DevopsFlutterProjectType(config.FlutterProjectType)
		flutterProjectType = &val
	}
	var androidRelatedId *int64 = nil
	if config.AndroidRelatedId > 0 {
		androidRelatedId = &config.AndroidRelatedId
	}
	var iosRelatedId *int64 = nil
	if config.IosRelatedId > 0 {
		iosRelatedId = &config.IosRelatedId
	}

	result := &config_service.DevopsAppBaseConfig{
		MetaAppID:            config.AppId,
		IsApp:                commonUtils.ConvertInt82Bool(config.IsApp),
		DevelopPattern:       config.DevelopPattern,
		ReleasePattern:       config.ReleasePattern,
		MasterPattern:        config.MasterPattern,
		BizDevelopPattern:    config.BizDevelopPattern,
		BizReleasePattern:    config.BizReleasePattern,
		BizMasterPattern:     config.BizMasterPattern,
		DirectoryOwnerConfig: directoryOwnerConfig,
		AndroidRelease:       config.AndroidRelease,
		AndroidSnapshot:      config.AndroidSnapshot,
		IosRepoKey:           config.IosRepoKey,
		IosSource:            config.IosSource,
		IosBinary:            config.IosBinary,
		IosRootDirectory:     config.IosRootDirectory,
		UiAutoTestID:         config.UiAutoTestId,
		MonkeyAutoTestID:     config.MonkeyAutoTestId,
		AutoTestAppID:        config.AutoTestAppId,
		FlutterProjectType:   flutterProjectType,
		AndroidRelatedID:     androidRelatedId,
		IosRelatedID:         iosRelatedId,
		IosBundleID:          &config.IosBundleId,
		AndroidPackageName:   &config.AndroidPackageName,
		AndroidRootDirectory: &config.AndroidRootDirectory,
		HarmonyRelease:       gptr.OfNotZero(config.HarmonyRelease),
	}
	return result, nil
}
