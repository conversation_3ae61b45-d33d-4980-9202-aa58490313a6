package operation_record

/*
	project config
*/
//pipeline trigger time
type PipelineTriggerTime struct {
	Commit bool `json:"commit"`
	Review bool `json:"review"`
	NewMr  bool `json:"new_mr"`
	Wip    bool `json:"wip"`
}

//jenkins
type Jenkins struct {
	HostURL    string `json:"host_url"`
	Job        string `json:"job"`
	AdminName  string `json:"admin_name"`
	AdminToken string `json:"admin_token"`
}

//rock_binary_source
type RockBinarySource struct {
	Debug   string `json:"Debug"`
	Release string `json:"Release"`
}

/*
	group config
*/
// cony setting
type ConySettings struct {
	BmReview            bool   `json:"bm_review"`
	CalendarWorkspaceID int    `json:"calendar_workspace_id"`
	DevelopBranch       string `json:"develop_branch"`
	Feature             struct {
		RocketBiz   int    `json:"rocket_biz"`
		RocketToken string `json:"rocket_token"`
		Type        string `json:"type"`
	} `json:"feature"`
	FeatureSwitches struct {
		CanUseLink      bool `json:"can_use_link"`
		IntegrationArea bool `json:"integration_area"`
		Mbox            bool `json:"mbox"`
		Release         bool `json:"release"`
	} `json:"feature_switches"`
	GreyBranch   string `json:"grey_branch"`
	IosCertScope string `json:"ios_cert_scope"`
	Jira         struct {
		Config string `json:"config"`
		URL    string `json:"url"`
	} `json:"jira"`
	ReleaseBranch  string `json:"release_branch"`
	ReleaseVersion struct {
		CancelTime struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"cancel_time"`
		EnvelopeDateAlias  string   `json:"envelope_date_alias"`
		EventName          string   `json:"event_name"`
		GroupNumbers       []string `json:"group_numbers"`
		MeegoProjectKey    string   `json:"meego_project_key"`
		OpenReleaseVersion bool     `json:"openReleaseVersion"`
		PublishDateAlias   string   `json:"publish_date_alias"`
		QaBmAlias          string   `json:"qa_bm_alias"`
		RdBmAlias          string   `json:"rd_bm_alias"`
		ReleaseTime        struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"release_time"`
		VersionPattern string `json:"version_pattern"`
		WarnTime       struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"warn_time"`
	} `json:"release_version"`
	ReverseFlowEnable bool `json:"reverse_flow_enable"`
}

//rc branch setting
type RCBranchSettings struct {
	DevelopBranch       string `json:"develop_branch"`
	OriginDevelopBranch string `json:"origin_develop_branch"`
	RcDevelopBranch     string `json:"rc_develop_branch"`
	StableDevelopBranch string `json:"stable_develop_branch"`
}

//mr_type_tip
type MRTypeTipConfig struct {
	Type string `json:"type"`
	Tip  []struct {
		Text     string `json:"text"`
		Language string `json:"language"`
	} `json:"tip"`
}

//bm_review_config
type BMReviewConfig struct {
	QaBm struct {
		MinNum int      `json:"min_num"`
		Branch []string `json:"branch"`
	} `json:"qa_bm"`
	RdBm struct {
		MinNum int      `json:"min_num"`
		Branch []string `json:"branch"`
	} `json:"rd_bm"`
}

//mr type settings
type MrTypeSettings struct {
	BranchDisableSetting map[string][]string `json:"branch_disable_setting"`
}
