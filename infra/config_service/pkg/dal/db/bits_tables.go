// Code generated by COMMENTS_BUILD_TOOLS 2.0.77. DO NOT EDIT.
package db

import (
	"time"

	"gorm.io/datatypes"
)

func (AppInfo) TableName() string {
	return "app_info"
}

func NewAppInfo() *AppInfo {
	return &AppInfo{}
}

type AppInfo struct {
	Id              int64     `gorm:"column:id;primary_key" json:"id"`                 // 主键id,内部app id
	AppCloudId      int64     `gorm:"column:app_cloud_id" json:"app_cloud_id"`         // 应用云的id
	ProductId       int64     `gorm:"column:product_id" json:"product_id"`             // app关联的产品线ID
	RelateAppId     int64     `gorm:"column:relate_app_id" json:"relate_app_id"`       // 关联appId
	LegacyAppId     int64     `gorm:"column:legacy_app_id" json:"legacy_app_id"`       // 老appid
	CreateTime      time.Time `gorm:"column:create_time" json:"create_time"`           // 创建日期
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time"`           // 更新日期
	ChineseName     string    `gorm:"column:chinese_name" json:"chinese_name"`         // 中文名
	EnglishName     string    `gorm:"column:english_name" json:"english_name"`         // 英文名
	TechnologyStack string    `gorm:"column:technology_stack" json:"technology_stack"` // 技术栈 枚举:Android,iOS
	Description     string    `gorm:"column:description" json:"description"`           // 应用描述
	Region          string    `gorm:"column:region" json:"region"`                     // 地区 枚举值, 国内:cn,海外:overseas
	Logo            string    `gorm:"column:logo" json:"logo"`                         // logo地址
	GitUrl          string    `gorm:"column:git_url" json:"git_url"`                   // git地址
	EnName          string    `gorm:"column:en_name" json:"en_name"`                   // english name to show
}

func (AppMember) TableName() string {
	return "app_member"
}

func NewAppMember() *AppMember {
	return &AppMember{}
}

type AppMember struct {
	Id         int64     `gorm:"column:id;primary_key" json:"id"`       // 主键id
	AppId      int64     `gorm:"column:app_id" json:"app_id"`           // 内部的appId
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"` // 创建日期
	UpdateTime time.Time `gorm:"column:update_time" json:"update_time"` // 更新日期
	Role       string    `gorm:"column:role" json:"role"`               // 角色 枚举值:MANAGER;RD;QA
	UserName   string    `gorm:"column:user_name" json:"user_name"`     // 用户名
}

func (ProductInfo) TableName() string {
	return "product_info"
}

func NewProductInfo() *ProductInfo {
	return &ProductInfo{}
}

type ProductInfo struct {
	ProductId       int64     `gorm:"column:product_id;primary_key" json:"product_id"`   // 业务线ID
	ParentProductId int64     `gorm:"column:parent_product_id" json:"parent_product_id"` // 父业务线ID
	DeletedAt       time.Time `gorm:"column:deleted_at" json:"deleted_at"`               // 软删标志字段 type:*time.Time
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time"`             // 更新时间 type:*time.Time
	ProductCnName   string    `gorm:"column:product_cn_name" json:"product_cn_name"`     // 中文名
	ProductName     string    `gorm:"column:product_name" json:"product_name"`           // 英文名
	Owner           string    `gorm:"column:owner" json:"owner"`                         // 负责人
	Description     string    `gorm:"column:description" json:"description"`             // 业务线描述
	Logo            string    `gorm:"column:logo" json:"logo"`                           // logo地址
}

func (UserToken) TableName() string {
	return "user_token"
}

func NewUserToken() *UserToken {
	return &UserToken{}
}

type UserToken struct {
	Id         int64     `gorm:"column:id;primary_key" json:"id"`
	ExpireTime time.Time `gorm:"column:expire_time" json:"expire_time"` // token过期时间
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"` // 创建日期 type:*time.Time
	UpdateTime time.Time `gorm:"column:update_time" json:"update_time"` // 更新日期 type:*time.Time
	Username   string    `gorm:"column:username" json:"username"`       // 用户名
	TokenType  string    `gorm:"column:token_type" json:"token_type"`   // token类型，枚举值:codebase
	Token      string    `gorm:"column:token" json:"token"`             // token值
}

func (BitsAppMemberTeamRelation) TableName() string {
	return "bits_app_member_team_relation"
}

func NewBitsAppMemberTeamRelation() *BitsAppMemberTeamRelation {
	return &BitsAppMemberTeamRelation{}
}

type BitsAppMemberTeamRelation struct {
	TeamId   int       `gorm:"column:team_id" json:"team_id"`     // 对应mpaas_subapp表的id
	Id       int64     `gorm:"column:id;primary_key" json:"id"`   // 主键id
	AppId    int64     `gorm:"column:app_id" json:"app_id"`       // 内部app_id
	Ctime    time.Time `gorm:"column:ctime" json:"ctime"`         // 创建时间 tags:{"gorm":"column:ctime;default:CURRENT_TIMESTAMP"} type:*time.Time
	Mtime    time.Time `gorm:"column:mtime" json:"mtime"`         // 修改时间 tags:{"gorm":"column:mtime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"} type:*time.Time
	UserName string    `gorm:"column:user_name" json:"user_name"` // 用户邮箱前缀
}

func (GitProjectInfo) TableName() string {
	return "git_project_info"
}

func NewGitProjectInfo() *GitProjectInfo {
	return &GitProjectInfo{}
}

type GitProjectInfo struct {
	Id        int64  `gorm:"column:id;primary_key" json:"id"`     // 主键id
	AppId     int64  `gorm:"column:app_id" json:"app_id"`         // 内部的appId
	ProjectId int64  `gorm:"column:project_id" json:"project_id"` // gitlab pid
	RepoId    int64  `gorm:"column:repo_id" json:"repo_id"`       // codebase repo id
	RepoUrl   string `gorm:"column:repo_url" json:"repo_url"`     // repo 地址
}

func (IamApiMap) TableName() string {
	return "iam_api_map"
}

func NewIamApiMap() *IamApiMap {
	return &IamApiMap{}
}

type IamApiMap struct {
	Id           int    `gorm:"column:id;primary_key" json:"id"`         // id
	ApiName      string `gorm:"column:api_name" json:"api_name"`         // 方法名
	Provider     string `gorm:"column:provider" json:"provider"`         // 业务方
	Introduction string `gorm:"column:introduction" json:"introduction"` // 描述
	Platform     string `gorm:"column:platform" json:"platform"`         // IAM-平台
	Resource     string `gorm:"column:resource" json:"resource"`         // IAM-资源
	Action       string `gorm:"column:action" json:"action"`             // IAM-动作
}

func (Channel) TableName() string {
	return "channel"
}

func NewChannel() *Channel {
	return &Channel{}
}

type Channel struct {
	Id              int64     `gorm:"column:id;primary_key" json:"id"`                 // 主键id
	BitsAppId       int64     `gorm:"column:bits_app_id" json:"bits_app_id"`           // bits app id
	CloudAppId      int64     `gorm:"column:cloud_app_id" json:"cloud_app_id"`         // 云应用id
	CreateTime      time.Time `gorm:"column:create_time" json:"create_time"`           // 创建日期
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time"`           // 更新日期
	Code            string    `gorm:"column:code" json:"code"`                         // 渠道编号
	ChannelName     string    `gorm:"column:channel_name" json:"channel_name"`         // 渠道名称
	ChannelType     string    `gorm:"column:channel_type" json:"channel_type"`         // 渠道类型 枚举值:RELEASE,GREY
	TechnologyStack string    `gorm:"column:technology_stack" json:"technology_stack"` // 技术栈 枚举:Android,iOS
	Url             string    `gorm:"column:url" json:"url"`                           // 渠道链接
}

func (AppChannelGroup) TableName() string {
	return "app_channel_group"
}

func NewAppChannelGroup() *AppChannelGroup {
	return &AppChannelGroup{}
}

type AppChannelGroup struct {
	Id              int64  `gorm:"column:id;primary_key" json:"id"`
	AppId           int64  `gorm:"column:app_id" json:"app_id"`                     // appId
	TechnologyStack string `gorm:"column:technology_stack" json:"technology_stack"` // 技术栈 枚举:Android,iOS
	GroupName       string `gorm:"column:group_name" json:"group_name"`             // 渠道分组名称
	Description     string `gorm:"column:description" json:"description"`           // 渠道分组描述
}

func (AppChannel) TableName() string {
	return "app_channel"
}

func NewAppChannel() *AppChannel {
	return &AppChannel{}
}

type AppChannel struct {
	Id        int64 `gorm:"column:id;primary_key" json:"id"`
	AppId     int64 `gorm:"column:app_id" json:"app_id"`         // appId
	GroupId   int64 `gorm:"column:group_id" json:"group_id"`     // 组id
	ChannelId int64 `gorm:"column:channel_id" json:"channel_id"` // 渠道id
}

func (Certificate) TableName() string {
	return "certificate"
}

func NewCertificate() *Certificate {
	return &Certificate{}
}

type Certificate struct {
	Id                 int64  `gorm:"column:id;primary_key" json:"id"`
	AppId              int64  `gorm:"column:app_id" json:"app_id"`                           // appId
	TechnologyStack    string `gorm:"column:technology_stack" json:"technology_stack"`       // 技术栈 枚举:Android,iOS
	Description        string `gorm:"column:description" json:"description"`                 // 证书描述
	ExpireTime         string `gorm:"column:expire_time" json:"expire_time"`                 // 证书过期时间
	CreateUser         string `gorm:"column:create_user" json:"create_user"`                 // 创建用户
	AuthorizedUsers    string `gorm:"column:authorized_users" json:"authorized_users"`       // 授权用户数组
	KeystoreUrl        string `gorm:"column:keystore_url" json:"keystore_url"`               // android keystore证书地址
	Storepass          string `gorm:"column:storepass" json:"storepass"`                     // android 证书文件密码
	Alias              string `gorm:"column:alias" json:"alias"`                             // android 别名
	Keypass            string `gorm:"column:keypass" json:"keypass"`                         // android 别名密码
	P12Url             string `gorm:"column:p12_url" json:"p12_url"`                         // ios p12证书地址
	MobileprovisionUrl string `gorm:"column:mobileprovision_url" json:"mobileprovision_url"` // ios mobileprovision地址
	Password           string `gorm:"column:password" json:"password"`                       // ios 签名证书密码
}

func (AppBaseConfig) TableName() string {
	return "app_base_config"
}

func NewAppBaseConfig() *AppBaseConfig {
	return &AppBaseConfig{}
}

type AppBaseConfig struct {
	IsApp                int8      `gorm:"column:is_app" json:"is_app"`                                 // 是否是线上app
	FirstGrayLr          int8      `gorm:"column:first_gray_lr" json:"first_gray_lr"`                   // 是否在第一轮的灰度流程中开启LR环节
	FlutterProjectType   int8      `gorm:"column:flutter_project_type" json:"flutter_project_type"`     // flutter工程类型
	Id                   int64     `gorm:"column:id;primary_key" json:"id"`                             // 主键id
	AppId                int64     `gorm:"column:app_id" json:"app_id"`                                 // 内部的appId
	UiAutoTestId         int64     `gorm:"column:ui_auto_test_id" json:"ui_auto_test_id"`               // UI自动化测试 关联id
	MonkeyAutoTestId     int64     `gorm:"column:monkey_auto_test_id" json:"monkey_auto_test_id"`       // Monkey自动化测试 关联id
	AutoTestAppId        int64     `gorm:"column:auto_test_app_id" json:"auto_test_app_id"`             // 自动化测试平台内部app_id
	AndroidRelatedId     int64     `gorm:"column:android_related_id" json:"android_related_id"`         // flutter混合开发关联的android宿主
	IosRelatedId         int64     `gorm:"column:ios_related_id" json:"ios_related_id"`                 // flutter混合开发关联的ios宿主
	CreateTime           time.Time `gorm:"column:create_time" json:"create_time"`                       // 创建日期 type:*time.Time
	UpdateTime           time.Time `gorm:"column:update_time" json:"update_time"`                       // 更新日期 type:*time.Time
	DevelopPattern       string    `gorm:"column:develop_pattern" json:"develop_pattern"`               // 集成分支
	ReleasePattern       string    `gorm:"column:release_pattern" json:"release_pattern"`               // 灰度分支
	MasterPattern        string    `gorm:"column:master_pattern" json:"master_pattern"`                 // 正式分支
	BizDevelopPattern    string    `gorm:"column:biz_develop_pattern" json:"biz_develop_pattern"`       // 业务组件 集成分支
	BizReleasePattern    string    `gorm:"column:biz_release_pattern" json:"biz_release_pattern"`       // 业务组件 灰度分支
	BizMasterPattern     string    `gorm:"column:biz_master_pattern" json:"biz_master_pattern"`         // 业务组件 正式分支
	DirectoryOwnerConfig string    `gorm:"column:directory_owner_config" json:"directory_owner_config"` // 目录负责人
	AndroidRelease       string    `gorm:"column:android_release" json:"android_release"`               // 安卓release库地址
	AndroidSnapshot      string    `gorm:"column:android_snapshot" json:"android_snapshot"`             // 安卓snapshot库地址
	AndroidPackageName   string    `gorm:"column:android_package_name" json:"android_package_name"`     // 安卓包名
	AndroidRootDirectory string    `gorm:"column:android_root_directory" json:"android_root_directory"` // 安卓包含 dependency-lock 文件的根路径
	IosRepoKey           string    `gorm:"column:ios_repo_key" json:"ios_repo_key"`                     // ios 组件源标识
	IosSource            string    `gorm:"column:ios_source" json:"ios_source"`                         // ios source库地址
	IosBinary            string    `gorm:"column:ios_binary" json:"ios_binary"`                         // ios 二进制库地址
	IosRootDirectory     string    `gorm:"column:ios_root_directory" json:"ios_root_directory"`         // ios 项目根目录
	IosBundleId          string    `gorm:"column:ios_bundle_id" json:"ios_bundle_id"`                   // ios 线上发布的 bundle id
	HarmonyRelease       string    `gorm:"column:harmony_release" json:"harmony_release"`               // harmony repo address
}

func (AppCodeCheckConfig) TableName() string {
	return "app_code_check_config"
}

func NewAppCodeCheckConfig() *AppCodeCheckConfig {
	return &AppCodeCheckConfig{}
}

type AppCodeCheckConfig struct {
	Id                          int64     `gorm:"column:id;primary_key" json:"id"`                                             // 主键id
	AppId                       int64     `gorm:"column:app_id" json:"app_id"`                                                 // 内部的appId
	CreateTime                  time.Time `gorm:"column:create_time" json:"create_time"`                                       // 创建日期 type:*time.Time
	UpdateTime                  time.Time `gorm:"column:update_time" json:"update_time"`                                       // 更新日期 type:*time.Time
	RuleLevelQuestionsThreshold string    `gorm:"column:rule_level_questions_threshold" json:"rule_level_questions_threshold"` // 规则等级的问题数限制  p0/p1/p2 --> 数量
}

func (BitsAppBuildConfig) TableName() string {
	return "bits_app_build_config"
}

func NewBitsAppBuildConfig() *BitsAppBuildConfig {
	return &BitsAppBuildConfig{}
}

type BitsAppBuildConfig struct {
	Id         int64     `gorm:"column:id;primary_key" json:"id"`
	MetaAppId  int64     `gorm:"column:meta_app_id" json:"meta_app_id"` // 属于基座中哪个 App，不要用来查询，用 relation 表
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"` // 创建日期 type:*time.Time
	UpdateTime time.Time `gorm:"column:update_time" json:"update_time"` // 更新日期 type:*time.Time
	DeletedAt  time.Time `gorm:"column:deleted_at" json:"deleted_at"`   // 软删除标记字段 type:*time.Time
	FullConfig string    `gorm:"column:full_config" json:"full_config"` // 具体的配置
}

func (BitsAppBuildConfigAssociation) TableName() string {
	return "bits_app_build_config_association"
}

func NewBitsAppBuildConfigAssociation() *BitsAppBuildConfigAssociation {
	return &BitsAppBuildConfigAssociation{}
}

type BitsAppBuildConfigAssociation struct {
	CiType              int       `gorm:"column:ci_type" json:"ci_type"`                   // CI 类型,枚举: 1:使用 Jenkins 2:使用 VIPER 3:使用云构建
	AssociationType     int       `gorm:"column:association_type" json:"association_type"` // 关联方式,枚举: 1-归属App,2-关联App,3-研发任务
	Id                  int64     `gorm:"column:id;primary_key" json:"id"`
	BuildConfigId       int64     `gorm:"column:build_config_id" json:"build_config_id"`             // 编译配置 ID
	AssociationId       int64     `gorm:"column:association_id" json:"association_id"`               // 关联对象的 ID
	CreateTime          time.Time `gorm:"column:create_time" json:"create_time"`                     // 创建时间 type:*time.Time
	UpdateTime          time.Time `gorm:"column:update_time" json:"update_time"`                     // 更新时间 type:*time.Time
	DeletedAt           time.Time `gorm:"column:deleted_at" json:"deleted_at"`                       // 软删除标记字段 type:*time.Time
	TechnologyStack     string    `gorm:"column:technology_stack" json:"technology_stack"`           // 技术栈 枚举:Android,iOS
	BuildConfigScenario string    `gorm:"column:build_config_scenario" json:"build_config_scenario"` // 编译场景 枚举: CUSTOM/DEVELOP/BETA/NORMAL
	ConfigType          string    `gorm:"column:config_type" json:"config_type"`                     // 配置类型,枚举: NONE:普通配置,MAIN:提测配置,PREBUILD:预编译配置
}

func (CronJob) TableName() string {
	return "cron_job"
}

func NewCronJob() *CronJob {
	return &CronJob{}
}

type CronJob struct {
	Able     int8   `gorm:"column:able" json:"able"`       // 开关
	Deleted  int8   `gorm:"column:deleted" json:"deleted"` // 软删除
	Id       int64  `gorm:"column:id;primary_key" json:"id"`
	AppId    int64  `gorm:"column:app_id" json:"app_id"`       // bits_app_id
	NextTime int64  `gorm:"column:next_time" json:"next_time"` // 下一次执行的至少时间
	Crontab  string `gorm:"column:crontab" json:"crontab"`     // crontab语法
	Type     string `gorm:"column:type" json:"type"`           // 执行分发时必要的类型
	Name     string `gorm:"column:name" json:"name"`           // config name
	Data     string `gorm:"column:data" json:"data"`           // 执行时需要的数据,比如直接存一个json
}

func (PlatformHelpConfig) TableName() string {
	return "platform_help_config"
}

func NewPlatformHelpConfig() *PlatformHelpConfig {
	return &PlatformHelpConfig{}
}

type PlatformHelpConfig struct {
	IsDelete     int8      `gorm:"column:is_delete" json:"is_delete"`         // 是否删除
	Id           int64     `gorm:"column:id;primary_key" json:"id"`           // 主键
	UpdateTime   time.Time `gorm:"column:update_time" json:"update_time"`     // 更新
	CreateTime   time.Time `gorm:"column:create_time" json:"create_time"`     // 创建
	CategoryName string    `gorm:"column:category_name" json:"category_name"` // 分类
	FormName     string    `gorm:"column:form_name" json:"form_name"`         // 表单
	FieldName    string    `gorm:"column:field_name" json:"field_name"`       // 字段
	Config       string    `gorm:"column:config" json:"config"`               // 字段的配置
	Username     string    `gorm:"column:username" json:"username"`           // 最后更新的用户
}

func (DevConfigWorkflowRealtimePart) TableName() string {
	return "dev_config_workflow_realtime_part"
}

func NewDevConfigWorkflowRealtimePart() *DevConfigWorkflowRealtimePart {
	return &DevConfigWorkflowRealtimePart{}
}

type DevConfigWorkflowRealtimePart struct {
	IsDefault       int8      `gorm:"column:is_default" json:"is_default"`             // 是否为页面上默认选中
	Deleted         int8      `gorm:"column:deleted" json:"deleted"`                   // deleted
	Id              int64     `gorm:"column:id" json:"id"`                             // id
	CopyFromId      int64     `gorm:"column:copy_from_id" json:"copy_from_id"`         // 从哪一个模板复制
	SpaceId         int64     `gorm:"column:space_id" json:"space_id"`                 // 空间id
	Version         int64     `gorm:"column:version" json:"version"`                   // 防止写并发的 version
	CreatedAt       time.Time `gorm:"column:created_at" json:"created_at"`             // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at" json:"updated_at"`             // 更新时间
	Name            string    `gorm:"column:name" json:"name"`                         // name
	NameI18n        string    `gorm:"column:name_i18n" json:"name_i18n"`               // name i18n
	Description     string    `gorm:"column:description" json:"description"`           // 描述
	DescriptionI18n string    `gorm:"column:description_i18n" json:"description_i18n"` // description i18n
	Creator         string    `gorm:"column:creator" json:"creator"`                   // 创建人
	Updater         string    `gorm:"column:updater" json:"updater"`                   // 更新人
	WorkflowConfig  string    `gorm:"column:workflow_config" json:"workflow_config"`   // workflow json config
	IsOfficial      int8      `gorm:"column:is_official" json:"is_official"`           // 是否是官方模板
}

func (DevConfigWorkflowSnapshotPart) TableName() string {
	return "dev_config_workflow_snapshot_part"
}

func NewDevConfigWorkflowSnapshotPart() *DevConfigWorkflowSnapshotPart {
	return &DevConfigWorkflowSnapshotPart{}
}

type DevConfigWorkflowSnapshotPart struct {
	Id         int64  `gorm:"column:id" json:"id"`                   // id
	WorkflowId int64  `gorm:"column:workflow_id" json:"workflow_id"` // workflow_id
	Dag        string `gorm:"column:dag" json:"dag"`                 // dag配置
}

func (DevConfigWorkflowStage) TableName() string {
	return "dev_config_workflow_stage"
}

func NewDevConfigWorkflowStage() *DevConfigWorkflowStage {
	return &DevConfigWorkflowStage{}
}

type DevConfigWorkflowStage struct {
	Id          int64     `gorm:"column:id" json:"id"`                     // id
	WorkflowId  int64     `gorm:"column:workflow_id" json:"workflow_id"`   // workflow_id
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`     // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`     // 更新时间
	Name        string    `gorm:"column:name" json:"name"`                 // name
	NameI18n    string    `gorm:"column:name_i18n" json:"name_i18n"`       // name i18n
	StageConfig string    `gorm:"column:stage_config" json:"stage_config"` // 阶段配置
	Creator     string    `gorm:"column:creator" json:"creator"`           // 创建人
	Updater     string    `gorm:"column:updater" json:"updater"`           // 更新人
	FixedName   string    `gorm:"column:fixed_name" json:"fixed_name"`     // fixed name
}

func (DevConfigWorkflowNode) TableName() string {
	return "dev_config_workflow_node"
}

func NewDevConfigWorkflowNode() *DevConfigWorkflowNode {
	return &DevConfigWorkflowNode{}
}

type DevConfigWorkflowNode struct {
	NodeType        int8      `gorm:"column:node_type" json:"node_type"`               // 节点类型
	EditDisabled    int8      `gorm:"column:edit_disabled" json:"edit_disabled"`       // 是否可以编辑
	Id              int64     `gorm:"column:id" json:"id"`                             // id
	WorkflowId      int64     `gorm:"column:workflow_id" json:"workflow_id"`           // workflow_id
	CreatedAt       time.Time `gorm:"column:created_at" json:"created_at"`             // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at" json:"updated_at"`             // 更新时间
	Name            string    `gorm:"column:name" json:"name"`                         // name
	NameI18n        string    `gorm:"column:name_i18n" json:"name_i18n"`               // name i18n
	NodeConfig      string    `gorm:"column:node_config" json:"node_config"`           // 节点配置
	Creator         string    `gorm:"column:creator" json:"creator"`                   // 创建人
	Updater         string    `gorm:"column:updater" json:"updater"`                   // 更新人
	FixedName       string    `gorm:"column:fixed_name" json:"fixed_name"`             // 节点固定名字
	Description     string    `gorm:"column:description" json:"description"`           // node description
	DescriptionI18n string    `gorm:"column:description_i18n" json:"description_i18n"` // node description_i18n
}

func (DevConfigDependency) TableName() string {
	return "dev_config_dependency"
}

func NewDevConfigDependency() *DevConfigDependency {
	return &DevConfigDependency{}
}

type DevConfigDependency struct {
	Id               int64     `gorm:"column:id" json:"id"`                               // id
	SpaceId          int64     `gorm:"column:space_id" json:"space_id"`                   // 空间id
	Version          int64     `gorm:"column:version" json:"version"`                     // version
	CreatedAt        time.Time `gorm:"column:created_at" json:"created_at"`               // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at" json:"updated_at"`               // 更新时间
	DependencyConfig string    `gorm:"column:dependency_config" json:"dependency_config"` // 依赖部署配置
	AutoSyncRt       bool      `gorm:"column:auto_sync_rt" json:"auto_sync_rt"`           // 是否自动同步发布单
	Creator          string    `gorm:"column:creator" json:"creator"`                     // 创建人
	Updater          string    `gorm:"column:updater" json:"updater"`                     // 更新人
}

func (OnesiteInfoRecord) TableName() string {
	return "onesite_info_record"
}

func NewOnesiteInfoRecord() *OnesiteInfoRecord {
	return &OnesiteInfoRecord{}
}

type OnesiteInfoRecord struct {
	Id                 int64          `gorm:"column:id" json:"id"`                                     // id
	SpaceId            int64          `gorm:"column:space_id" json:"space_id"`                         // space_id
	OccurUnixTimestamp int64          `gorm:"column:occur_unix_timestamp" json:"occur_unix_timestamp"` // occur_unix_timestamp
	CreatedAt          time.Time      `gorm:"column:created_at" json:"created_at"`                     // 创建时间
	Parent             string         `gorm:"column:parent" json:"parent"`                             // parent 开发任务/发布单
	ParentPrimaryKey   string         `gorm:"column:parent_primary_key" json:"parent_primary_key"`     // parent_primary_key
	EventType          string         `gorm:"column:event_type" json:"event_type"`                     // event_type
	EventName          string         `gorm:"column:event_name" json:"event_name"`                     // event_name
	ContentType        string         `gorm:"column:content_type" json:"content_type"`                 // content_type
	Operator           string         `gorm:"column:operator" json:"operator"`                         // operator
	Content            string         `gorm:"column:content" json:"content"`                           // content
	ContentI18n        string         `gorm:"column:content_i18n" json:"content_i18n"`                 // content_i18n
	ParamValue         datatypes.JSON `gorm:"column:param_value" json:"param_value"`                   // param_value options:{"type":{"value":"datatypes.JSON", "import":"gorm.io/datatypes"}}
	OldValue           datatypes.JSON `gorm:"column:old_value" json:"old_value"`                       // old_value options:{"type":{"value":"datatypes.JSON", "import":"gorm.io/datatypes"}}
	NewValue           datatypes.JSON `gorm:"column:new_value" json:"new_value"`                       // new_value options:{"type":{"value":"datatypes.JSON", "import":"gorm.io/datatypes"}}
	LogId              string         `gorm:"column:log_id" json:"log_id"`                             // log_id
}

func (OnesiteSpaceNotificationConfig) TableName() string {
	return "onesite_space_notification_config"
}

func NewOnesiteSpaceNotificationConfig() *OnesiteSpaceNotificationConfig {
	return &OnesiteSpaceNotificationConfig{}
}

type OnesiteSpaceNotificationConfig struct {
	Open         bool      `gorm:"column:open" json:"open"`                   // open
	Id           int64     `gorm:"column:id" json:"id"`                       // id
	SpaceId      int64     `gorm:"column:space_id" json:"space_id"`           // space id
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`       // created time
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`       // updated time
	EventName    string    `gorm:"column:event_name" json:"event_name"`       // event name
	NotifyConfig string    `gorm:"column:notify_config" json:"notify_config"` // notify config
}

func (OnesiteSpaceNotificationLarkGroup) TableName() string {
	return "onesite_space_notification_lark_group"
}

func NewOnesiteSpaceNotificationLarkGroup() *OnesiteSpaceNotificationLarkGroup {
	return &OnesiteSpaceNotificationLarkGroup{}
}

type OnesiteSpaceNotificationLarkGroup struct {
	Id          int64  `gorm:"column:id" json:"id"`                     // id
	SpaceId     int64  `gorm:"column:space_id" json:"space_id"`         // space id
	GroupConfig string `gorm:"column:group_config" json:"group_config"` // group config
}
