package cache

import (
	"context"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/redis"
	"code.byted.org/kv/goredis"
	redis2 "code.byted.org/kv/redis-v6"
)

func TestWorkflowStageCache(t *testing.T) {
	ctx := context.Background()
	rdb := redis.NewLocal()
	cache := NewWorkflowStageCache(rdb)
	
	t.Run("没有数据时, 读取得到 Nil", func(t *testing.T) {
		none := cache.FindByWorkflowId(ctx, 23121).IsNil()
		assert.True(t, none)
	})
	
	t.Run("存入数据可以正确读取", func(t *testing.T) {
		values := []*entity.WorkflowStage{
			{
				WorkflowId: 122,
				Name:       "a",
			},
			{
				WorkflowId: 122,
				Name:       "b",
			},
			{
				WorkflowId: 122,
				Name:       "c",
			},
			{
				WorkflowId: 122,
				Name:       "d",
			},
		}
		cache.BatchCreate(ctx, 122, values, 3*time.Second)
		defer func() {
			cache.DeleteByWorkflowId(ctx, 122)
		}()
	
		actual := cache.FindByWorkflowId(ctx, 122).Must()
		assert.Equal(t, 4, len(actual))
		assert.Equal(t, "a", actual[0].Name)
		assert.Equal(t, "b", actual[1].Name)
		assert.Equal(t, "c", actual[2].Name)
		assert.Equal(t, "d", actual[3].Name)
	})
}

func TestWorkflowStageCache_BatchCreateAutoGen(t *testing.T) {
	// Verify the BatchCreate function of WorkflowStageCache.
	t.Run("testWorkflowStageCache_BatchCreate", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var pipelinedRet1Mock []redis2.Cmder
			var pipelinedRet2Mock error
			mockey.Mock((*goredis.Client).Pipelined).Return(pipelinedRet1Mock, pipelinedRet2Mock).Build()

			var clientMockPtrValueClient redis2.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.Mock((*goredis.Client).WithContext).Return(clientMock).Build()

			// prepare parameters
			var receiverPtrValue WorkflowStageCache
			receiver := &receiverPtrValue
			var receiverRdbPtrValueClient redis2.Client
			receiverRdbPtrValue := goredis.Client{Client: &receiverRdbPtrValueClient}
			receiver.rdb = &receiverRdbPtrValue
			ctx := context.Background()
			var workflowId int64
			var values []*entity.WorkflowStage
			var expiration time.Duration
			convey.So(func() { receiver.BatchCreate(ctx, workflowId, values, expiration) }, convey.ShouldNotPanic)
		})
	})

}

