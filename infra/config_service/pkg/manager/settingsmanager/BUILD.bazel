load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "settingsmanager",
    srcs = [
        "associated_project_settings.go",
        "manager.go",
        "mapper.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/config_service/pkg/manager/settingsmanager",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/app:app_go_proto",
        "//infra/config_service/kitex_gen/base",
        "//infra/config_service/kitex_gen/bytedance/bits/config_service",
        "//infra/config_service/kitex_gen/bytedance/bits/dev",
        "//infra/config_service/pkg/backends/appcenter",
        "//infra/config_service/pkg/dal/mysql/entity",
        "//infra/config_service/pkg/dal/mysql/repository",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "settingsmanager_test",
    srcs = ["associated_project_settings_test.go"],
    embed = [":settingsmanager"],
    deps = [
        "//infra/config_service/kitex_gen/bytedance/bits/config_service",
        "//infra/config_service/pkg/dal/mysql/entity",
        "//infra/config_service/pkg/dal/mysql/repository",
        "//libs/connections/sqlite",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_lang_gg//gptr",
    ],
)
