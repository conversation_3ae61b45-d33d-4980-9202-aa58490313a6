package authdomain

import (
	"context"
	"testing"
	"time"

	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"github.com/stretchr/testify/assert"
)

func TestQueryCodebaseToken(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.UserToken))
	userTokenRepository := repository.NewUserTokenRepository(db)
	domain := NewAuthDomain(rdb, db, nil, nil, nil)

	t.Run("当参数是空的时候,返回error", func(t *testing.T) {
		req := &meta.QueryCodebaseTokenRequest{
			Username: "",
		}

		err := domain.QueryCodebaseToken(ctx, req).Err()

		assert.ErrorIs(t, err, bits_err.COMMON.ErrInvalidInput)
	})

	t.Run("当从 db 中查询不到对应用户 token 的时候返回 error", func(t *testing.T) {
		req := &meta.QueryCodebaseTokenRequest{
			Username: "not-exist",
		}

		err := domain.QueryCodebaseToken(ctx, req).Err()

		assert.ErrorIs(t, err, bits_err.META.ErrUserTokenNotFound)
	})

	t.Run("当从 db 中查询到对应用户 token 时得到对应的数据", func(t *testing.T) {
		userToken := entity.NewUserToken("abc", entity.UserTokenTypeCodebase, "123", time.Now().Add(1*time.Hour))
		id := userTokenRepository.Create(ctx, userToken).Must()
		defer func() { _ = userTokenRepository.DeleteById(ctx, id) }()

		req := &meta.QueryCodebaseTokenRequest{
			Username: "abc",
		}
		resp := domain.QueryCodebaseToken(ctx, req).Must()
		assert.Equal(t, "123", resp.Token)
	})

	t.Run("如果 token 过期了, 那么不返回", func(t *testing.T) {
		userToken := entity.NewUserToken("abc", entity.UserTokenTypeCodebase, "123", time.Now().Add(-1*time.Hour))
		id := userTokenRepository.Create(ctx, userToken).Must()
		defer func() { _ = userTokenRepository.DeleteById(ctx, id) }()

		req := &meta.QueryCodebaseTokenRequest{
			Username: "abc",
		}
		resp := domain.QueryCodebaseToken(ctx, req).Must()
		assert.Equal(t, "", resp.Token)
		assert.Equal(t, int64(0), resp.ExpiredAt)
	})
}
