package entity

import "time"

type FeishuUser struct {
	Id               int64      `gorm:"column:id;primary_key"` // 主键
	CreateTime       *time.Time `gorm:"column:create_time"`    // 创建时间
	UpdateTime       *time.Time `gorm:"column:update_time"`    // 更新时间
	UserId           string     `gorm:"column:user_id"`        // 用户id
	Name             string     `gorm:"column:name"`           // 用户名(中文)
	EnName           string     `gorm:"column:en_name"`
	Email            string     `gorm:"column:email"`               // 用户邮箱
	AvatarUrl        string     `gorm:"column:avatar_url"`          // 用户头像地址
	OptimusBotOpenId string     `gorm:"column:optimus_bot_open_id"` // 相对于optimus平台助手open id
	EmployeeId       int64      `gorm:"column:employee_id"`
}

func (FeishuUser) TableName() string {
	return "optimus_user_feishu_user"
}

func NewFeishuUser(userId, name, enName, email, avatarUrl, optimusBotOpenId string, employeeId int64) *FeishuUser {
	now := time.Now()

	return &FeishuUser{
		CreateTime:       &now,
		UpdateTime:       &now,
		UserId:           userId,
		Name:             name,
		EnName:           enName,
		Email:            email,
		AvatarUrl:        avatarUrl,
		OptimusBotOpenId: optimusBotOpenId,
		EmployeeId:       employeeId,
	}
}
