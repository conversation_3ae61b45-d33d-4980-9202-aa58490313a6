/*
*
小程序需要的相关鉴权能力
*/
package handlers

import (
	"context"
	"errors"
	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/service/lark/mini_app"
)

func GetUserInfo(ctx context.Context, req *meta.GetMiniAppUserInfoQuery) (*meta.GetMiniAppUserInfoResponse, error) {
	res, err := mini_app.GetUserSessionKey(ctx, req.Code, req.Bot)

	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, errors.New("failed to get user session")
	}

	user, err := mini_app.GetUserInfoByOpenID(ctx, res.OpenID, req.Bot)

	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("failed to get user info")
	}

	return &meta.GetMiniAppUserInfoResponse{
		AvatarURL:  user.Avatar,
		Email:      user.Email,
		Name:       user.Name,
		OpenID:     user.OpenID,
		UserID:     user.UserID,
		SessionKey: res.SessionKey,
		TenantKey:  res.TenantKey,
		ExpireTime: res.ExpiresIn,
	}, nil
}
