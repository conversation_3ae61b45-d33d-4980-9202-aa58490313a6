package entity

import (
	"time"

	"gorm.io/gorm/schema"
)

type FaultRecoveryConf struct {
	Id             int64     `gorm:"column:id"` // primary key
	CreatedAt      time.Time `gorm:"column:created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at"`
	MaxRetryTimes  int       `gorm:"column:max_retry_times"`  // 重试次数,默认为1
	MaxDurationMs  int       `gorm:"column:max_duration_ms"`  // 重试间隔(单位,毫秒)
	FirstIntervalS int       `gorm:"column:first_interval_s"` // 首次重试,延迟时间(单位,秒)
	DownstreamPSM  string    `gorm:"downstream_psm"`          // 下游系统的PSM
	DownstreamApi  string    `gorm:"downstream_api"`          // 下游系统的接口
}

var _ schema.Tabler = &FaultRecoveryConf{}

func (f *FaultRecoveryConf) TableName() string {
	return "fault_recovery_conf"
}

func (f *FaultRecoveryConf) GetMaxDuration() time.Duration {
	if f.MaxDurationMs <= 0 {
		return 1000 * time.Millisecond
	}
	return time.Duration(f.MaxDurationMs) * time.Millisecond
}

func (f *FaultRecoveryConf) GetFirstInterval() time.Duration {
	if f.FirstIntervalS <= 0 {
		return 10 * time.Second
	}
	return time.Duration(f.FirstIntervalS) * time.Second
}
