package cache

import (
	"context"
	"time"

	"code.byted.org/gopkg/localcache"
	"code.byted.org/gopkg/singleflight"
)

type LocalSimpleCache struct {
	cache localcache.SimpleCache
	g     singleflight.Group
}

func NewLocalSimpleCache(maxSize int, expiration time.Duration) *LocalSimpleCache {
	return &LocalSimpleCache{
		cache: localcache.MustNewSimpleCache(maxSize, expiration, localcache.LRU),
		g:     singleflight.Group{},
	}
}

func (c *LocalSimpleCache) Get(ctx context.Context, key string, onMiss func() (interface{}, error)) (interface{}, error) {
	val, err := c.cache.Get(ctx, key)
	if val != nil { // 命中缓存
		return val, nil
	}
	if onMiss == nil {
		return nil, nil
	}
	val, err, _ = c.g.Do(key, func() (interface{}, error) {
		val, err = onMiss()
		if err != nil { // 回源也没找到
			return nil, err
		}
		if val != nil { // 更新缓存
			_ = c.cache.Set(ctx, key, val)
		}
		return val, err
	})
	return val, err
}

func (c *LocalSimpleCache) MGet(ctx context.Context, keys []string, onMiss func(missKeys []string) (map[string]interface{}, error)) (map[string]interface{}, error) {
	result, missKeys := c.cache.MGet(ctx, keys)
	if len(missKeys) == 0 || onMiss == nil { // 全部命中了
		return result, nil
	}
	missResult, err := onMiss(missKeys) // 回源查找
	if err != nil {
		return result, err
	}
	if missResult != nil {
		_ = c.cache.MSet(ctx, missResult) // 更新缓存
		if result == nil {
			result = make(map[string]interface{})
		}
		for k, v := range missResult { // 合并结果
			result[k] = v
		}
	}
	return result, err
}
