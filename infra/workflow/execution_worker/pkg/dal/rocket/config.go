/**
 * @Date: 2023/5/24
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package rocket

import (
	"fmt"

	"code.byted.org/gopkg/env"
)

type ConsumerConfig struct {
	Group   string `toml:"group"`
	Cluster string `toml:"cluster"`
	Topic   string `toml:"topic"`
}

type ConsumersConfig struct {
	DevTaskV1Action         *ConsumerConfig `toml:"dev_task_v1_action"`
	DevTaskAdvancedAction   *ConsumerConfig `toml:"dev_task_advanced_action"`
	DevTaskRecreationAction *ConsumerConfig `toml:"dev_task_recreation_action"`
	ClientTtpAction         *ConsumerConfig `toml:"client_ttp_action"`
	ReleaseTicketAction     *ConsumerConfig `toml:"release_ticket_action"`
	PingAction              *ConsumerConfig `toml:"ping_action"`
	ReleaseTicketPingAction *ConsumerConfig `toml:"release_ticket_ping_action"`
}

func (c *ConsumersConfig) GetPingAction() *ConsumerConfig { // 根据集群信息判断使用那个 ping 队列
	if env.Cluster() == "default" {
		return c.PingAction
	} else if env.Cluster() == "release_ticket" {
		return c.ReleaseTicketPingAction
	} else {
		panic(fmt.Errorf("没有识别到集群信息, 请联系 <EMAIL> 咨询服务部署相关信息"))
	}
}

type ProducerConfig struct {
	Cluster string `toml:"cluster"`
	Topic   string `toml:"topic"`
}
