package main

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/kitex_gen/bits/workflow/execution_engine"
	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/pkg/backends/track"
	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/pkg/byteflow_app"
	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/pkg/dal/messagequeue/rocket"
	"code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/pkg/domain"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/kv/goredis"
	"gorm.io/gorm"
)

type EngineServiceImpl struct {
	execution     *domain.ExecutionDomain
	executionTask *domain.ExecutionTaskDomain
}

func NewEngineService(rdb *goredis.Client, db *gorm.DB, byteflowApp *byteflow_app.Wrapper, producers *rocket.Producers, eventcenter *events.ProducerWrapper) execution_engine.EngineService {
	trackApi := track.New()

	return &EngineServiceImpl{
		execution:     domain.NewExecutionDomain(rdb, db, byteflowApp, producers.DevTaskV1, producers.DevTaskAdvanced, producers.DevTaskRecreation, producers.ReleaseTicket, producers.Ping, eventcenter, trackApi),
		executionTask: domain.NewExecutionTaskDomain(rdb, db, byteflowApp, eventcenter, producers.Ping),
	}
}

func (impl *EngineServiceImpl) TriggerExecution(ctx context.Context, req *execution_engine.TriggerExecutionRequest) (*execution_engine.TriggerExecutionResponse, error) {
	return impl.execution.TriggerExecution(ctx, req).Get()
}

func (impl *EngineServiceImpl) CancelExecution(ctx context.Context, req *execution_engine.CancelExecutionRequest) (*execution_engine.CancelExecutionResponse, error) {
	return impl.execution.CancelExecution(ctx, req).Get()
}

func (impl *EngineServiceImpl) ResetExecution(ctx context.Context, req *execution_engine.ResetExecutionRequest) (*execution_engine.ResetExecutionResponse, error) {
	return impl.execution.ResetExecution(ctx, req).Get()
}

func (impl *EngineServiceImpl) RetryTask(ctx context.Context, req *execution_engine.RetryTaskRequest) (*execution_engine.RetryTaskResponse, error) {
	return impl.executionTask.RetryTask(ctx, req).Get()
}

func (impl *EngineServiceImpl) GetExecutionTasks(ctx context.Context, req *execution_engine.GetExecutionTasksRequest) (*execution_engine.GetExecutionTasksResponse, error) {
	return impl.execution.GetExecutionTasks(ctx, req).Get()
}

func (impl *EngineServiceImpl) GetExecutionTasksByUniques(ctx context.Context, req *execution_engine.GetExecutionTasksByUniquesRequest) (*execution_engine.GetExecutionTasksByUniquesResponse, error) {
	return impl.execution.GetExecutionTasksByUniques(ctx, req).Get()
}

func (impl *EngineServiceImpl) TransitionTaskState(ctx context.Context, req *execution_engine.TransitionTaskStateRequest) (*execution_engine.TransitionTaskStateResponse, error) {
	return impl.executionTask.TransitionTaskState(ctx, req).Get()
}

func (impl *EngineServiceImpl) ActivateExecution(ctx context.Context, req *execution_engine.ActivateExecutionRequest) (*base.EmptyResponse, error) {
	return impl.execution.ActivateExecution(ctx, req).Get()
}
func (impl *EngineServiceImpl) ResetConcurrencyCounter(ctx context.Context, req *execution_engine.ResetConcurrencyCounterRequest) (*execution_engine.ResetConcurrencyCounterResponse, error) {
	return impl.execution.ResetConcurrencyCounter(ctx, req).Get()
}

func (impl *EngineServiceImpl) RescheduleExecution(ctx context.Context, req *execution_engine.RescheduleExecutionRequest) (*execution_engine.RescheduleExecutionResponse, error) {
	return impl.execution.RescheduleExecution(ctx, req).Get()
}

func (impl *EngineServiceImpl) FakeRetryTask(ctx context.Context, req *execution_engine.FakeRetryTaskRequest) (*execution_engine.FakeRetryTaskResponse, error) {
	return impl.executionTask.FakeRetryTask(ctx, req).Get()
}

func (impl *EngineServiceImpl) GetExecutionAndTasks(ctx context.Context, req *execution_engine.GetExecutionAndTasksRequest) (*execution_engine.GetExecutionTasksResponse, error) {
	return impl.executionTask.GetExecutionAndTasks(ctx, req).Get()
}

func (impl *EngineServiceImpl) GetTask(ctx context.Context, req *execution_engine.GetTaskRequest) (*execution_engine.GetTaskResponse, error) {
	return impl.executionTask.GetTask(ctx, req).Get()
}

func (impl *EngineServiceImpl) TryRecoveryExecution(ctx context.Context, req *execution_engine.TryRecoveryExecutionRequest) (*base.EmptyResponse, error) {
	return impl.execution.TryRecoveryExecution(ctx, req).Get()
}
