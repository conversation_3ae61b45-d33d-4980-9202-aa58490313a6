load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "external_resp.go",
        "http_resp.go",
        "lark.go",
        "params.go",
        "urls.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/notification/pkg/model",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/common_lib/model",
        "//libs/common_lib/model/lark_model",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_hallokael_httpr//:httpR",
    ],
)
