package feishumodel

type Note struct {
	Tag      string        `json:"tag"`
	Elements []interface{} `json:"elements"`
}

type Field struct {
	IsShort bool `json:"is_short"`
	Text    Text `json:"text"`
}

type ConfirmBlock struct {
	Title Text `json:"title"`
	Text  Text `json:"text"`
}

type Option struct {
	Text     Text   `json:"text"`
	Value    string `json:"value"`
	Url      string `json:"url"`
	MultiUrl Url    `json:"multi_url"`
}

type Url struct {
	URL        string `json:"url"`
	AndroidUrl string `json:"android_url"`
	IosUrl     string `json:"ios_url"`
	PCUrl      string `json:"pc_url"`
}

type CardLink struct {
	Url
}

type Image struct {
	Tag          string `json:"tag"`
	ImgKey       string `json:"img_key"`
	Alt          Text   `json:"alt"`
	Title        Text   `json:"title"`
	CustomWidth  int    `json:"custom_width"`
	CompactWidth bool   `json:"compact_width"`
	Mode         string `json:"mode"`
	Preview      bool   `json:"preview"`
}

func NewImage(imgKey string, title ...string) *Image {
	var realTitle Text
	if len(title) > 0 {
		realTitle = *NewMDText(title[0])
	}
	return &Image{
		Tag:          "img",
		ImgKey:       imgKey,
		Alt:          *NewMDText("this is an image"),
		Title:        realTitle,
		CustomWidth:  0,
		CompactWidth: false,
		Mode:         "",
		Preview:      false,
	}
}

func NewNote(text string) *Note {
	return &Note{
		Tag: "note",
		Elements: []interface{}{
			NewMDText(text),
		},
	}
}
