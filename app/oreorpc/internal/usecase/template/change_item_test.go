package template

import (
	"context"
	"fmt"
	"time"

	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"

	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/model/cdmodel"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/query/cd"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase/repo/tcc"
	"code.byted.org/devinfra/hagrid/app/oreorpc/pkg/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/templatepb"
	"code.byted.org/iesarch/paas_sdk/goofy_deploy"
)

func (t *TemplateTestSuite) Test_genDefaultTemplateKey() {
	res := genDefaultTemplateKey(1, 2)
	t.Equal("2-1", res)
}

func (t *TemplateTestSuite) Test_genProjTemplateKey() {
	res := genProjTemplateKey(1, 2, "3")
	t.Equal("1-2-3", res)
}

func (t *TemplateTestSuite) Test_genByteTreeKey() {
	res := genByteTreeKey(1, 2, "3")
	t.Equal("1-2-3", res)
}

func (t *TemplateTestSuite) Test_genBriefTemplateFromDB() {
	timeCache := time.Now()
	timeStr := utils.TimeToString(timeCache)
	res := genBriefTemplateFromDB(&cdmodel.OreoPplTemplate{
		TemplateID: 123,
		UpdatedBy:  "lusong.chn",
		UpdatedAt:  timeCache,
	})
	t.Equal(int64(123), res.TemplateId)
	t.Equal("lusong.chn", res.UpdatedBy)
	t.Equal(timeStr, res.UpdatedAt)

	res = genBriefTemplateFromDB(nil)
	t.Nil(res)
}

func (t *TemplateTestSuite) Test_ChangeItemParamCheck() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	svc := t.SetupUC(ctx, ctrl)

	t.Run("success", func() {
		err := svc.ChangeItemParamCheck(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			SpaceId: 123,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_TCE,
				},
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_FAAS,
				},
			},
		})
		t.NoError(err)
	})

	t.Run("error - space id is zero", func() {
		err := svc.ChangeItemParamCheck(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			SpaceId: 0,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_TCE,
				},
			},
		})
		t.Error(err)
		t.ErrorContains(err, "space id is required")
	})

	t.Run("error - invalid control plane", func() {
		err := svc.ChangeItemParamCheck(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			SpaceId: 123,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_TCE,
				},
			},
		})
		t.Error(err)
		t.ErrorContains(err, "invalid control plane or project type")
	})

	t.Run("error - invalid project type", func() {
		err := svc.ChangeItemParamCheck(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			SpaceId: 123,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_MONOREPO,
				},
			},
		})
		t.Error(err)
		t.ErrorContains(err, "invalid control plane or project type")
	})

	t.Run("error - multiple invalid items", func() {
		err := svc.ChangeItemParamCheck(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			SpaceId: 123,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
				},
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:  sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
				},
			},
		})
		t.Error(err)
		t.ErrorContains(err, "invalid control plane or project type")
	})
}

func (t *TemplateTestSuite) Test_GetTemplateIDByChangeItems() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	list := make([]*cdmodel.OreoPplTemplate, 0)
	mainConfig := &cdmodel.OreoPplTemplate{
		ID:              1,
		WorkflowID:      639608832,
		NodeID:          679442432,
		WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
		ProjectType:     int32(sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED),
		ProjectUniqueID: "",
		ProjectName:     "",
		Region:          int32(sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED),
		Scene:           0,
		ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_UNSPECIFIED),
		TemplateID:      1200970,
		IsDefault:       1,
		IsRollback:      0,
		Creator:         "lusong.chn",
	}
	list = append(list, mainConfig)

	ProjectTypeList := []sharedpb.ProjectType{1, 2, 3, 4, 5}
	ControlPlaneList := []sharedpb.ControlPlane{1, 2}
	index := 2

	for _, projectType := range ProjectTypeList {
		for _, region := range ControlPlaneList {
			list = append(list, &cdmodel.OreoPplTemplate{
				ID:              uint64(index),
				WorkflowID:      639608832,
				NodeID:          679442432,
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				ProjectType:     int32(projectType),
				ProjectUniqueID: "",
				ProjectName:     "",
				Region:          int32(region),
				Scene:           0,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
				TemplateID:      int64(projectType) * int64(region),
				IsDefault:       1,
				IsRollback:      0,
				Creator:         "lusong.chn",
			})
			index++
		}
	}

	// project config
	list = append(list, &cdmodel.OreoPplTemplate{
		ID:              uint64(index),
		WorkflowID:      639608832,
		NodeID:          679442432,
		WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
		ProjectType:     int32(sharedpb.ProjectType_PROJECT_TYPE_TCE),
		ProjectUniqueID: "bc.song.mono",
		ProjectName:     "bc.song.mono",
		Region:          int32(sharedpb.ControlPlane_CONTROL_PLANE_CN),
		Scene:           0,
		ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
		TemplateID:      99999,
		IsDefault:       0,
		IsRollback:      0,
		Creator:         "lusong.chn",
	})
	index++

	// rollback config
	list = append(list, []*cdmodel.OreoPplTemplate{
		{
			ID:              uint64(index),
			WorkflowID:      639608832,
			NodeID:          679442432,
			WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
			ProjectType:     int32(sharedpb.ProjectType_PROJECT_TYPE_TCE),
			ProjectUniqueID: "",
			ProjectName:     "",
			Region:          int32(sharedpb.ControlPlane_CONTROL_PLANE_CN),
			Scene:           0,
			ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
			TemplateID:      88888,
			IsDefault:       1,
			IsRollback:      1,
			Creator:         "lusong.chn",
			UpdatedBy:       "tester",
			UpdatedAt:       time.Now(),
		},
		{
			ID:              uint64(index + 1),
			WorkflowID:      639608832,
			NodeID:          679442432,
			WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
			ProjectType:     int32(sharedpb.ProjectType_PROJECT_TYPE_TCE),
			ProjectUniqueID: "",
			ProjectName:     "",
			Region:          int32(sharedpb.ControlPlane_CONTROL_PLANE_I18N),
			Scene:           0,
			ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
			TemplateID:      88889,
			IsDefault:       1,
			IsRollback:      1,
			Creator:         "lusong.chn",
			UpdatedBy:       "tester",
			UpdatedAt:       time.Now(),
		},
	}...)

	err := cd.Q.OreoPplTemplate.WithContext(ctx).Create(list...)
	t.NoError(err)

	defer Mock(GetMethod(svc.TCCRepo, "SpaceAllowCustomRollback")).Return(false, nil).Build().UnPatch()
	defer Mock(GetMethod(svc.PresetTemplateUC, "GetSpaceInitTemplateByNodeMeta")).Return(&usecase.SpaceInitConfig{}, nil).Build().UnPatch()

	t.Run("success", func() {
		defer Mock(GetMethod(svc, "GetTTPMainTemplate")).Return(&cdmodel.OreoPplTemplate{
			TemplateID: 999,
		}, nil).Build().UnPatch()

		t.Run("forward", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCDInitPplConfig")).Return(&tcc.InitTemplateItem{
				TemplateID: 123,
				Name:       "mock template",
				NameI18N:   "mock template CN",
			}, nil).Build().UnPatch()
			res, err := svc.GetTemplateIDByChangeItems(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
				WorkflowId:   639608832,
				NodeId:       679442432,
				WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
				SpaceId:      123,
				ChangeItems: []*templatepb.BriefChangeItem{
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
						ProjectUniqueId: "123",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
						ProjectUniqueId: "123",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
						ProjectUniqueId: "123",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
						ProjectUniqueId: "123",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_HYBRID,
						ProjectUniqueId: "123",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
						ProjectUniqueId: "bc.song.mono",
					},
					{
						ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
						ProjectUniqueId: "bc.song.mono2",
					},
				},
			})
			t.NoError(err)
			t.NotNil(res)
			t.Equal(1, len(res.MainTemplates))

			ids := make([]int64, 0)
			for _, tt := range res.ChangeItemTemplates {
				ids = append(ids, tt.TemplateId)
			}
			t.Equal([]int64{1, 2, 3, 4, 5, 99999, 1}, ids)
		})

		t.Run("rollback", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCDInitPplConfig")).Return(&tcc.InitTemplateItem{
				TemplateID: 123,
				Name:       "mock template",
				NameI18N:   "mock template CN",
			}, nil).Build().UnPatch()

			t.Run("non-ttp", func() {
				res, err := svc.GetTemplateIDByChangeItems(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
					WorkflowId:   639608832,
					NodeId:       679442432,
					WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
					SpaceId:      123,
					ChangeItems: []*templatepb.BriefChangeItem{
						{
							ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
							ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
							ProjectUniqueId: "bc.song.mono",
						},
						{
							ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
							ProjectUniqueId: "bc.song.mono",
						},
					},
					IsRollback: true,
				})
				t.NoError(err)
				t.NotNil(res)
				t.Equal(2, len(res.MainTemplates))

				ids := make([]int64, 0)
				for _, tt := range res.ChangeItemTemplates {
					ids = append(ids, tt.TemplateId)
					t.Equal("", tt.UpdatedBy)
				}
				t.Equal([]int64{123, 123}, ids)
			})
			t.Run("ttp", func() {
				defer Mock(GetMethod(svc, "GetRollbackMainTemplates")).Return([]*cdmodel.OreoPplTemplate{
					{
						TemplateID: 999,
					},
				}, nil).Build().UnPatch()

				res, err := svc.GetTemplateIDByChangeItems(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
					WorkflowId:   639608832,
					NodeId:       679442432,
					WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
					SpaceId:      123,
					ChangeItems: []*templatepb.BriefChangeItem{
						{
							ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_TTP,
							ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
							ProjectUniqueId: "bc.song.mono",
						},
					},
					IsRollback: true,
				})
				t.NoError(err)
				t.NotNil(res)
				t.Equal(1, len(res.MainTemplates))
			})
		})
	})

	t.Run("failed", func() {
		defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCDInitPplConfig")).
			Return(nil, fmt.Errorf("mock err")).Build().UnPatch()

		_, err := svc.GetTemplateIDByChangeItems(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			WorkflowId:   639608832,
			NodeId:       679442432,
			SpaceId:      123,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_MONOREPO,
					ProjectUniqueId: "bc.song.mono",
				},
			},
			IsRollback: true,
		})
		t.Error(err)
	})

	t.Run("space error", func() {
		_, err := svc.GetTemplateIDByChangeItems(ctx, &templatepb.GetTemplateIDByChangeItemsReq{
			WorkflowId:   639608832,
			NodeId:       679442432,
			SpaceId:      0,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_MONOREPO,
					ProjectUniqueId: "bc.song.mono",
				},
			},
			IsRollback: true,
		})
		t.ErrorContains(err, "space id is required")
	})
}

func (t *TemplateTestSuite) Test_GetTTPMainTemplate() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	t.Run("success", func() {
		t.Run("CI", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCIInitPplConfig")).Return(&tcc.InitTemplateItem{
				TemplateID: 123,
				Name:       "ttp 模版",
				NameI18N:   "ttp template",
			}, nil).Build().UnPatch()

			res, err := svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_DEVELOPMENT_TASK, false)
			t.NoError(err)
			t.NotNil(res)
			t.Equal(int64(123), res.TemplateID)
		})

		t.Run("CD", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCDInitPplConfig")).To(func(ctx context.Context, params *tcc.GetCDTemplateParams) (*tcc.InitTemplateItem, error) {
				if params.Scene == tcc.CDRollback {
					return &tcc.InitTemplateItem{
						TemplateID: 666,
						Name:       "cd forward",
						NameI18N:   "cd en forward",
					}, nil
				}
				return &tcc.InitTemplateItem{
					TemplateID: 456,
					Name:       "cd forward",
					NameI18N:   "cd en forward",
				}, nil
			}).Build().UnPatch()

			res, err := svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET, false)
			t.NoError(err)
			t.NotNil(res)
			t.Equal(int64(456), res.TemplateID)

			res, err = svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET, true)
			t.NoError(err)
			t.NotNil(res)
			t.Equal(int64(666), res.TemplateID)
		})
	})

	t.Run("failed", func() {
		t.Run("CI", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCIInitPplConfig")).
				Return(nil, fmt.Errorf("mock err")).Build().UnPatch()
			res, err := svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_DEVELOPMENT_TASK, false)
			t.Error(err)
			t.Nil(res)
		})
		t.Run("CD", func() {
			defer Mock(GetMethod(svc.TCCRepo, "SafelyGetCDInitPplConfig")).
				Return(nil, fmt.Errorf("mock err")).Build().UnPatch()
			res, err := svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET, false)
			t.Error(err)
			t.Nil(res)

			res, err = svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET, true)
			t.Error(err)
			t.Nil(res)
		})
		t.Run("invalid type", func() {
			res, err := svc.GetTTPMainTemplate(ctx, workflowpb.WorkflowType_WORKFLOW_TYPE_UNSPECIFIED, false)
			t.Error(err)
			t.Nil(res)
		})
	})
}

func (t *TemplateTestSuite) Test_GetDefaultTemplateFromTCC() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	mockReq := &templatepb.GetTemplateIDByChangeItemsReq{
		WorkflowId:   1,
		NodeId:       1,
		WorkflowType: 1,
		ChangeItems: []*templatepb.BriefChangeItem{
			{
				ControlPlane:    1,
				ProjectType:     1,
				ProjectUniqueId: "bc.song.demo",
			},
			{
				ControlPlane:    2,
				ProjectType:     2,
				ProjectUniqueId: "bc.song.faas",
			},
		},
	}
	mockRes := make([]*templatepb.BriefTemplateItem, 2)

	mockDefaultMap := map[string]*cdmodel.OreoPplTemplate{
		"1-1": {
			ID:         1,
			TemplateID: 123,
			UpdatedAt:  time.Time{},
			UpdatedBy:  "lusong.chn",
		},
		"2-2": {
			ID:         1,
			TemplateID: 345,
			UpdatedAt:  time.Time{},
			UpdatedBy:  "lusong.chn",
		},
	}

	err := svc.GetDefaultTemplate(ctx, mockReq, mockRes, mockDefaultMap)
	t.NoError(err)
	t.Len(mockRes, 2)
	t.Equal(int64(123), mockRes[0].TemplateId)
	t.Equal(int64(345), mockRes[1].TemplateId)

	// failed case
	mockReq.ChangeItems = append(mockReq.ChangeItems, &templatepb.BriefChangeItem{
		ControlPlane:    1,
		ProjectType:     4,
		ProjectUniqueId: "404",
	})
	mockRes = append(mockRes, nil)
	err = svc.GetDefaultTemplate(ctx, mockReq, mockRes, mockDefaultMap)
	t.ErrorContains(err, "forward template not found")
}

func (t *TemplateTestSuite) Test_GetGoofyParentNodeIDs() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	t.Run("success", func() {
		defer Mock(GetMethod(svc.GoofySDK, "GetAppByteTreeListByAppIds")).Return([]goofy_deploy.GetAppByteTreeItem{
			{
				AppID:      123,
				ByteTreeId: 123000,
			},
			{
				AppID:      456,
				ByteTreeId: 456000,
			},
		}, nil).Build().UnPatch()
		defer Mock(GetMethod(svc.ByteTreeRepo, "GetIndexNodesByIdListWithCache")).Return([]*usecase.BriefBytetreeNodeInfo{
			{
				ID:              123000,
				AllParentIDList: []uint64{0, 1, 2, 3},
			},
			{
				ID:              456000,
				AllParentIDList: []uint64{0, 4, 5, 6},
			},
		}, nil).Build().UnPatch()

		res, err := svc.GetGoofyParentNodeIDs(ctx, []uint64{123, 456}, "lusong.chn")
		t.NoError(err)
		t.Len(res, 2)
		t.Equal([]uint64{0, 1, 2, 3, 123000}, res[123])
		t.Equal([]uint64{0, 4, 5, 6, 456000}, res[456])
	})

	t.Run("failed", func() {
		m1 := Mock(GetMethod(svc.GoofySDK, "GetAppByteTreeListByAppIds")).Return(nil, fmt.Errorf("mock err")).Build()

		_, err := svc.GetGoofyParentNodeIDs(ctx, []uint64{123, 456}, "lusong.chn")
		t.ErrorContains(err, "mock err")

		m1.UnPatch()

		defer Mock(GetMethod(svc.GoofySDK, "GetAppByteTreeListByAppIds")).Return([]goofy_deploy.GetAppByteTreeItem{
			{
				AppID:      123,
				ByteTreeId: 123000,
			},
		}, nil).Build().UnPatch()
		defer Mock(GetMethod(svc.ByteTreeRepo, "GetIndexNodesByIdListWithCache")).Return(nil, fmt.Errorf("mock err")).Build().UnPatch()

		_, err = svc.GetGoofyParentNodeIDs(ctx, []uint64{123, 456}, "lusong.chn")
		t.ErrorContains(err, "mock err")
	})
}

func (t *TemplateTestSuite) Test_GetClosetByteTreeTemplate() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	t.Run("success", func() {
		mockByteTreeMap := map[string]*cdmodel.OreoPplTemplate{
			"1-1-123000": {
				TemplateID: 666,
				UpdatedAt:  time.Time{},
				UpdatedBy:  "zhangsan",
			},
			"2-1-3": {
				TemplateID: 777,
				UpdatedAt:  time.Time{},
				UpdatedBy:  "lisi",
			},
			"2-1-2": {
				TemplateID: 888,
				UpdatedAt:  time.Time{},
				UpdatedBy:  "wangwu",
			},
		}

		res := svc.GetClosetByteTreeTemplate([]uint64{0, 1, 2, 3, 123000}, mockByteTreeMap, sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ProjectType_PROJECT_TYPE_TCE)

		t.NotNil(res)
		t.Equal(int64(777), res.TemplateId)
		t.Equal("lisi", res.UpdatedBy)
	})
}

func (t *TemplateTestSuite) Test_GetByteTreeTemplate() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	svc := t.SetupUC(ctx, ctrl)

	t.Run("success, goofy", func() {
		defer Mock(GetMethod(svc, "GetGoofyParentNodeIDs")).Return(map[uint64][]uint64{
			123: {0, 1, 2, 3, 123000},
			456: {0, 4, 5, 6, 456000},
		}, nil).Build().UnPatch()

		mockReq := &templatepb.GetTemplateIDByChangeItemsReq{
			WorkflowId:   1,
			NodeId:       1,
			WorkflowType: 1,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane:    1,
					ProjectType:     4,
					ProjectUniqueId: "123",
				},
				{
					ControlPlane:    2,
					ProjectType:     4,
					ProjectUniqueId: "456",
				},
				{
					ControlPlane:    2,
					ProjectType:     4,
					ProjectUniqueId: "aaa",
				},
			},
		}
		mockRes := make([]*templatepb.BriefTemplateItem, 3)
		mockByteTreeMap := map[string]*cdmodel.OreoPplTemplate{
			"1-4-123000": {
				TemplateID:  666,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "zhangsan",
			},
			"2-4-3": {
				TemplateID:  777,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "lisi",
			},
			"2-4-2": {
				TemplateID:  888,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "wangwu",
			},
		}
		err := svc.GetByteTreeTemplate(ctx, mockReq, mockRes, mockByteTreeMap)
		t.NoError(err)
		t.NotNil(mockRes[0])
		t.Nil(mockRes[1])
		t.Nil(mockRes[2])
		t.Equal(int64(666), mockRes[0].TemplateId)
	})

	t.Run("success, tce", func() {
		defer Mock(GetMethod(svc.ByteTreeRepo, "GetNodeByPSMWithCache")).To(func(ctx context.Context, psm string) (*usecase.BriefBytetreeNodeInfo, error) {
			if psm == "bc.song.tce" {
				return &usecase.BriefBytetreeNodeInfo{
					ID:              123000,
					AllParentIDList: []uint64{0, 1, 2, 3},
				}, nil
			}
			if psm == "bc.song.faas" {
				return &usecase.BriefBytetreeNodeInfo{
					ID:              456000,
					AllParentIDList: []uint64{0, 4, 5, 6},
				}, nil
			}
			return &usecase.BriefBytetreeNodeInfo{}, fmt.Errorf("not found")
		}).Build().UnPatch()
		mockReq := &templatepb.GetTemplateIDByChangeItemsReq{
			WorkflowId:   1,
			NodeId:       1,
			WorkflowType: 1,
			ChangeItems: []*templatepb.BriefChangeItem{
				{
					ControlPlane:    1,
					ProjectType:     1,
					ProjectUniqueId: "bc.song.tce",
				},
				{
					ControlPlane:    2,
					ProjectType:     2,
					ProjectUniqueId: "bc.song.faas",
				},
				{
					ControlPlane:    2,
					ProjectType:     100,
					ProjectUniqueId: "aaa",
				},
			},
		}
		mockRes := make([]*templatepb.BriefTemplateItem, 3)
		mockByteTreeMap := map[string]*cdmodel.OreoPplTemplate{
			"1-1-123000": {
				TemplateID:  666,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "zhangsan",
			},
			"2-2-6": {
				TemplateID:  777,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "lisi",
			},
			"2-2-5": {
				TemplateID:  888,
				ProjectType: int32(sharedpb.ProjectType_PROJECT_TYPE_WEB),
				UpdatedAt:   time.Now(),
				UpdatedBy:   "wangwu",
			},
		}

		err := svc.GetByteTreeTemplate(ctx, mockReq, mockRes, mockByteTreeMap)
		t.NoError(err)
		t.NotNil(mockRes[0])
		t.Equal(int64(666), mockRes[0].TemplateId)
		t.Equal(int64(777), mockRes[1].TemplateId)
	})

}
