/**
 * @Date: 2023/12/27
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package coredomain

import (
	"testing"

	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/bits/complat_common"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/bits/component_platform/server"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/backends/optimusserver"
	"github.com/stretchr/testify/assert"
)

func TestMapper(t *testing.T) {
	t.Run("TransformAndroidAssociateArtifactToOptimusComponent", func(t *testing.T) {
		stage := complat_common.RepoUpgradeCallbackStage_Start
		jobUrl := "www.bytedance.net"
		input := &server.AndroidAssociateArtifact{
			RepoId:       42214,
			Version:      "7.10.1-alpha.27-test-SNAPSHOT",
			Upgrade:      true,
			HistoryId:    34934357,
			BuildResult_: 1,
			ChangeLog:    "a",
			Flavor:       "b",
			Mo<PERSON>le:       "c",
			GroupId:      "d",
			ArtifactId:   "e",
			ExtraData:    "{\"checkJobUrl\":\"\",\"buildNumber\":0,\"gitlabId\":427756,\"gitlabMrId\":598,\"commitId\":\"\",\"repoUrl\":\"******************:ee/message_card_engine.git\",\"type\":\"snapshot\",\"username\":\"shuaiwenliang\",\"uploadOptimus\":true,\"origin_commit_id\":\"46dda4a30df3cbde5d8e282c20d108e1489b0e39\",\"extra_info\":{\"upgrade_key\":\"lVZtZYBIxU\"}}",
		}

		component := TransformAndroidAssociateArtifactToOptimusComponent(stage, jobUrl, input)

		assert.Equal(t, int64(42214), component.Id)
		assert.Equal(t, optimusserver.ComponentStatusRunning, component.Status)
		assert.Equal(t, "a", component.Message)
		assert.Equal(t, int64(427756), component.ProjectId)
		assert.Equal(t, int64(598), component.MrIid)
		assert.Equal(t, "", component.CommitId)
		assert.Equal(t, "www.bytedance.net", component.CheckJobUrl)
		assert.Equal(t, true, component.Upgrade)
		assert.Equal(t, "46dda4a30df3cbde5d8e282c20d108e1489b0e39", component.OriginCommitId)
		assert.Equal(t, map[string]string{"upgrade_key": "lVZtZYBIxU"}, component.ExtraInfo)
		assert.Equal(t, "7.10.1-alpha.27-test-SNAPSHOT", component.Version)
	})
}
