/**
 * @Date: 2022/7/18
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package ee

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRepository(t *testing.T) {
	ctx := context.Background()
	client := New()

	t.Run("CreateRepository", func(t *testing.T) {
		t.Ski<PERSON>Now()

		args := NewCreateRepositoryArgs("1", "******************:ugc/component-pod-template.git", "abc", "", true, true, true, 1, false, 1)
		req := NewCreateRepositoryRequest("liutao.rs", PlatformGitlab, "liutao.rs/abcabc", *********************, args.Jsonify())

		id := client.CreateRepository(ctx, req).Must()

		assert.True(t, id > 0)
	})
}
