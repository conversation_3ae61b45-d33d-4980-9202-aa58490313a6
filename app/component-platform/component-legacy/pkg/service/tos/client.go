package tos

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/tos"
)

var (
	ArchTosClient *tos.Tos
)

func Init() {
	var err error
	ArchTosClient, err = tos.NewTos(tos.WithAuth(ArchBucket, ArchBucketAccessKey))
	if err != nil {
		panic(err)
	}

	if env.IsBoe() {
		boeClient, err := tos.NewTos(tos.WithAuth(BitsBoeBucket, BitsBoeAccessKey))
		if err != nil {
			panic(err)
		}
		ArchTosClient = boeClient
	}
}

func MakeUrl(bucket, key string) string {
	return TosInfo[bucket].BaseUrl + key
}

// tos
const (
	// ttclient-android 的 tos
	AndroidBucket          = "ttclient-android-crashinfo"
	AndroidBucketAccessKey = "NTD082DDQDJZ2TTS38KS"

	//tos bucket1
	ArchBucket          = "toutiao.ios.arch"
	ArchBucketAccessKey = "MJMETJODXZF7FZLFY3VT"

	//tos bucket2
	BinaryBucket          = "iosbinary"
	BinaryBucketAccessKey = "UK8JN1U3GCZ2EVKYJF1V"

	IOSFrameworkBucket    = "staticanalysisresult"
	IOSFrameworkAccessKey = "C5V4TROQGXMCTPXLIJFT"

	BitsBoeBucket    = "bits"
	BitsBoeAccessKey = "CJFBGXL1PXNQJ2X34U2K"
)

type TosBaseInfo struct {
	Bucket  string `json:"bucket"`
	Token   string `json:"token"`
	BaseUrl string `json:"base_url"`
}

var TosInfo = map[string]TosBaseInfo{
	"toutiao.ios.arch": {
		Bucket:  "toutiao.ios.arch",
		Token:   "MJMETJODXZF7FZLFY3VT",
		BaseUrl: "http://voffline.byted.org/obj/toutiao.ios.arch/",
	},
	"iosbinary": {
		Bucket:  "iosbinary",
		Token:   "UK8JN1U3GCZ2EVKYJF1V",
		BaseUrl: "http://voffline.byted.org/obj/iosbinary/",
	},
}
