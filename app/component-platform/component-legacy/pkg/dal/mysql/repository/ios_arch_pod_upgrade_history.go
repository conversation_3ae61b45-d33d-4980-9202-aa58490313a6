/**
 * @Date: 2022/7/5
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"code.byted.org/devinfra/hagrid/app/component-platform/component-legacy/pkg/dal/mysql/entity"
	"context"

	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"gorm.io/plugin/dbresolver"
)

type IosArchPodUpgradeHistoryRepository struct {
	db *gorm.DB
}

func NewIosArchPodUpgradeHistoryRepository(mdb *gorm.DB) *IosArchPodUpgradeHistoryRepository {
	return &IosArchPodUpgradeHistoryRepository{
		db: mdb.Table(schema.Tabler(new(entity.PodUpgradeHistory)).TableName()),
	}
}

func (repository *IosArchPodUpgradeHistoryRepository) Create(ctx context.Context, value *entity.PodUpgradeHistory) gresult.R[int64] {
	err := repository.db.
		WithContext(ctx).
		Create(value).
		Error

	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(value.Id)
}

func (repository *IosArchPodUpgradeHistoryRepository) FindLastByRepoIdAndVersion(ctx context.Context, repoId int64, version string) gresult.R[*entity.PodUpgradeHistory] {
	value := new(entity.PodUpgradeHistory)
	err := repository.db.
		WithContext(ctx).
		Where("`repoId` = ? and `version` = ?", repoId, version).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.PodUpgradeHistory](err)
	}
	return gresult.OK(value)
}

func (repository *IosArchPodUpgradeHistoryRepository) FindLastByRepoIdAndVersionAndCommitId(ctx context.Context, repoId int64, version string, commitId string) gresult.R[*entity.PodUpgradeHistory] {
	value := new(entity.PodUpgradeHistory)
	err := repository.db.
		WithContext(ctx).
		Where("`repoId` = ? AND `version` = ? AND `commit_id` = ?", repoId, version, commitId).
		Order("`id` DESC").
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.PodUpgradeHistory](err)
	}
	return gresult.OK(value)
}

func (repository *IosArchPodUpgradeHistoryRepository) FindLastByRepoIdAndVersionAndCommitIdAndBuildResultAndBuildFromLike(ctx context.Context, repoId int64, version string, commitId string, buildResult string, buildFrom string) gresult.R[*entity.PodUpgradeHistory] {
	value := new(entity.PodUpgradeHistory)
	err := repository.db.
		WithContext(ctx).
		Where("`repoId` = ? AND `version` = ? AND `commit_id` = ? AND `buildResult` = ? AND `build_from` LIKE ?", repoId, version, commitId, buildResult, buildFrom).
		Order("`id` DESC").
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.PodUpgradeHistory](err)
	}
	return gresult.OK(value)
}

func (repository *IosArchPodUpgradeHistoryRepository) CountByRepoIdAndVersion(ctx context.Context, repoId int64, version string) gresult.R[int64] {
	var count int64
	err := repository.db.
		WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("`repoId` = ? AND `version` = ?", repoId, version).
		Count(&count).
		Error

	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(count)
}

func (repository *IosArchPodUpgradeHistoryRepository) DeleteById(ctx context.Context, id int64) error {
	err := repository.db.
		WithContext(ctx).
		Delete(new(entity.PodUpgradeHistory), id).
		Error

	if err != nil {
		return err
	}
	return nil
}
