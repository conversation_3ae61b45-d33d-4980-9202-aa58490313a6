package repo

import (
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/consts"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/db"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/mongo"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/redis"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/tcc"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/tos"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/utils"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	common_utils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"net/http"
	"time"

	"code.byted.org/bytedoc/mongo-go-driver/bson"
	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
)

const (
	IOSFrameworkTosKey           = "ios_frameworks.json"
	IOSFrameworkRedisKey         = "update_tos_ios_frameworks"
	IOSFrameworkMysqlKey         = "ios_frameworks"
	IOSFrameworkTCCKey           = "use_mongo_data"
	IOSFrameworkMongoKey         = "ios_repo_binary_record"
	IOSFrameworkMongoDataVersion = "1"
)

var filter = bson.M{"version": IOSFrameworkMongoDataVersion}

type versionMongoData struct {
	Version string `bson:"version"`
	Content string `bson:"content"`
}

type updateTosRecordReq struct {
	RepoName string `json:"repo_name" valid:"NotEmpty"`
	Version  string `json:"version" valid:"NotEmpty"`
	AppID    string `json:"app_id" valid:"NotEmpty"`
}

func UpdateTosRecord(ctx *gin.Context) {
	req := &updateTosRecordReq{}
	err := ctx.Bind(req)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_PARAM_ERROR, nil, err)
		return
	}
	err = utils.ParamsValidate(*req)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_PARAM_ERROR, nil, err)
		return
	}

	err = updateTosRecord(ctx, req)
	if err != nil {
		utils.LogCtxError(ctx, "update tos record error: %v", err)
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_PARAM_ERROR, nil, err)
		return
	}

	utils.RenderCommonResponse(ctx, consts.ERROR_NO_OK, nil, nil)
	return
}

func GetTosRecord(ctx *gin.Context) {
	obj, err := tos.IOSFrameworkClient.GetObject(ctx, IOSFrameworkTosKey)
	if err != nil {
		utils.RenderHTTPErrorResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, err, http.StatusInternalServerError)
	}
	defer obj.R.Close()
	bytesData, err := io.ReadAll(obj.R)

	//useMongoData := tcc.GetBoolWithDefault(ctx, consts.TCC_PSM_CORE_CONFIG, IOSFrameworkTCCKey, false)
	//if useMongoData {
	//	dataMap, err := getTosRecordFromMongo(ctx)
	//	if err != nil {
	//		utils.RenderHTTPErrorResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, err, http.StatusInternalServerError)
	//		return
	//	}
	//
	//	utils.RenderJsonResponse(ctx, dataMap)
	//	return
	//}
	//
	//data, err := db.GetStoreValueForKey(ctx, IOSFrameworkMysqlKey)
	//if err != nil {
	//	utils.RenderHTTPErrorResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, err, http.StatusInternalServerError)
	//	return
	//}
	dataMap := make(map[string]interface{})
	err = json.Unmarshal(bytesData, &dataMap)
	if err != nil {
		utils.RenderHTTPErrorResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, err, http.StatusInternalServerError)
		return
	}

	utils.RenderJsonResponse(ctx, dataMap)
}

func getTosRecordFromMongo(ctx context.Context) (map[string]interface{}, error) {
	foundDoc := versionMongoData{}
	err := mongo.BitsDocDB.Collection(IOSFrameworkMongoKey).FindOne(ctx, filter).Decode(&foundDoc)
	if err != nil {
		utils.LogCtxError(ctx, "read ios framework record from mongo failed. err: %v", err)
		return nil, err
	}

	dataMap := make(map[string]interface{})
	err = json.Unmarshal([]byte(foundDoc.Content), &dataMap)
	if err != nil {
		return nil, err
	}

	return dataMap, nil
}

// app id => repo name => versions

func updateTosRecord(ctx context.Context, req *updateTosRecordReq) error {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()

	// 先加锁
	key := fmt.Sprintf("%s:%s:%s", IOSFrameworkRedisKey, req.RepoName, req.Version)
	ok, err := redis.RedisClient.SetNX(key, "1", time.Second*5).Result()
	logs.CtxInfo(ctx, "ok: %v, err: %v", ok, err)
	if err == nil && !ok {
		return errors.New("tos file is locked, please try again later")
	}

	defer func() {
		logs.CtxInfo(ctx, "release redis lock")
		ok, err := redis.RedisClient.Del(key).Result()
		logs.CtxInfo(ctx, "ok: %v, err: %v", ok, err)
	}()

	dataMap := make(map[string]interface{})

	useMongoData := tcc.GetBoolWithDefault(ctx, consts.TCC_PSM_CORE_CONFIG, IOSFrameworkTCCKey, false)
	if useMongoData {
		dataMap, err = getTosRecordFromMongo(ctx)
		if err != nil {
			return err
		}
	} else {
		//data, err := db.GetStoreValueForKey(ctx, IOSFrameworkMysqlKey)
		//if err != nil { // 从tos获取并写入到mysql
		//	obj, err := tos.IOSFrameworkClient.GetObject(ctx, IOSFrameworkTosKey)
		//	if err != nil {
		//		return err
		//	}
		//	defer obj.R.Close()
		//	bytesData, err := ioutil.ReadAll(obj.R)
		//	data = string(bytesData)
		//	_ = db.InsertStoreValueForKey(ctx, IOSFrameworkMysqlKey, data)
		//}

		obj, err := tos.IOSFrameworkClient.GetObject(ctx, IOSFrameworkTosKey)
		if err != nil {
			return err
		}
		defer obj.R.Close()
		bytesData, err := ioutil.ReadAll(obj.R)
		data := string(bytesData)
		_ = db.InsertStoreValueForKey(ctx, IOSFrameworkMysqlKey, data)

		// 解析json
		err = json.Unmarshal([]byte(data), &dataMap)
		if err != nil {
			return err
		}
	}

	// 检测app id是否存在
	if _, ok := dataMap[req.AppID]; !ok {
		dataMap[req.AppID] = make(map[string]interface{})
	}

	// 解析app数据
	appData, ok := dataMap[req.AppID].(map[string]interface{})
	if !ok {
		return errors.New("Dirty data, please contact the platform")
	}

	// 检测repo name是否存在
	if _, ok = appData[req.RepoName]; !ok {
		appData[req.RepoName] = make([]interface{}, 0)
	}

	// 解析version数据
	versionData, ok := appData[req.RepoName].([]interface{})
	if !ok {
		return errors.New("Dirty data, please contact the platform")
	}

	appData[req.RepoName] = append(versionData, req.Version)
	dataMap[req.AppID] = appData

	jsonData := utils.ToJson(dataMap)

	// 总是异步更新tos
	common_utils.SafeGo(ctx, func() {
		err = tos.IOSFrameworkClient.PutObject(ctx, IOSFrameworkTosKey, int64(len(jsonData)), bytes.NewBuffer([]byte(jsonData)))
		if err != nil {
			utils.LogCtxInfo(ctx, "update tos record fail")
		}
	})

	if useMongoData {
		// 异步写 db，同步写 mongo
		common_utils.SafeGo(ctx, func() {
			// 更新数据库
			utils.LogCtxInfo(ctx, "update mysql record begin")
			err = db.UpdateStoreValueForKey(ctx, IOSFrameworkMysqlKey, jsonData)
			if err != nil {
				utils.LogCtxInfo(ctx, "update mysql record fail")
			} else {
				utils.LogCtxInfo(ctx, "update mysql record success")
			}
		})

		utils.LogCtxInfo(ctx, "update mongo record begin")
		err = saveRecordToMongo(ctx, jsonData)
		if err != nil {
			utils.LogCtxInfo(ctx, "update mongo record fail")
			return err
		}

		utils.LogCtxInfo(ctx, "update mongo record success")
		return nil
	} else {
		// 异步入 Mongo，同步写 DB
		common_utils.SafeGo(ctx, func() {
			utils.LogCtxInfo(ctx, "update mongo record begin")
			err = saveRecordToMongo(ctx, jsonData)
			if err != nil {
				utils.LogCtxInfo(ctx, "update mongo record fail")
			} else {
				utils.LogCtxInfo(ctx, "update mongo record success")
			}
		})

		utils.LogCtxInfo(ctx, "update mysql record begin")
		err = db.UpdateStoreValueForKey(ctx, IOSFrameworkMysqlKey, jsonData)
		if err != nil {
			utils.LogCtxInfo(ctx, "update mysql record fail")
			return err
		}
		utils.LogCtxInfo(ctx, "update mysql record success")
		return nil
	}
}

func saveRecordToMongo(ctx context.Context, content string) error {

	foundDoc := versionMongoData{}

	collection := mongo.BitsDocDB.Collection(IOSFrameworkMongoKey)
	err := collection.FindOne(ctx, filter).Decode(&foundDoc)
	if err != nil {
		if mongo.IsErrNoDocuments(err) {
			doc := versionMongoData{
				Version: IOSFrameworkMongoDataVersion,
				Content: content,
			}
			result, err := collection.InsertOne(ctx, doc)
			if err != nil {
				utils.LogCtxError(ctx, "insert data to mongo failed. err: %v", err)
				return err
			}
			utils.LogCtxInfo(ctx, "insert success: %v", result.InsertedID)
		} else {
			utils.LogCtxError(ctx, "query data from mongo failed. err: %v", err)
			return err
		}
	} else {
		result, err := collection.UpdateOne(ctx, filter, bson.D{{
			"$set", bson.D{{
				"content", content,
			}},
		}})
		if err != nil {
			utils.LogCtxError(ctx, "insert data to mongo failed. err: %v", err)
			return err
		}
		utils.LogCtxInfo(ctx, "update success: %v %v", result.ModifiedCount, result.UpsertedID)
	}
	return nil
}
