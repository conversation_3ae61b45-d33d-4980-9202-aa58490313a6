package repo

import (
	"context"
	"net/http/httputil"

	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/business/repo/tags"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/consts"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/model"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/db"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/git_lab_dev"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/utils"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/utils/ginex_helper"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	json "github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
)

type UpdateParams struct {
	CreateCommonComponentsBody
	RepoID int `json:"repoID" valid:"int-min=1"`
}

func GetRepoByRepoID(ctx context.Context, repoID int) (*model.ArchPodMainNg, error) {
	repo, err := db.GetRepoNgByID(ctx, repoID)
	if err != nil {
		return nil, err
	}
	return repo, err
}

func UpdateRepo(ctx context.Context, oldRepo *model.ArchPodMainNg, req *UpdateParams) (*model.ArchPodMainNg, error) {
	updateRepoParams := buildUpdateNgModelByParams(ctx, oldRepo, req)
	newRepo, err := db.UpdateRepoNg(ctx, updateRepoParams)

	return newRepo, err
}

// 根据params构建新的Ng-model
func buildUpdateNgModelByParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	// 根据NG和req构建新的NG
	repo = buildCommonParams(ctx, repo, req)
	appType := req.AppType
	switch consts.AppType(appType) {
	case consts.AppType_Android:
		repo = buildAndroidParams(ctx, repo, req)
	case consts.AppType_Ios:
		repo = buildIOSParmas(ctx, repo, req)
	case consts.FLUTTER_APP_TYPE:
		repo = buildFlutterParmas(ctx, repo, req)
	case consts.AppType_Custom:
		repo = buildCustomParams(ctx, repo, req)
	case consts.RUBYGEM_APP_TYPE:
		repo = buildRubyGemParams(ctx, repo, req)
	case consts.AppType_Harmony:
		repo = buildHarmonyParams(ctx, repo, req)
	}
	return repo
}

func AfterUpdate(ctx context.Context, repo *model.ArchPodMainNg, oldNgRepo *model.ArchPodMainNg, req *UpdateParams) error {
	var err error
	appType := repo.AppType
	tagIds := req.Tags

	switch consts.AppType(appType) {
	case consts.AppType_Android:
		{
			err = afterUpdateAndroid(ctx, repo)
		}
	case consts.AppType_Ios:
		{
			tagIds = tags.HandlerContainerTagForIosComponent(tagIds, repo)
		}
	}
	err = tags.EditTagComponentRelation(ctx, tagIds, repo.ID)

	updateTeamRelation(ctx, repo, req)
	return err
}

func updateTeamRelation(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) {
	if req.TeamID != nil && req.TeamName != nil {
		relation, err := db.GetRelatedRepoByAppIDAndRepoID(ctx, repo.AppID, repo.ID)
		if err != nil {
			utils.LogCtxError(ctx, "err = %v", err)
		}

		if relation == nil {
			relation = &model.MpaasAppRepoRelation{}
			relation.AppId = repo.AppID
			relation.RepoId = repo.ID
		}

		if *req.TeamID != relation.TeamId || *req.TeamName != relation.TeamName {
			relation.TeamId = *req.TeamID
			relation.TeamName = *req.TeamName
			db.UpsertTeamByAppIdAndRepoId(ctx, []*model.MpaasAppRepoRelation{relation})
		}
	}
}

func afterUpdateAndroid(ctx context.Context, repo *model.ArchPodMainNg) error {
	var err error
	// 更新这个父组件下的username,,,
	if repo.Config.AndroidExtInfo.Group.Role == consts.ANDROID_COMPONENT_PARENT {
		userName := db.BuildUserList(repo.Config.Members, db.REPO_HEAD_MAN)
		followList := db.BuildUserList(repo.Config.Members, db.REPO_FOLLOW_MAN)
		developers := db.BuildUserList(repo.Config.Members, db.REPO_DEVELOPER_MAN)
		err = db.UpdatePodMainMember(ctx, repo.Config.AndroidExtInfo.Group.ReposID, userName, followList, developers)
		updateSubComponentWhenGroupUpdate(ctx, repo)
	}
	return err
}

func buildCommonParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	repo.AppType = consts.AppType(req.AppType)
	//repo.RepoName = req.RepoName // 拦截前端传过来值
	// 如果是公网库,则不限制改git地址
	if !git_lab_dev.IsIntranetSource(ctx, req.GitUrl) && req.GitUrl != "" {
		repo.GitUrl = req.GitUrl
	}
	if req.AppID != repo.AppID && repo.MetaAppId == repo.AppID { // 更改了app, 重新同步下meta app id
		repo.MetaAppId = req.AppID
	}
	repo.Branch = req.Branch
	repo.Version = req.Version
	repo.Description = req.Description
	repo.RepoHomepage = req.RepoHomepage
	repo.SubappID = req.SubAppID
	repo.AppID = req.AppID
	repo.DocUrl = req.DocUrl
	repo.IsUseOptimus = req.IsUseOptimus
	repo.Tags = req.Tags
	repo.Config.Members = choose.If(len(req.Members) == 0, repo.Config.Members, req.Members)
	repo.Config.LarkGroup = req.LarkGroup
	repo.IsPublic = req.IsPublic
	if len(req.PodOwner) != 0 {
		repo.PodOwner = req.PodOwner // 有值再改，无值就保持原样。（因Bits和ByteBus的兼容性问题）
	}
	// 移动网关
	repo.IsFromBam = req.IsFromBam
	repo.IdlRepoNamespace = req.IdlRepoNamespace
	repo.IdlRepoType = req.IdlRepoType
	if repo.Config.CommonExtInfo == nil {
		repo.Config.CommonExtInfo = &model.PodCommonExtInfo{UpgradeExtInfo: req.UpgradeExtInfo}
	} else {
		repo.Config.CommonExtInfo.UpgradeExtInfo = req.UpgradeExtInfo
	}
	if req.MetaAppId > 0 { // bits平台的, 设置一下配置参数
		repo.MetaAppId = req.MetaAppId
		repo.IsBizPod = req.IsBizPod
		repo.IsExternal = req.IsExternal
		repo.IsBusiness = req.IsBusiness
		repo.Config.DirOwner = req.DirOwner
	}
	if req.ReleaseConfig != nil {
		repo.Config.ReleaseConfig = req.ReleaseConfig
	}
	if req.TobYmlPath != nil {
		repo.TobYmlPath = *req.TobYmlPath
	}
	return repo
}

func buildHarmonyParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	var info HarmonyExtraInfo
	info = GetHarmonyExtraInfo(req.Extra)

	repo.Config.HarmonyExtInfo.BuildProfilePath = info.BuildProfilePath
	repo.Config.HarmonyExtInfo.RepoGroupName = info.RepoGroupName
	repo.Config.HarmonyExtInfo.Targets = info.Targets
	repo.Config.HarmonyExtInfo.UpgradeRelated = info.UpgradeRelated
	repo.Config.HarmonyExtInfo.IsUploadSource = info.IsUploadSource
	repo.Config.HarmonyExtInfo.CustomUpgradeCommand = info.CustomUpgradeCommand
	repo.Config.HarmonyExtInfo.ModuleName = info.ModuleName

	return repo
}

func buildAndroidParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	var info AndroidExtraInfo
	info = GetAndroidExtraInfo(req.Extra)
	if info.Flavors == nil {
		info.Flavors = req.Flavors
	}

	if repo.Config.AndroidExtInfo.Group == nil {
		repo.Config.AndroidExtInfo.Group = &model.PodAndroidExtInfoGroup{
			Role:             info.Group.Role,
			IsVersionRelated: info.Group.IsVersionRelated,
			GroupID:          info.Group.GroupID,
			GroupName:        info.Group.GroupName,
		}
	}
	// 支持组件组更改是否是版本关联和功能关联
	repo.Config.AndroidExtInfo.Group.IsVersionRelated = info.Group.IsVersionRelated
	// 组件组类型修改时同步至旧字段
	repo.Config.AndroidExtInfo.Group.Type = BoolToInt(info.Group.IsVersionRelated)

	repo.Config.AndroidExtInfo.Group.Role = info.Group.Role
	repo.Config.AndroidExtInfo.ModuleName = info.ModuleName
	repo.Config.AndroidExtInfo.CustomUpgradeCommand = info.CustomUpgradeCommand
	repo.Config.AndroidExtInfo.IsUploadSource = info.IsUploadSource
	repo.Config.AndroidExtInfo.IsNdkBuild = info.IsNdkBuildSupport
	repo.Config.AndroidExtInfo.NdkVersion = info.NdkVersion
	repo.Config.AndroidExtInfo.IsFlavor = info.IsFlavor
	repo.Config.AndroidExtInfo.Targets = info.Targets
	repo.Config.AndroidExtInfo.Flavors = info.Flavors

	// 安卓组件，flavors 和 targets 兼容。参考：https://bytedance.feishu.cn/docs/doccnX9JvDxDuq0IFdQaZ6OqAjb
	if ginex_helper.IsFromByteBus(ctx) {
		// 通过 ByteBus 编辑：将 targets 字段同步到 bits 的 flavors 字段
		utils.LogCtxInfo(ctx, "android repo sync use targets: %v", info.Targets)
		repo.Config.AndroidExtInfo.Flavors = info.Targets
	} else if ginex_helper.IsFromBits(ctx) {
		utils.LogCtxInfo(ctx, "android repo sync use flavors: %v", info.Flavors)
		repo.Config.AndroidExtInfo.Targets = info.Flavors
		repo.Config.AndroidExtInfo.IsFlavor = len(info.Flavors) > 0
	} else {
		// 比如通过 API 抓包修改的，不做任何修改
		utils.LogCtxInfo(ctx, "android repo sync use none")
		var ginContext *gin.Context
		var ok bool
		if ginContext, ok = ctx.(*gin.Context); !ok {
			log.V2.Info().With(ctx).Str("not gin context").Emit()
		}

		request, _ := httputil.DumpRequest(ginContext.Request, false)
		utils.LogCtxInfo(ctx, "yes gin context: %v, Referer: %v, Request: %v, header: %v", ginContext, ginContext.Request.Referer(), string(request), ginContext.Request.Header)
		utils.LogCtxInfo(ctx, "origin: %v", ginContext.GetHeader("origin"))
	}

	if info.ArtifactId != "" {
		repo.Config.AndroidExtInfo.ArtifactId = info.ArtifactId
	} else {
		repo.Config.AndroidExtInfo.ArtifactId = req.RepoName
	}
	//repo.Config.AndroidExtInfo.RepoGroupName = info.RepoGroupName // 拦截前端传过来值
	repo.Config.AndroidExtInfo.SettingsGradlePath = info.SettingsGradlePath
	repo.Config.AndroidExtInfo.UpgradeRelated = info.UpgradeRelated
	return repo
}

func buildIOSParmas(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	var info IosExtraInfo
	info = GetIosExtraInfo(req.Extra)
	repo.Config.IosExtInfo.IsNeedFramework = info.IsNeedFramework
	repo.Config.IosExtInfo.IsNeedInferAnalysis = info.IsNeedInferAnalysis
	repo.Config.IosExtInfo.IsStrict = info.IsStrict
	repo.Config.IosExtInfo.IsNeedSecurity = info.IsNeedSecurity
	repo.Config.IosExtInfo.IsNeedSemver = info.IsNeedSemver
	repo.Config.IosExtInfo.SubSpecs = info.SubSpecs
	repo.Config.IosExtInfo.LinkType = info.LinkType
	repo.Config.IosExtInfo.Configurations = info.Configurations
	if info.IsContainer != nil {
		repo.Config.IosExtInfo.IsContainer = *info.IsContainer
	}
	repo.Config.IosExtInfo.CompilerFlags = info.CompilerFlags
	repo.Config.IosExtInfo.NoSource = info.NoSource
	repo.Config.IosExtInfo.XcodeVersion = info.XcodeVersion
	repo.Config.IosExtInfo.IsNeedFramework = req.ReleaseConfig.PublishType != model.IosProjectBinaryTypeNone

	if len(info.BuildConfig) > 0 {
		var buildConfig model.BusIosRepoUpgradeBuildProducts
		err := json.Unmarshal([]byte(info.BuildConfig), &buildConfig)
		if err != nil {
			utils.LogCtxDebug(ctx, "err = %v", err)
		}
		repo.Config.IosExtInfo.BuildConfig = &buildConfig
	}

	return repo
}

func buildFlutterParmas(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	var info FlutterExtraInfo
	info = GetFlutterExtraInfo(req.Extra)
	repo.Config.FlutterExtInfo.PlayGroundInfo = info.PlaygroundInfo
	repo.Config.FlutterExtInfo.FilePath = info.FilePath
	return repo
}

func buildCustomParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	repo.CloudBuildTemplateId = req.CloudBuildTemplateId
	return repo
}

func buildRubyGemParams(ctx context.Context, repo *model.ArchPodMainNg, req *UpdateParams) *model.ArchPodMainNg {
	var info RubyGemExtraInfo
	info = GetRubyGemExtraInfo(req.Extra)
	repo.Config.RubyGemExtInfo.GemPath = info.GemPath
	return repo
}

// 当组件组相关字段更新时子组件同时进行更新
func updateSubComponentWhenGroupUpdate(ctx context.Context, groupComp *model.ArchPodMainNg) {
	if groupComp.Config.AndroidExtInfo.Group != nil {
		for _, subCompId := range groupComp.Config.AndroidExtInfo.Group.ReposID {
			subComp, err := db.GetRepoNgByID(ctx, subCompId)
			if err == nil {
				if subComp.Config.AndroidExtInfo.Group != nil {
					// 因为组件组支持修改类型,所以同步相关字段到其子组件
					subComp.Config.AndroidExtInfo.Group.Type = BoolToInt(groupComp.Config.AndroidExtInfo.Group.IsVersionRelated)
					subComp.Config.AndroidExtInfo.Group.IsVersionRelated = groupComp.Config.AndroidExtInfo.Group.IsVersionRelated
				}

				// 如果是版本关联的组件组,同步【上传源码】字段值到其下所有子组件
				if groupComp.Config.AndroidExtInfo.Group.IsVersionRelated {
					subComp.Config.AndroidExtInfo.IsUploadSource = groupComp.Config.AndroidExtInfo.IsUploadSource
				}

				_, updateErr := db.UpdateAndroidExtInfoById(ctx, int64(subComp.ID), subComp.Config.AndroidExtInfo)
				if updateErr != nil {
					utils.LogCtxInfo(ctx, "updateSubComponentWhenGroupUpdate UpdateAndroidExtInfoById error: %v", err)
				}
			} else {
				utils.LogCtxInfo(ctx, "updateSubComponentWhenGroupUpdate GetRepoNgByID error: %v", err)
			}
		}
	}
}
