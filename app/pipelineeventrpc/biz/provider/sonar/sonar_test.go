package sonar

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/pipelineeventrpc/biz/dal/kms"
)

func TestMain(m *testing.M) {
	kms.MustInitialize()
	MustInitialize()
	m.Run()
}

func TestGetReport(t *testing.T) {
	t.Run("test get report", func(t *testing.T) {
		report, err := GetReport(context.Background(), "devops/liuyang_atomservice", "b197ad9b8565fdde37cbbbff7df7b3ffe679607b")
		if err != nil {
			t.Fatal(err)
		}
		t.Logf("%+v", report)
	})
}
