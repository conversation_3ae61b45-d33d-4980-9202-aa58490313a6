package utils

import (
	"code.byted.org/gopkg/logs"
	"github.com/bytedance/sonic"
	"unsafe"
)

func StringToBytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	b := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&b))
}

func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func ToJsonString(v interface{}) string {
	b, err := sonic.Marshal(v)
	if err != nil {
		logs.Error("ToJson error, v=%#v, error=%v", v, err)
	}
	return BytesToString(b)
}
