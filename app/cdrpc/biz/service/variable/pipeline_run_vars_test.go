package variable

import (
	"context"
	"reflect"

	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"code.byted.org/bits/monkey"
	ppl "code.byted.org/canal/bytecycle_sdk/pipeline"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb/varstoreservice_mock"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	oreopb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	spacerpcpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi_mock"
	"code.byted.org/kite/kitex/client/callopt"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	changeItemMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/oreo_manager"
	"code.byted.org/devinfra/hagrid/internal/varstore/testfactory"
	"code.byted.org/devinfra/hagrid/pkg/pvariable"
)

func (t *ReleaseTicketVarSuite) Test_GetStagePipelineRunVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	ctx := context.Background()

	rtMockDao := repositorymock.NewMockReleaseTicketDao(ctrl)
	rtMockDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(
		&entity.DBReleaseTicket{
			ReleaseTicketID: 1,
			WorkspaceID:     1,
		}, nil).AnyTimes()
	r := &releaseTicketVarSVC{
		releaseTicketDao: rtMockDao,
	}
	mockey.Mock(r.getReleaseTicketCustomVars).Return([]*release_ticket_sharedpb.Variable{
		&release_ticket_sharedpb.Variable{
			Definition: &varstorepb.VarDefinition{
				Name:     "ticket1",
				FullName: "custom.ticket1",
			},
		},
	}, nil).Build()
	mockey.Mock(r.getStagePipelinesRunVars).Return([]*release_ticket_sharedpb.Variable{
		&release_ticket_sharedpb.Variable{
			Definition: &varstorepb.VarDefinition{
				Name:     "var1",
				FullName: "custom.var1",
			},
		},
	}, []*entity.DBStagePipeline{&entity.DBStagePipeline{
		PipelineID: 1,
	}}, nil).Build()

	t.Run("get no vars", func() {
		p1 := mockey.Mock(r.useByteTreeVarsInSpace).Return(false, nil).Build()
		defer p1.UnPatch()
		p2 := mockey.Mock(r.getProjectCustomVars).Return([]*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "projectVar1",
					FullName: "custom.projectVar1",
				},
			},
		}, nil).Build()
		defer p2.UnPatch()

		got, err := r.GetStagePipelineRunVars(ctx, &release_ticket_sharedpb.GetStagePipelineRunVarsReq{
			StageId:              1,
			ReleaseTicketId:      1,
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
			SelectedProjects:     nil,
		})
		t.NoError(err)
		t.NotNil(got)
		got, err = r.GetStagePipelineRunVars(ctx, &release_ticket_sharedpb.GetStagePipelineRunVarsReq{
			StageId:              1,
			ReleaseTicketId:      1,
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP,
			SelectedProjects:     nil,
		})
		t.NoError(err)
		t.NotNil(got)
	})

	t.Run("byte_tree vars", func() {
		p1 := mockey.Mock(r.useByteTreeVarsInSpace).Return(true, nil).Build()
		defer p1.UnPatch()

		p2 := mockey.Mock(r.getByteTreeCustomVars).Return([]*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "byteTreeVar1",
					FullName: "custom.byteTreeVar1",
				},
			},
		}, nil).Build()
		defer p2.UnPatch()

		got, err := r.GetStagePipelineRunVars(ctx, &release_ticket_sharedpb.GetStagePipelineRunVarsReq{
			StageId:              1,
			ReleaseTicketId:      1,
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
			SelectedProjects:     nil,
		})
		t.NoError(err)
		t.NotNil(got)
	})
}

func (t *ReleaseTicketVarSuite) Test_getSinglePipelineVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	pipelineSDK := oreo_manager.NewOreoManager()

	DSL := "{\"Stages\":[{\"states\":[\"da69fe\",\"e5c705\",\"specify_upgrade_policy-ff0643\",\"cronjob_cluster_upgrade-43e5e2\"]},{\"states\":[\"4d7b60\",\"fdd7fa\",\"cronjob_cluster_test-fe882c\",\"CronjobClusterEnvUpdate-ee6865\",\"cronjob_cluster_selector-6a66bb\"]}],\"States\":{\"4d7b60\":{\"Type\":\"Pass\",\"Next\":\"cronjob_cluster_selector-6a66bb\",\"id\":\"4d7b60\"},\"fdd7fa\":{\"Type\":\"Pass\",\"id\":\"fdd7fa\",\"End\":true},\"cronjob_cluster_test-fe882c\":{\"id\":\"cronjob_cluster_test-fe882c\",\"Type\":\"Task\",\"Next\":\"fdd7fa\",\"Parameters\":{\"service_title\":\"Cronjob cluster test\",\"service_name\":\"cronjob_cluster_test\",\"stream_attributes\":{\"rerun_command\":\"\"}},\"nextArr\":[],\"level\":0,\"stage\":\"0__heart__+__heart__Stage\"},\"CronjobClusterEnvUpdate-ee6865\":{\"id\":\"CronjobClusterEnvUpdate-ee6865\",\"Type\":\"Task\",\"Next\":\"cronjob_cluster_test-fe882c\",\"Parameters\":{\"service_title\":\"cronjob_cluster_env_update\",\"service_name\":\"CronjobClusterEnvUpdate\",\"stream_attributes\":{\"psm\":\"toutiao.canal.tjy_test\",\"control_plane\":\"cn\",\"cluster_ids\":\"51362\",\"service_env\":\"sinf\"}}},\"da69fe\":{\"Type\":\"Pass\",\"Next\":\"specify_upgrade_policy-ff0643\",\"id\":\"da69fe\"},\"e5c705\":{\"Type\":\"Pass\",\"Next\":\"4d7b60\",\"id\":\"e5c705\"},\"specify_upgrade_policy-ff0643\":{\"id\":\"specify_upgrade_policy-ff0643\",\"Type\":\"Task\",\"Next\":\"cronjob_cluster_upgrade-43e5e2\",\"Parameters\":{\"service_title\":\"specify upgrade policy\",\"service_name\":\"specify_upgrade_policy\",\"stream_attributes\":{\"scm_configs\":[{\"scm_repo_name\":\"toutiao/devops/tjy_test01\",\"upgrade_policy\":\"latest\",\"version\":\"\"}],\"notify_status\":[\"START\"],\"extra_content_list\":[{\"content_title\":\"{{context.params.1}}\",\"content_value\":\"\"},{\"content_title\":\"{{custom.foo}}\",\"content_value\":\"\"}]}}},\"cronjob_cluster_upgrade-43e5e2\":{\"id\":\"cronjob_cluster_upgrade-43e5e2\",\"Type\":\"Task\",\"Next\":\"e5c705\",\"Parameters\":{\"service_title\":\"Cronjob cluster upgrade\",\"service_name\":\"cronjob_cluster_upgrade\",\"stream_attributes\":{\"psm\":\"toutiao.canal.tjy_test\",\"cluster_ids\":\"-1\",\"service_env\":\"tce_cn\"}},\"nextArr\":[\"cronjob_cluster_test-fe882c\"],\"level\":0,\"stage\":\"2__heart__+__heart__Stage\"},\"cronjob_cluster_selector-6a66bb\":{\"id\":\"cronjob_cluster_selector-6a66bb\",\"Type\":\"Task\",\"Next\":\"CronjobClusterEnvUpdate-ee6865\",\"Parameters\":{\"service_title\":\"cronjob集群选择\",\"service_name\":\"cronjob_cluster_selector\",\"stream_attributes\":{\"config_method\":\"dynamic\",\"cluster_env\":\"cn\",\"cluster_psm\":\"toutiao.canal.tjy_test\",\"cluster_regexp\":\"\"}},\"nextArr\":[],\"level\":0,\"stage\":\"0__heart__+__heart__Stage\"}},\"StartAt\":\"da69fe\"}"
	monkey.PatchInstanceMethod(reflect.TypeOf(pipelineSDK), "GetOnePipeline").Return(&oreopb.Pipeline{
		PipelineId: 10,
		VarGroup:   &varstorepb.VarGroup{GroupId: 100},
		Stages:     &oreopb.Pipeline_Dsl{Dsl: DSL},
	}, nil)
	oldPipelineSDK := ppl.NewSDK()
	monkey.PatchInstanceMethod(reflect.TypeOf(oldPipelineSDK), "GetOnePipeline").Return(&ppl.Pipeline{
		ID:         10,
		VarGroupID: 100,
		DSL:        DSL,
	}, nil)

	changeItemSvc := changeItemMock.NewMockChangeItemSvc(ctrl)
	changeItemSvc.EXPECT().GetProjectName(gomock.Any(), gomock.Any()).Return("bits.cd.api").AnyTimes()
	changeItemSvc.EXPECT().GetProjectNameByDbChangeItem(gomock.Any(), gomock.Any()).Return("bits.cd.api").AnyTimes()
	t.Run("filter not cited vars", func() {
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetVarGroupResp{Group: &varstorepb.VarGroup{
				GroupId:        1,
				Version:        2,
				VarDefinitions: []*varstorepb.VarDefinition{{Name: "foo", FullName: "custom.foo"}, {Name: "123", FullName: "custom.123"}},
			}}, nil)
		r := &releaseTicketVarSVC{
			varstoreCli:   mockVarstoreCli,
			changeItemSvc: changeItemSvc,
			oreoManager:   oreo_manager.NewOreoManager(),
		}

		got, got1, err := r.getSinglePipelineVars(t.ctx, &entity.DBStagePipeline{
			PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN.String(),
			PipelineID:   10,
		})
		t.NoError(err)

		t.Equal([]*release_ticket_sharedpb.Variable{{
			Definition:       &varstorepb.VarDefinition{Name: "foo", FullName: "custom.foo"},
			AssociationItems: []*release_ticket_sharedpb.AssociationItem{newMainPipelineAssociationItem(10)},
			GroupId:          1,
			Version:          2,
		}}, got)
		t.NotNil(got1)
	})

	t.Run("mainPipeline", func() {
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetVarGroupResp{Group: &varstorepb.VarGroup{
				GroupId:        1,
				Version:        2,
				VarDefinitions: []*varstorepb.VarDefinition{{Name: "foo", FullName: "custom.foo"}},
			}}, nil)
		r := &releaseTicketVarSVC{
			varstoreCli:   mockVarstoreCli,
			changeItemSvc: changeItemSvc,
			oreoManager:   oreo_manager.NewOreoManager(),
		}

		got, got1, err := r.getSinglePipelineVars(t.ctx, &entity.DBStagePipeline{
			PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN.String(),
			PipelineID:   10,
		})
		t.NoError(err)

		t.Equal([]*release_ticket_sharedpb.Variable{{
			Definition:       &varstorepb.VarDefinition{Name: "foo", FullName: "custom.foo"},
			AssociationItems: []*release_ticket_sharedpb.AssociationItem{newMainPipelineAssociationItem(10)},
			GroupId:          1,
			Version:          2,
		}}, got)
		t.NotNil(got1)
	})

	t.Run("projectPipeline", func() {
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetVarGroupResp{Group: &varstorepb.VarGroup{
				GroupId:        1,
				Version:        2,
				VarDefinitions: []*varstorepb.VarDefinition{{Name: "foo", FullName: "custom.foo"}},
			}}, nil)
		r := &releaseTicketVarSVC{
			varstoreCli:   mockVarstoreCli,
			oreoManager:   pipelineSDK,
			changeItemSvc: changeItemSvc,
		}
		got, got1, err := r.getSinglePipelineVars(t.ctx, &entity.DBStagePipeline{
			PipelineType:    sharedpb.PipelineType_PIPELINE_TYPE_PROJECT.String(),
			PipelineID:      10,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bits.cd.api",
		})
		t.NoError(err)

		t.Equal([]*release_ticket_sharedpb.Variable{{
			Definition:       &varstorepb.VarDefinition{Name: "foo", FullName: "custom.foo"},
			AssociationItems: []*release_ticket_sharedpb.AssociationItem{newProjectPipelineAssociationItem(uint64(10), "bits.cd.api", sharedpb.ProjectType_PROJECT_TYPE_TCE.String(), "bits.cd.api")},
			GroupId:          1,
			Version:          2,
		}}, got)
		t.NotNil(got1)
	})
}

func (t *ReleaseTicketVarSuite) Test_getStagePipelinesRunVars() {
	t.db.Create(&entity.DBStagePipeline{
		StageID:         1,
		ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
		ProjectUniqueID: "bits.cd.api",
		PipelineType:    sharedpb.PipelineType_PIPELINE_TYPE_MAIN.String(),
		PipelineID:      10,
	})

	r := &releaseTicketVarSVC{
		stagePipelineDao: repository.NewStagePipelineDao(),
	}
	t.Run("stage not exist", func() {
		got1, got2, got3, err := r.getStagePipelinesRunVars(t.ctx, 2, sharedpb.ControlPlane_CONTROL_PLANE_CN, nil)
		t.NoError(err)
		t.Nil(got2)
		t.Nil(got1)
		t.Nil(got3)
	})
}

func (t *ReleaseTicketVarSuite) Test_FillLastRunVarsValues() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	mockVarstoreCli.EXPECT().
		ListAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(
			ctx context.Context,
			Req *varstorepb.ListAssignmentReq,
			callOptions ...callopt.Option,
		) (r *varstorepb.ListAssignmentResp, err error) {
			r = new(varstorepb.ListAssignmentResp)
			if len(Req.GetAssignmentIds()) == 0 {
				return nil, errors.New("no assignment")
			}

			for _, id := range Req.GetAssignmentIds() {
				if id == 0 {
					return nil, errors.New("assignment id is 0")
				}
			}

			for _, id := range Req.GetAssignmentIds() {
				if id == 1 {
					as1Assignment := []*varstorepb.VarAssignment{{
						Name:  "custom.test1",
						Value: testfactory.NewStringVarValue("text1"),
						DefSnapshot: &varstorepb.VarDefSnapshot{
							GroupId: 1,
							Version: 11,
						},
					}, {
						Name:  "custom.test2",
						Value: testfactory.NewStringVarValue("text2"),
						DefSnapshot: &varstorepb.VarDefSnapshot{
							GroupId: 1,
							Version: 11,
						},
					}}
					r.FlattenValues = append(r.FlattenValues, as1Assignment...)

					r.Assignments = append(r.Assignments, &varstorepb.Assignment{Assignments: as1Assignment})
				}

				if id == 2 {
					as2Assignment := []*varstorepb.VarAssignment{{
						Name:  "custom.test2_1",
						Value: testfactory.NewStringVarValue("text2_1"),
						DefSnapshot: &varstorepb.VarDefSnapshot{
							GroupId: 2,
							Version: 22,
						},
					}, {
						Name:  "custom.test2_2",
						Value: testfactory.NewStringVarValue("text2_2"),
						DefSnapshot: &varstorepb.VarDefSnapshot{
							GroupId: 2,
							Version: 22,
						},
					}}
					r.FlattenValues = append(r.FlattenValues, as2Assignment...)

					r.Assignments = append(r.Assignments, &varstorepb.Assignment{Assignments: as2Assignment})
				}
			}

			return r, nil
		}).AnyTimes()

	service := &releaseTicketVarSVC{
		varstoreCli: mockVarstoreCli,
	}
	t.Run("fill history inputs", func() {
		ctx := context.Background()
		releaseTicketVars := []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{FullName: "custom.test1"},
			GroupId:    1,
		}, {
			Definition: &varstorepb.VarDefinition{FullName: "custom.test3"},
			GroupId:    1,
		}}
		pipelineVars := []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{FullName: "custom.test2_2"},
			GroupId:    2,
		}, {
			Definition: &varstorepb.VarDefinition{FullName: "custom.test1"},
			GroupId:    2,
		}}
		stagePipelines := []*entity.DBStagePipeline{{
			PipelineType:     sharedpb.PipelineType_PIPELINE_TYPE_MAIN.String(),
			PipelineID:       100,
			VarsAssignmentID: 1,
		}, {
			PipelineType:     sharedpb.PipelineType_PIPELINE_TYPE_PROJECT.String(),
			PipelineID:       101,
			VarsAssignmentID: 2,
		}}
		vars, err := service.FillLastRunVarsValues(ctx, append(releaseTicketVars, pipelineVars...), stagePipelines)
		t.NoError(err)

		for _, v := range vars {
			if v.GetDefinition().GetFullName() == "custom.test1" && v.GetGroupId() == 1 {
				t.True(reflect.DeepEqual(v.GetValue(), testfactory.NewStringVarValue("text1")))
			} else if v.GetDefinition().GetFullName() == "custom.test1" && v.GetGroupId() == 2 {
				t.Nil(v.GetValue())
			} else if v.GetDefinition().GetFullName() == "custom.test2_2" {
				t.True(reflect.DeepEqual(v.GetValue(), testfactory.NewStringVarValue("text2_2")))
			} else if v.GetDefinition().GetFullName() == "custom.test3" {
				t.Nil(v.GetValue())
			} else {
				t.Fail("unexpected var")
			}
		}
	})

	t.Run("no assignment id", func() {
		ctx := context.Background()
		releaseTicketVars := []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{FullName: "custom.test1"},
			GroupId:    1,
		}, {
			Definition: &varstorepb.VarDefinition{FullName: "custom.test3"},
			GroupId:    1,
		}}
		pipelineVars := []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{FullName: "custom.test2_2"},
			GroupId:    2,
		}, {
			Definition: &varstorepb.VarDefinition{FullName: "custom.test1"},
			GroupId:    2,
		}}
		stagePipelines := []*entity.DBStagePipeline{{
			PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN.String(),
			PipelineID:   100,
		}, {
			PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT.String(),
			PipelineID:   101,
		}}
		vars, err := service.FillLastRunVarsValues(ctx, append(releaseTicketVars, pipelineVars...), stagePipelines)
		t.NoError(err)
		for _, v := range vars {
			t.Nil(v.GetValue())
		}
	})
}

func (t *ReleaseTicketVarSuite) Test_GetVarsInKeys() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	ctx := context.Background()
	t.Run("get vars in keys", func() {
		r := &releaseTicketVarSVC{}
		vars := []*release_ticket_sharedpb.Variable{
			&release_ticket_sharedpb.Variable{
				Definition: &varstorepb.VarDefinition{
					Name:     "a",
					FullName: "custom.a",
				},
			},
		}
		keys := []string{"custom", "custom.a", "custom.b"}
		got := r.getVarsInKeys(ctx, vars, keys)
		t.Equal(vars, got)
	})
	t.Run("get no vars in keys", func() {
		r := &releaseTicketVarSVC{}
		vars := []*release_ticket_sharedpb.Variable{
			&release_ticket_sharedpb.Variable{
				Definition: &varstorepb.VarDefinition{
					Name:     "a",
					FullName: "custom.a",
				},
			},
		}
		expectedVars := make([]*release_ticket_sharedpb.Variable, 0)
		keys := []string{"custom", "custom.b"}
		got := r.getVarsInKeys(ctx, vars, keys)

		t.Equal(expectedVars, got)
	})
}

func (t *ReleaseTicketVarSuite) Test_GetAvailableVars() {
	r := &releaseTicketVarSVC{}
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	t.Run("get sys vars", func() {
		p1 := monkey.PatchInstanceMethod(reflect.TypeOf(pvariable.GetVarService()), "GetAvailableVars", func(ctx context.Context, workspaceId uint64,
			pipelineId, templateId, devTaskId, releaseTicketId uint64, providers []varstorepb.SysProvider) (*servicepb.GetAvailableVarsResponse, error) {
			return &servicepb.GetAvailableVarsResponse{
				SystemVars: []*varstorepb.AvailableVar{
					&varstorepb.AvailableVar{
						GroupId: 1,
						Version: 1,
						VarDefinition: &varstorepb.VarDefinition{
							FullName: "sys.release_ticket.a",
						},
					},
				},
			}, nil
		})
		defer p1.Unpatch()

		got, err := r.GetAvailableVars(t.ctx, &release_ticket_sharedpb.GetAvailableVarsReq{
			SpaceId: 1,
		})
		t.NoError(err)
		t.NotNil(got)
	})
	t.Run("get vars", func() {
		p1 := monkey.PatchInstanceMethod(reflect.TypeOf(pvariable.GetVarService()), "GetAvailableVars", func(ctx context.Context, workspaceId uint64,
			pipelineId, templateId, devTaskId, releaseTicketId uint64, providers []varstorepb.SysProvider) (*servicepb.GetAvailableVarsResponse, error) {
			return &servicepb.GetAvailableVarsResponse{
				CustomVars: []*varstorepb.AvailableVar{
					&varstorepb.AvailableVar{
						GroupId: 1,
						Version: 1,
						VarDefinition: &varstorepb.VarDefinition{
							FullName: "custom.a",
						},
					},
				},
				SystemVars: []*varstorepb.AvailableVar{
					&varstorepb.AvailableVar{
						GroupId: 1,
						Version: 1,
						VarDefinition: &varstorepb.VarDefinition{
							FullName: "sys.release_ticket.a",
						},
					},
				},
			}, nil
		})
		defer p1.Unpatch()
		got, err := r.GetAvailableVars(t.ctx, &release_ticket_sharedpb.GetAvailableVarsReq{
			SpaceId:                 1,
			ReleaseTicketWorkflowId: 1,
		})
		t.NoError(err)
		t.NotNil(got)

		got, err = r.GetAvailableVars(t.ctx, &release_ticket_sharedpb.GetAvailableVarsReq{
			SpaceId:           1,
			DevTaskWorkflowId: 1,
		})
		t.NoError(err)
		t.NotNil(got)
	})
}

func (t *ReleaseTicketVarSuite) Test_useByteTreeVarsInSpace() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	spaceCliMock := spacerpcapi_mock.NewMockClient(ctrl)
	spaceCliMock.EXPECT().GetSpaceInfoByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&spacerpcpb.SpaceDetail{
			Id:    1,
			Extra: "",
		}, nil)
	r := &releaseTicketVarSVC{
		spaceCli: spaceCliMock,
	}

	got, err := r.useByteTreeVarsInSpace(ctx, 1)
	t.NoError(err)
	t.Equal(false, got)
}
