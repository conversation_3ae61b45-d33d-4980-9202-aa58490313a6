package rt_flow_task_service

import (
	"context"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	"code.byted.org/bits/monkey"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	change_item_mock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/mock"
	released_commit_managermock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/released_commit_manager/mock"
	mock_rt_orchestration "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_orchestration/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_stage"
	mock_rt_stage "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_stage/mock"
	mock_stage_check "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/stage_check/mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/iesarch/cdaas_utils/erri"
)

type DeployStageTaskTestSuite struct {
	suite.Suite
	ctx      context.Context
	cancel   context.CancelFunc
	mockCtrl *gomock.Controller
}

func (t *DeployStageTaskTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	t.mockCtrl = gomock.NewController(t.T())

	rt_stage.MustInit()
}

func (t *DeployStageTaskTestSuite) TearDownTest() {
	t.cancel()
	monkey.UnpatchAll()
}

func (t *DeployStageTaskTestSuite) Test_AccessTaskStart() {
	baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
	rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

	rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
		StageID:  1,
		NodeType: workflowpb.NodeType_NODE_TYPE_ARCHIVE_STAGE.String(),
	}, nil)
	deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
	deployStageSvc.EXPECT().StageAccessTask(gomock.Any(), gomock.Any()).Return(false, nil)
	svc := &deployStageTaskSvc{
		baseStageManager: baseStageManager,
		deployStageSvc:   deployStageSvc,
		rtOrchestration:  rtOrchestrationMock,
	}
	_, err := svc.AccessTaskStart(t.ctx, 1, &DeployStageTaskRequest{})
	t.NoError(err)
}

func (t *DeployStageTaskTestSuite) Test_AccessTaskPing() {
	t.Run("CheckArtifactReady not pass", func() {
		baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
		rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

		rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID:  1,
			NodeType: workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		}, nil)
		baseStageManager.EXPECT().CheckArtifactReady(gomock.Any(), gomock.Any()).Return(false, nil)
		svc := &deployStageTaskSvc{
			baseStageManager: baseStageManager,
			rtOrchestration:  rtOrchestrationMock,
		}
		pass, err := svc.AccessTaskPing(t.ctx, 1, &DeployStageTaskRequest{})
		t.NoError(err)
		t.Equal(false, pass)
	})

	t.Run("CheckArtifactReady pass, check not pass", func() {
		baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
		rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

		rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID:  1,
			NodeType: workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		}, nil)
		baseStageManager.EXPECT().StageAccessCheckTask(gomock.Any(), gomock.Any()).Return(false, nil)
		baseStageManager.EXPECT().CheckArtifactReady(gomock.Any(), gomock.Any()).Return(true, nil)
		svc := &deployStageTaskSvc{
			baseStageManager: baseStageManager,
			rtOrchestration:  rtOrchestrationMock,
		}
		pass, err := svc.AccessTaskPing(t.ctx, 1, &DeployStageTaskRequest{})
		t.NoError(err)
		t.Equal(false, pass)
	})

	t.Run("CheckArtifactReady pass, check pass", func() {
		baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
		rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

		rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID:  1,
			NodeType: workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		}, nil)
		baseStageManager.EXPECT().StageAccessCheckTask(gomock.Any(), gomock.Any()).Return(true, nil)
		baseStageManager.EXPECT().CheckArtifactReady(gomock.Any(), gomock.Any()).Return(true, nil)
		svc := &deployStageTaskSvc{
			baseStageManager: baseStageManager,
			rtOrchestration:  rtOrchestrationMock,
		}
		pass, err := svc.AccessTaskPing(t.ctx, 1, &DeployStageTaskRequest{})
		t.NoError(err)
		t.Equal(true, pass)
	})
}

func (t *DeployStageTaskTestSuite) Test_FinishStart() {
	baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
	rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

	rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
		StageID:         1,
		NodeType:        workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		ReleaseTicketID: 1,
	}, nil)
	baseStageManager.EXPECT().SendReleaseTicketStageTransferLarkNotice(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
	deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
	deployStageSvc.EXPECT().StageFinishTask(gomock.Any(), gomock.Any()).Return(nil)
	releasedCommitManager := released_commit_managermock.NewMockReleasedCommitManager(t.mockCtrl)
	releasedCommitManager.EXPECT().CompensateReleasedCommitByReleaseTicketID(gomock.Any(), gomock.Any()).Return(nil)
	stageCheckManager := mock_stage_check.NewMockStageCheckSvc(t.mockCtrl)
	stageCheckManager.EXPECT().TerminateStageGatekeeperCheckPoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	svc := &deployStageTaskSvc{
		baseStageManager:      baseStageManager,
		deployStageSvc:        deployStageSvc,
		releasedCommitManager: releasedCommitManager,
		stageCheckSvc:         stageCheckManager,
		rtOrchestration:       rtOrchestrationMock,
	}
	err := svc.FinishStart(t.ctx, 1, &DeployStageTaskRequest{})
	t.NoError(err)
}

func (t *DeployStageTaskTestSuite) Test_ForceCompleteStart() {
	baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
	rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

	rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
		StageID:         1,
		NodeType:        workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		ReleaseTicketID: 1,
	}, nil)
	defer monkey.Patch(redis.GetWithResult).Return("aa", nil).UnPatch()
	deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
	deployStageSvc.EXPECT().StageForceFinishTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	svc := &deployStageTaskSvc{
		baseStageManager: baseStageManager,
		deployStageSvc:   deployStageSvc,
		rtOrchestration:  rtOrchestrationMock,
	}
	err := svc.ForceCompleteStart(t.ctx, 1, &DeployStageTaskRequest{})
	t.NoError(err)
}

func (t *DeployStageTaskTestSuite) Test_PplCreateTaskStart() {
	t.Run("not autoPass", func() {
		baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
		rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

		rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID:  1,
			NodeType: workflowpb.NodeType_NODE_TYPE_ARCHIVE_STAGE.String(),
		}, nil)
		deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
		deployStageSvc.EXPECT().StagePipelineCreateTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		changeItemSvc := change_item_mock.NewMockChangeItemSvc(t.mockCtrl)
		changeItemSvc.EXPECT().GetBaselineChangeItemDependencies(gomock.Any(), gomock.Any()).Return(nil, nil)

		svc := &deployStageTaskSvc{
			baseStageManager: baseStageManager,
			deployStageSvc:   deployStageSvc,
			changeItemSvc:    changeItemSvc,
			rtOrchestration:  rtOrchestrationMock,
		}
		err := svc.PplCreateTaskStart(t.ctx, 1)
		t.NoError(err)
	})

	t.Run("autoPass", func() {
		baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
		rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)

		rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID:  1,
			NodeType: workflowpb.NodeType_NODE_TYPE_ARCHIVE_STAGE.String(),
		}, nil)
		deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
		deployStageSvc.EXPECT().StagePipelineCreateTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		changeItemSvc := change_item_mock.NewMockChangeItemSvc(t.mockCtrl)
		changeItemSvc.EXPECT().GetBaselineChangeItemDependencies(gomock.Any(), gomock.Any()).Return(nil, nil)

		svc := &deployStageTaskSvc{
			baseStageManager: baseStageManager,
			deployStageSvc:   deployStageSvc,
			rtOrchestration:  rtOrchestrationMock,
			changeItemSvc:    changeItemSvc,
		}
		err := svc.PplCreateTaskStart(t.ctx, 1)
		t.NoError(err)
	})
}

func (t *DeployStageTaskTestSuite) Test_ExitTaskPing() {
	releaseTicketDao := repositorymock.NewMockReleaseTicketDao(t.mockCtrl)
	releaseTicketSvc := mock.NewMockReleaseTicketSvc(t.mockCtrl)
	t.Run("处于限流间隔", func() {
		defer monkey.Patch(redis.GetWithResult, func(key string) (string, error) {
			return "1", nil
		}).UnPatch()
		svc := &deployStageTaskSvc{}
		pass, err := svc.ExitTaskPing(t.ctx, 1)
		t.NoError(err)
		t.Equal(pass, false)
	})

	t.Run("判断流水线状态", func() {
		defer monkey.Patch(redis.GetWithResult, func(key string) (string, error) {
			return "", erri.Errorf("redis get error")
		}).UnPatch()
		defer monkey.Patch(redis.SetNxWithResult, func(key string, val []byte, expiration time.Duration) (bool, error) {
			return true, nil
		}).UnPatch()
		releaseTicketDao.EXPECT().GetBasicByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{ReleaseTicketID: 1}, nil)
		releaseTicketSvc.EXPECT().IsDeployStageReadyToCompleted(gomock.Any(), gomock.Any()).Return(true, nil)
		svc := &deployStageTaskSvc{
			releaseTicketDao: releaseTicketDao,
			releaseTicketSvc: releaseTicketSvc,
		}
		pass, err := svc.ExitTaskPing(t.ctx, 1)
		t.NoError(err)
		t.Equal(pass, true)
	})
}

func (t *DeployStageTaskTestSuite) Test_EnterTaskStart() {
	baseStageManager := mock_rt_stage.NewMockBaseStageManager(t.mockCtrl)
	rtOrchestrationMock := mock_rt_orchestration.NewMockRtOrchestration(t.mockCtrl)
	rtOrchestrationMock.EXPECT().FindDeployStage(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
		StageID:         1,
		NodeType:        workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String(),
		ReleaseTicketID: 1,
	}, nil)
	baseStageManager.EXPECT().SendReleaseTicketStageTransferLarkNotice(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
	deployStageSvc := mock_rt_stage.NewMockDeployStageSvc(t.mockCtrl)
	deployStageSvc.EXPECT().StageEnterTask(gomock.Any(), gomock.Any()).Return(nil)

	releaseTicketSvc := mock.NewMockReleaseTicketSvc(t.mockCtrl)
	releaseTicketSvc.EXPECT().UpdateMainScmBranchToReleaseBranch(gomock.Any(), gomock.Any()).Return(nil)
	stageCheckManager := mock_stage_check.NewMockStageCheckSvc(t.mockCtrl)
	stageCheckManager.EXPECT().TerminateStageGatekeeperCheckPoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	svc := &deployStageTaskSvc{
		baseStageManager: baseStageManager,
		deployStageSvc:   deployStageSvc,
		releaseTicketSvc: releaseTicketSvc,
		stageCheckSvc:    stageCheckManager,
		rtOrchestration:  rtOrchestrationMock,
	}
	err := svc.EnterTaskStart(t.ctx, 1, &DeployStageTaskRequest{})
	t.NoError(err)
}
