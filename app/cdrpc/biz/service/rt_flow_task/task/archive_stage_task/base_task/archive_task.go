package archive_stage_task

import (
	"context"

	rt_flow_task_service "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/service"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/task/base"
	"code.byted.org/gopkg/logs/v2"
)

type ReleaseArchiveStageArchiveTask struct {
	base.ServiceTask

	ArchiveStageTaskSvc rt_flow_task_service.ArchiveStageTaskSvc
}

func NewReleaseArchiveStageArchiveTask() ReleaseArchiveStageArchiveTask {
	return ReleaseArchiveStageArchiveTask{
		ArchiveStageTaskSvc: rt_flow_task_service.NewArchiveStageTaskSvc(),
	}
}

func (t *ReleaseArchiveStageArchiveTask) Start(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	return true, nil, nil
}

func (t *ReleaseArchiveStageArchiveTask) Reset(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	return true, nil, nil
}

func (t *ReleaseArchiveStageArchiveTask) Close(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	return true, nil, nil
}

func (t *ReleaseArchiveStageArchiveTask) Ping(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	ctx = logs.CtxAddKVs(ctx, "ReleaseArchiveStageArchiveTask_id", req.ReleaseTicketID)

	archiveTaskReq := &rt_flow_task_service.ArchiveStageTaskRequest{
		IsIncrementalDelivery: req.IsIncrementalDelivery,
	}

	pass, err := t.ArchiveStageTaskSvc.ArchiveTaskPing(ctx, uint64(req.ReleaseTicketID), archiveTaskReq)
	if err != nil {
		logs.CtxError(ctx, "ReleaseArchiveStageArchiveTask ping fail, err: %s", err.Error())
		return false, nil, err
	}

	return pass, nil, nil
}
