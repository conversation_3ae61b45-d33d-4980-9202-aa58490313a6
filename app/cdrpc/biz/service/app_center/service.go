package app_center

import (
	"context"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/app_centerpb"
	"code.byted.org/devinfra/hagrid/pkg/envplatform"
)

//go:generate mockgen -destination mock/service_mock.go -package mock code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/app_center AppCenterSvc

type AppCenterSvc interface {
	GetHybridChannelList(ctx context.Context, req *app_centerpb.GetHybridChannelListReq) (resp *app_centerpb.GetHybridChannelListResp, err error)
}

type appCenter struct {
	envSDK envplatform.SDK
}

func NewAppCenterSvc() AppCenterSvc {
	return &appCenter{
		envSDK: envplatform.DefaultSDK(),
	}
}
