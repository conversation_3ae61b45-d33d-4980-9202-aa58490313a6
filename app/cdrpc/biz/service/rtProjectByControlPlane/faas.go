package rtProjectByControlPlane

import (
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/base_project"
	_default "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/faas/default"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/faas/eu_ttp"
	i18n_bd "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/faas/i18n_bd"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/faas/us_ttp"
)

func NewFaasDefaultProject() base_project.RtProjectControlPlane {
	return &_default.FaasDefaultProject{}
}

func NewFaasI18nbdProject() base_project.RtProjectControlPlane {
	return &i18n_bd.FaasI18nbdProject{}
}

func NewFaasEUTTPProject() base_project.RtProjectControlPlane {
	return &eu_ttp.FaasEUTTPProject{
		CrossborderSDK: crossborder.NewCrossborderSDK(),
	}
}

func NewFaasUSTTPProject() base_project.RtProjectControlPlane {
	return &us_ttp.FaasUSTTPProject{
		CrossborderSDK: crossborder.NewCrossborderSDK(),
	}
}
