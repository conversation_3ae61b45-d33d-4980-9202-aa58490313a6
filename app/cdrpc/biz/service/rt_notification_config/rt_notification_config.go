package rt_notification_config

import (
	"context"
	"strconv"

	"github.com/bytedance/sonic"
	json "github.com/bytedance/sonic"
	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	meegoAPI "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/meego"
	"code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/meego"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gslice"
)

const (
	CDWorkItemType_Feature  = "feature"
	CDWorkItemType_Issue    = "issue"
	CDWorkItemType_Custom   = "custom"
	MeegoWorkitemType_Story = "story"
	MeegoWorkitemType_Issue = "issue"
	bitsBotAppid            = "cli_9e96a6d621a89102"
)

type NotificationTargetInfo struct {
	Usernames []string
	ChatIds   []string
}

func (h *rtNotificationConfig) GetReleaseTicketNotificationTargetInfo(ctx context.Context, releaseTicketId uint64, eventName string) (*NotificationTargetInfo, error) {
	releaseTicketInfo, err := h.releaseTicketDao.GetByReleaseTicketID(ctx, releaseTicketId)
	if err != nil {
		return nil, err
	}
	notificationConfig, err := h.configServiceSvc.GetOnesiteSpaceNotificationConfig(ctx, int64(releaseTicketInfo.WorkspaceID), eventName)
	if err != nil {
		return nil, err
	}
	if notificationConfig.GetConfig() == nil || notificationConfig.GetConfig().GetNotify() == nil {
		return nil, errors.Errorf("notification config is empty")
	}
	// 未开启返回空结构体
	if !notificationConfig.Config.GetOpen() {
		return &NotificationTargetInfo{}, nil
	}
	releaseApprovers, err := h.releaseApproverDao.GetByReleaseTicketID(ctx, releaseTicketId, release_ticketpb.ReleaseTicketApproverRole_RELEASE_TICKET_APPROVER_ROLE_RELEASE.String())
	if err != nil {
		return nil, err
	}
	testApprovers, err := h.releaseApproverDao.GetByReleaseTicketID(ctx, releaseTicketId, release_ticketpb.ReleaseTicketApproverRole_RELEASE_TICKET_APPROVER_ROLE_TEST.String())
	if err != nil {
		return nil, err
	}
	usernames, err := h.getNotificationUsernames(ctx, notificationConfig.GetConfig().GetNotify(), releaseTicketId, releaseApprovers, testApprovers)
	if err != nil {
		return nil, err
	}
	chatIds, err := h.getNotificationLarkGroupIds(ctx, notificationConfig.GetConfig().GetNotify(), releaseTicketInfo)
	if err != nil {
		return nil, err
	}
	return &NotificationTargetInfo{
		Usernames: usernames,
		ChatIds:   chatIds,
	}, nil
}

func (h *rtNotificationConfig) getNotificationLarkGroupIds(ctx context.Context, config *config_service.OnesiteSpaceNotificationConfigNotify, releaseTicket *entity.DBReleaseTicket) ([]string, error) {

	var chatIds []string
	// 需要发送至发布单关联群
	if config.GetSendToReleaseTicketRelatedChatId() && len(releaseTicket.LarkGroupIDs) > 0 {
		var releaseTicketLarkGroupIds []string
		err := sonic.Unmarshal([]byte(releaseTicket.LarkGroupIDs), &releaseTicketLarkGroupIds)
		if err == nil {
			chatIds = append(chatIds, releaseTicketLarkGroupIds...)
		}
	}

	// 需要发布至空间关联群
	if config.GetSendToSpaceRelatedChatId() {
		spaceGroupConfig, err := h.configServiceSvc.GetOnesiteSpaceLarkGroupConfig(ctx, int64(releaseTicket.WorkspaceID))
		if err == nil {
			chatIds = append(chatIds, spaceGroupConfig.GetConfig().GetSpaceLarkGroups()...)
		}
	}

	// 将发布单的通知，发送到绑定的 meego 群里
	if config.GetSendToReleaseTicketMeegoChatId() {
		if len(releaseTicket.WorkItems) > 0 {
			var rtWorkItems []*release_ticketpb.WorkItem
			err := json.UnmarshalString(releaseTicket.WorkItems, &rtWorkItems)
			if err != nil {
				logs.CtxError(ctx, "UnmarshalFromString error: %+v", err)
			} else {
				for _, rtWorkItem := range rtWorkItems {
					var workItemTypeKey string
					if workItemTypeKey, err = transCDWorkItemTypeToMeegoWorkItemType(rtWorkItem.Type); err != nil {
						logs.CtxError(ctx, "transCDWorkItemTypeToMeegoWorkItemType error: %+v", err)
						continue
					}
					req := &meego.InviteBotIntoChatRequest{
						AppIds:          []string{bitsBotAppid},
						WorkItemTypeKey: workItemTypeKey,
					}
					wid, err := strconv.ParseInt(rtWorkItem.Id, 10, 64)
					if err != nil {
						logs.CtxError(ctx, "err: %s", err.Error())
						continue
					}
					log.V2.Info().With(ctx).Str("getNotificationLarkGroupIds InviteBotIntoChat").KVs(
						"projectKey", rtWorkItem.SpaceKey, "workItemId", wid, "req", req).Emit()
					// 拉 bits 机器人进群
					resp, inviteErr := meegoAPI.MeegoClient.InviteBotIntoChat(ctx, "bits", rtWorkItem.SpaceKey, wid, req)
					log.V2.Info().With(ctx).Str(" getNotificationLarkGroupIds InviteBotIntoChat").KV("resp", resp).Emit()
					if inviteErr != nil || resp == nil {
						logs.CtxError(ctx, "invite bits bot into meego chat error: %v", inviteErr)
						continue
					}
					chatIds = append(chatIds, resp.ChatId)
				}
			}
		}
	}

	return set.New(chatIds...).ToSlice(), nil
}

func (h *rtNotificationConfig) getNotificationUsernames(ctx context.Context, config *config_service.OnesiteSpaceNotificationConfigNotify, releaseTicketId uint64, releaseApprovers []*entity.DBReleaseTicketReleaseApprover, testApprovers []*entity.DBReleaseTicketReleaseApprover) ([]string, error) {
	usernames := make([]string, 0)
	for _, role := range config.Roles {
		if !role.Notify {
			continue
		}
		switch role.GetRole() {
		case config_service.OnesiteSpaceNotificationEventRole_spaceAdminAndOwner:
			break
		case config_service.OnesiteSpaceNotificationEventRole_releaseTicketOwner:
			usernames = append(usernames, gslice.Map(releaseApprovers, func(f *entity.DBReleaseTicketReleaseApprover) string {
				return f.Username
			})...)
		case config_service.OnesiteSpaceNotificationEventRole_releaseTicketQualityOwner:
			usernames = append(usernames, gslice.Map(testApprovers, func(f *entity.DBReleaseTicketReleaseApprover) string {
				return f.Username
			})...)
		case config_service.OnesiteSpaceNotificationEventRole_releaseTicketProjectOwner:
			changeItems, err := h.releaseTicketChangeItemDao.GetByReleaseTicketID(ctx, releaseTicketId)
			if err != nil {
				logs.CtxError(ctx, "get release change item error %s", err.Error())
				continue
			}
			projectOwners := gslice.FlatMap(changeItems, func(f *entity.DBReleaseTicketChangeItem) []string {
				owners := make([]string, 0)
				if f.ProjectOwners == "" {
					return nil
				}
				iErr := sonic.Unmarshal([]byte(f.ProjectOwners), &owners)
				if iErr != nil {
					logs.CtxError(ctx, "Unmarshal ProjectOwners error %s", err.Error())
					return nil
				}
				return owners
			})
			usernames = append(usernames, projectOwners...)
		}
	}
	usernames = append(usernames, config.Usernames...)
	return set.New(usernames...).ToSlice(), nil
}

func transCDWorkItemTypeToMeegoWorkItemType(rtWorkItemType string) (string, error) {
	var workItemTypeKey string
	switch rtWorkItemType {
	case CDWorkItemType_Feature:
		workItemTypeKey = MeegoWorkitemType_Story
	case CDWorkItemType_Issue:
		workItemTypeKey = MeegoWorkitemType_Issue
	default:
		// 其他的类型透传
		return rtWorkItemType, nil
	}
	return workItemTypeKey, nil
}
