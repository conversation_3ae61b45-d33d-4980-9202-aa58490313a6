load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "branch_del",
    srcs = [
        "create.go",
        "index.go",
        "submit.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/biz/branch_del",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/biz/dal/mysql",
        "//app/cdrpc/biz/dal/mysql/entity",
        "//app/cdrpc/biz/dal/mysql/repository",
        "//app/cdrpc/biz/dal/redis",
        "//app/cdrpc/biz/domain/release_ticket",
        "//app/cdrpc/biz/pkg/auth",
        "//app/cdrpc/biz/pkg/env",
        "//app/cdrpc/biz/pkg/idgen",
        "//app/cdrpc/biz/pkg/integrate_sdk",
        "//app/cdrpc/biz/pkg/tce",
        "//app/cdrpc/biz/service/branch_manager",
        "//app/cdrpc/biz/service/change_item",
        "//app/cdrpc/biz/service/config",
        "//app/cdrpc/biz/service/rt_operation_reporter",
        "//app/cdrpc/biz/service/rtproject",
        "//app/cdrpc/kitex_gen/bytedance/bits/git_server",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//libs/bits_err",
        "//pkg/canal_provider/crossborder",
        "//pkg/envplatform",
        "@com_github_avast_retry_go//:retry-go",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_cloudwego_kitex//pkg/kerrors",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_lang_v2//slicex",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_iesarch_cdaas_utils//erri",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_overpass_bits_integration_multi//kitex_gen/bits/integration/multi",
        "@org_golang_x_sync//errgroup",
    ],
)
