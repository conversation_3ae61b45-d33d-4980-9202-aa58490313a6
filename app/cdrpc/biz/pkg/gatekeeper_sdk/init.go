package gatekeeper_sdk

import (
	"code.byted.org/kite/kitex/byted/transmeta"
	"code.byted.org/kite/kitex/client"
	bits_gatekeeper_process_raw "code.byted.org/overpass/bits_gatekeeper_process/rpc/bits_gatekeeper_process"
)

var bits_gatekeeper_process bits_gatekeeper_process_raw.OverpassClient

func MustInitialize() {
	transmeta.SetReadBizStatusErr(true)
	bits_gatekeeper_process_client, err := bits_gatekeeper_process_raw.NewClient("bits.gatekeeper.process", newRPCOptions()...)
	if err != nil {
		panic(err)
	}
	bits_gatekeeper_process_client.Conf().EnableErrHandler = false
	bits_gatekeeper_process = bits_gatekeeper_process_client
}

func newRPCOptions() []client.Option {
	var options []client.Option
	return options
}
