package releaseticketdomain

import (
	json "github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/projectpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
)

type ChangeItemPBModel struct {
	*release_ticketpb.ReleaseTicketChangeItem
}

type ChangeItemChangeItemUniqKeyType = string

func NewChangeItemPBModel(item *release_ticketpb.ReleaseTicketChangeItem) *ChangeItemPBModel {
	return &ChangeItemPBModel{item}
}

func (m *ChangeItemPBModel) UniqueKeyWithoutCP() string {
	return ChangeItemUniqKeyWithoutCP(m.GetProjectType(), m.GetProjectUniqueId())
}

// UniqueKey 在不同控制面下的唯一键
func (m *ChangeItemPBModel) UniqueKey() string {
	return ChangeItemUniqKey(m.ProjectType, m.ProjectUniqueId, m.ControlPlane)
}

type ChangeItemModel struct {
	*entity.DBReleaseTicketChangeItem
}

func NewChangeItemModel(item *entity.DBReleaseTicketChangeItem) *ChangeItemModel {
	return &ChangeItemModel{item}
}

func (m *ChangeItemModel) ProjectOwners() []string {
	var projectOwners []string
	if m.DBReleaseTicketChangeItem.ProjectOwners != "" {
		err := json.UnmarshalString(m.DBReleaseTicketChangeItem.ProjectOwners, &projectOwners)
		if err != nil {
			logs.Error("unmarshal change item project owners error: %s", err.Error())
		}
	}
	return projectOwners
}

// UniqueKey 在不同控制面下的唯一键
func (m *ChangeItemModel) UniqueKey() string {
	return ChangeItemUniqKey(
		sharedpb.ProjectType(sharedpb.ProjectType_value[m.ProjectType]),
		m.ProjectUniqueID,
		sharedpb.ControlPlane(sharedpb.ControlPlane_value[m.ControlPlane]),
	)
}

// UniqueKeyWithoutCP 忽略控制面情况下的唯一键
func (m *ChangeItemModel) UniqueKeyWithoutCP() string {
	return ChangeItemUniqKeyWithoutCP(
		sharedpb.ProjectType(sharedpb.ProjectType_value[m.ProjectType]),
		m.ProjectUniqueID,
	)
}

// ChangeItemUniqKeyWithoutCP 忽略控制面情况下的唯一键
func ChangeItemUniqKeyWithoutCP(projectType sharedpb.ProjectType, projectUniqID string) string {
	return projectUniqID + "|" + projectType.String()
}

// ChangeItemUniqKey 在不同控制面下的唯一键
func ChangeItemUniqKey(projectType sharedpb.ProjectType, projectUniqID string, cp sharedpb.ControlPlane) ChangeItemChangeItemUniqKeyType {
	return projectUniqID + "|" + projectType.String() + cp.String()
}

func (m *ChangeItemModel) ToProjectPB() *projectpb.ProjectBasicInfoItem {
	return &projectpb.ProjectBasicInfoItem{
		ProjectUniqueId: m.ProjectUniqueID,
		ProjectType:     sharedpb.ProjectType(sharedpb.ProjectType_value[m.ProjectType]),
		ControlPlane:    sharedpb.ControlPlane(sharedpb.ControlPlane_value[m.ControlPlane]),
	}
}
