package redis

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/idgen"
	"code.byted.org/gopkg/logs"
)

var GetLockFailErr = fmt.Errorf("get lock fail")

type SpinLock interface {
	Lock(ctx context.Context) error
	UnLock(ctx context.Context) error
	IsLock(ctx context.Context) bool
}

type LockInfo struct {
	lockKey        string
	lockValue      string
	lockFlag       bool
	lockExpireTime time.Duration
	lockRetryTimes int
	RetryInterval  time.Duration
}

func NewDeploymentMutexLock(ctx context.Context, projectKey string, scopeHash string) SpinLock {
	lockKey := fmt.Sprintf("deployment_mutex_lock_%s_%s", projectKey, scopeHash)
	lockValue, _ := idgen.GetID(ctx)
	return &LockInfo{
		lockKey:        lockKey,
		lockValue:      fmt.Sprintf("%d", lockValue),
		lockFlag:       false,
		lockExpireTime: 2 * time.Second,
		lockRetryTimes: 10,
		RetryInterval:  80 * time.Millisecond,
	}
}

func (l *LockInfo) Lock(ctx context.Context) error {
	if l.lockFlag {
		logs.CtxInfo(ctx, "already lock")
		return nil
	}
	for i := 0; i < l.lockRetryTimes; i++ {
		lockFlag, err := _redisClient.Cas2NX(l.lockKey, "", l.lockValue, l.lockExpireTime).Result()
		if lockFlag <= 0 {
			if err != nil {
				logs.CtxError(ctx, "%s set nx err:%v", l.lockKey, err.Error())
			}
			time.Sleep(l.RetryInterval)
			continue
		}
		logs.CtxInfo(ctx, "success lock %s", l.lockKey)
		l.lockFlag = true
		return nil
	}
	logs.CtxError(ctx, "%s not get lock in %d retry times", l.lockKey, l.lockRetryTimes)
	return GetLockFailErr
}

func (l *LockInfo) UnLock(ctx context.Context) error {
	if !l.lockFlag {
		logs.CtxInfo(ctx, "already unlock")
		return nil
	}
	err := _redisClient.Cad(l.lockKey, l.lockValue).Err()
	if err != nil {
		logs.CtxError(ctx, "%s unlock err:%v", l.lockKey, err)
		return err
	}
	logs.CtxInfo(ctx, "success unlock %s", l.lockKey)
	l.lockFlag = false
	return nil
}

func (l *LockInfo) IsLock(ctx context.Context) bool {
	if l.lockFlag {
		return true
	}
	v, _ := _redisClient.Get(l.lockKey).Result()
	return len(v) != 0
}

type MockLock struct{}

func (m MockLock) Lock(ctx context.Context) error   { return nil }
func (m MockLock) UnLock(ctx context.Context) error { return nil }
func (m MockLock) IsLock(ctx context.Context) bool  { return false }
