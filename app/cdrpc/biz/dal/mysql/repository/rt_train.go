package repository

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/iesarch/cdaas_utils/erri"
)

type rtTrainDao struct {
}

//go:generate mockgen -destination mock/rt_train.go -package repositorymock -source rt_train.go . RTTrainDao
type RTTrainDao interface {
	List(ctx context.Context, param *entity.ListRtTrainParam) ([]*entity.RtTrain, int64, error)
}

func NewTRTrainDao() RTTrainDao {
	return &rtTrainDao{}
}

func (d *rtTrainDao) List(ctx context.Context, param *entity.ListRtTrainParam) ([]*entity.RtTrain, int64, error) {
	db, err := mysql.GetDefaultConn(ctx)
	if err != nil {
		return nil, 0, erri.Error(err)
	}
	limit := param.PageSize
	offset := (param.Page - 1) * param.PageSize
	r := make([]*entity.RtTrain, 0)
	count := int64(0)

	db = db.WithContext(ctx).
		Where("workspace_id in (?)", param.WorkspaceID)

	if len(param.Status) != 0 {
		db = db.Where("status =?", param.Status)
	}
	if param.CreateAt != 0 {
		db = db.Where("created_at > ?", param.CreateAt)
	}
	if param.Count {
		if err = db.Model(&entity.RtTrain{}).Count(&count).Error; err != nil {
			return nil, 0, erri.Error(err)
		}
	}

	if err = db.
		Limit(int(limit)).
		Offset(int(offset)).
		Find(&r).Order("created_at DESC").Error; err != nil {
		return nil, 0, erri.Error(err)
	}
	return r, count, nil
}
