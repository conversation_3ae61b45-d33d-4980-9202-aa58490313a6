load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = [
        "associate_workflow.go",
        "bc_flow_to_bits_migration_record.go",
        "bc_flow_to_bits_migration_result.go",
        "branch_sync_task_mock.go",
        "branching_model_config_mock.go",
        "check_meta_mock.go",
        "deployment_lock_mock.go",
        "devops_running_workspace_mock.go",
        "devops_running_workspace_upgrade_var_mock.go",
        "hagrid_bc_flow_to_bis_migration_train_workspace_info_mock.go",
        "main_pipeline_rollback_info_mock.go",
        "march_project_meta.go",
        "minitask_mock.go",
        "mutex_lock_mock.go",
        "node_mock.go",
        "pick_commit_mock.go",
        "project.go",
        "project_attr.go",
        "project_resource_mock.go",
        "project_rollback_info_mock.go",
        "project_template.go",
        "release_commit_record_mock.go",
        "release_info_confirm_mock.go",
        "release_ticket_change_item_mock.go",
        "release_ticket_escape_build_log_mock.go",
        "release_ticket_mock.go",
        "release_ticket_post_task_mock.go",
        "release_ticket_pre_change_item_mock.go",
        "release_ticket_release_approver_mock.go",
        "release_ticket_repo.go",
        "release_ticket_team_flow_config_mock.go",
        "rt_create_train_cron_job_config_mock.go",
        "rt_dev_relation_mock.go",
        "rt_dev_task_migration_result.go",
        "rt_dev_task_migration_task.go",
        "rt_train.go",
        "rt_train_snapshot.go",
        "stage_mock.go",
        "stage_pipeline_errinfo_mock.go",
        "stage_pipeline_mock.go",
        "stage_pipeline_run_record_mock.go",
        "task_info_mock.go",
        "team_flow_config_mock.go",
        "workflow_default_config_mock.go",
        "workflow_mock.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/biz/dal/mysql/entity",
        "//app/cdrpc/biz/dal/mysql/repository",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "@com_github_golang_mock//gomock",
        "@io_gorm_gorm//clause",
    ],
)
