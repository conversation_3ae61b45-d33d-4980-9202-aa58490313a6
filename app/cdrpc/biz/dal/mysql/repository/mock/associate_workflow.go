// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository (interfaces: AssociateWorkflowDao)

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockAssociateWorkflowDao is a mock of AssociateWorkflowDao interface.
type MockAssociateWorkflowDao struct {
	ctrl     *gomock.Controller
	recorder *MockAssociateWorkflowDaoMockRecorder
}

// MockAssociateWorkflowDaoMockRecorder is the mock recorder for MockAssociateWorkflowDao.
type MockAssociateWorkflowDaoMockRecorder struct {
	mock *MockAssociateWorkflowDao
}

// NewMockAssociateWorkflowDao creates a new mock instance.
func NewMockAssociateWorkflowDao(ctrl *gomock.Controller) *MockAssociateWorkflowDao {
	mock := &MockAssociateWorkflowDao{ctrl: ctrl}
	mock.recorder = &MockAssociateWorkflowDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssociateWorkflowDao) EXPECT() *MockAssociateWorkflowDaoMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockAssociateWorkflowDao) BatchCreate(arg0 context.Context, arg1 []*entity.DBWorkflowAssociation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockAssociateWorkflowDaoMockRecorder) BatchCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockAssociateWorkflowDao)(nil).BatchCreate), arg0, arg1)
}

// Delete mocks base method.
func (m *MockAssociateWorkflowDao) Delete(arg0 context.Context, arg1 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockAssociateWorkflowDaoMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAssociateWorkflowDao)(nil).Delete), arg0, arg1)
}

// GetByAssociateWorkflowID mocks base method.
func (m *MockAssociateWorkflowDao) GetByAssociateWorkflowID(arg0 context.Context, arg1 uint64) ([]*entity.DBWorkflowAssociation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAssociateWorkflowID", arg0, arg1)
	ret0, _ := ret[0].([]*entity.DBWorkflowAssociation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAssociateWorkflowID indicates an expected call of GetByAssociateWorkflowID.
func (mr *MockAssociateWorkflowDaoMockRecorder) GetByAssociateWorkflowID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAssociateWorkflowID", reflect.TypeOf((*MockAssociateWorkflowDao)(nil).GetByAssociateWorkflowID), arg0, arg1)
}

// GetByWorkflowID mocks base method.
func (m *MockAssociateWorkflowDao) GetByWorkflowID(arg0 context.Context, arg1 uint64) ([]*entity.DBWorkflowAssociation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByWorkflowID", arg0, arg1)
	ret0, _ := ret[0].([]*entity.DBWorkflowAssociation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByWorkflowID indicates an expected call of GetByWorkflowID.
func (mr *MockAssociateWorkflowDaoMockRecorder) GetByWorkflowID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByWorkflowID", reflect.TypeOf((*MockAssociateWorkflowDao)(nil).GetByWorkflowID), arg0, arg1)
}

// GetByWorkflowIDs mocks base method.
func (m *MockAssociateWorkflowDao) GetByWorkflowIDs(arg0 context.Context, arg1 []uint64) ([]*entity.DBWorkflowAssociation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByWorkflowIDs", arg0, arg1)
	ret0, _ := ret[0].([]*entity.DBWorkflowAssociation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByWorkflowIDs indicates an expected call of GetByWorkflowIDs.
func (mr *MockAssociateWorkflowDaoMockRecorder) GetByWorkflowIDs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByWorkflowIDs", reflect.TypeOf((*MockAssociateWorkflowDao)(nil).GetByWorkflowIDs), arg0, arg1)
}
