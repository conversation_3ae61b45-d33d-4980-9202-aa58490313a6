package entity

import (
	"time"
)

// DBRtDevTaskMigrationResult 发布单创建开发任务Task结果
type DBRtDevTaskMigrationResult struct {
	ID              int64     `json:"id" gorm:"id"`                                 // 自增ID
	MigrationTaskId int64     `json:"migration_task_id" gorm:"migration_task_id"`   // 迁移任务ID
	SourceDevTaskId int64     `json:"source_dev_task_id" gorm:"source_dev_task_id"` // 原开发任务ID
	TargetDevTaskId int64     `json:"target_dev_task_id" gorm:"target_dev_task_id"` // 开发任务ID
	TaskId          int64     `json:"task_id" gorm:"task_id"`                       // 异步创建开发任务ID
	Status          string    `json:"status" gorm:"status"`                         // 任务状态
	ErrCode         int64     `json:"err_code" gorm:"err_code"`                     // 错误码
	ErrMsg          string    `json:"err_msg" gorm:"err_msg"`                       // 错误信息
	CreatedAt       time.Time `json:"created_at" gorm:"created_at"`                 // 创建时间
	UpdatedAt       time.Time `json:"updated_at" gorm:"updated_at"`                 // 更新时间
}

// TableName 表名称
func (*DBRtDevTaskMigrationResult) TableName() string {
	return "hagrid_rt_dev_task_migration_result"
}
