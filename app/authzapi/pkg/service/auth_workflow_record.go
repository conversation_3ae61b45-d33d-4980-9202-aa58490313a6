package service

import (
	"context"

	dal "code.byted.org/devinfra/hagrid/app/authzapi/pkg/dal"
	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/provider/mysql"
	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/schema"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
)

type AuthWorkflowRecordService struct {
}

type QueryAuthWorkflowParams struct {
	ResourceBRN  string
	RoleBRNs     []string
	PrincipalBrn string
	Status       schema.AuthWorkflowStatus
}

func (s *AuthWorkflowRecordService) QueryAuthWorkflowRecords(ctx context.Context, params *QueryAuthWorkflowParams) ([]*schema.AuthWorkflowRecord, error) {
	var res []*schema.AuthWorkflowRecord
	for _, role := range params.RoleBRNs {
		records, err := dal.AuthWorkflowRecord.Query(ctx, params.ResourceBRN, role, params.PrincipalBrn, string(params.Status))
		if err != nil {
			log.V2.Error().With(ctx).Str("fail to QueryAuthWorkflowRecords").Error(err).Emit()
			return nil, err
		}
		if len(records) == 0 {
			continue
		}
		res = append(res, schema.AuthWorkflowRecordPoToSchema(records[0]))
	}
	return res, nil
}

func (s *AuthWorkflowRecordService) CreateAuthWorkflowRecord(ctx context.Context, record *schema.AuthWorkflowRecord) error {
	var err error
	tx := mysql.GetDB().Begin()
	defer func() {
		if recover() != nil || err != nil {
			_ = tx.Rollback()
		}
	}()
	err = dal.AuthWorkflowRecord.InsertWithTx(ctx, record, tx)
	if err != nil {
		logs.CtxError(ctx, "insert permission group, err=%+v", err)
		return err
	}
	if err = tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (s *AuthWorkflowRecordService) UpdateAuthWorkflowRecord(ctx context.Context, bpmid int64, status string) error {
	var err error
	tx := mysql.GetDB().Begin()
	defer func() {
		if recover() != nil || err != nil {
			_ = tx.Rollback()
		}
	}()
	err = dal.AuthWorkflowRecord.UpdateWithTx(ctx, bpmid, status, tx)
	if err != nil {
		logs.CtxError(ctx, "update permission group, err=%+v", err)
		return err
	}
	if err = tx.Commit().Error; err != nil {
		return err
	}
	return nil
}
