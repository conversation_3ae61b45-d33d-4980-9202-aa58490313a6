package agent

import (
	"context"
	"errors"
	"strings"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/external/custom_model"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/devinfra/hagrid/libs/stream"
)

// customModelAction 实现 IAction 接口
// 算法自建模型
type customModelAction struct {
	action
	modelPath string
	client    custom_model.APIIFace
}

func NewCustomModelAction(uid, name, description, modelPath string) IAction {
	return &customModelAction{
		action: action{
			UID:         uid,
			Name:        name,
			Type:        ActionTypeCustomModel,
			Description: description,
		},
		modelPath: modelPath,
		client:    custom_model.NewClient(),
	}
}

var _ IAction = new(customModelAction)

func (a *customModelAction) IsStream() bool {
	return true
}

func (a *customModelAction) Call(ctx context.Context, conversationID string, params map[string]string) (string, model.MessageRecord, error) {
	content, ok := params[model.DevGPTContentField]
	if !ok {
		return "", model.MessageRecord{}, errors.New("content is empty")
	}
	res, err := a.client.Chat(ctx, a.modelPath, content)
	if err != nil {
		return "", model.MessageRecord{}, err
	}
	return res, model.MessageRecord{}, nil
}

func (a *customModelAction) CallStream(ctx context.Context, conversationID string, params map[string]string) (*stream.RecvChannel[*model.StreamMessageEvent], error) {
	content, ok := params[model.DevGPTContentField]
	if !ok {
		return nil, errors.New("content is empty")
	}

	streamResp, err := a.client.ChatStream(ctx, a.modelPath, content)
	if err != nil {
		return nil, err
	}

	msgContent := strings.Builder{}
	chatMsgSend, chatMsgRecv := stream.NewChannel[*model.StreamMessageEvent](10)
	go stream.Forward(ctx, streamResp, chatMsgSend, func(item *custom_model.MessageChunk) *model.StreamMessageEvent {
		if item == nil || len(item.Choices) == 0 || item.Choices[0] == nil || item.Choices[0].Delta == nil {
			return nil
		}

		cnt := item.Choices[0].Delta.Content
		if len(cnt) == 0 {
			return nil
		}
		msgContent.WriteString(cnt)
		return &model.StreamMessageEvent{
			Event:   model.FlowStreamEventGenerateAnswer,
			Content: msgContent.String(),
		}
	})
	return chatMsgRecv, nil
}
