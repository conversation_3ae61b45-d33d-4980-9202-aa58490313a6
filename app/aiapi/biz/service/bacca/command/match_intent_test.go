package command

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/rmq"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/rmq/producer"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/devinfra/hagrid/app/aiapi/config"
	"code.byted.org/gopkg/env"
	"github.com/stretchr/testify/assert"
)

func TestMatchIntentCommandHandler_Handle(t *testing.T) {
	if env.IsBoe() {
		assert.True(t, env.IsBoe())
		// todo 跑的时候注释掉这段env判断
		return
	}
	tcc.MustInitialize(&tcc.Config{Psm: "bits.devopsai.api"})
	mysql.MustInitialize(&mysql.Config{
		PSM: "toutiao.mysql.hagrid_pipeline_boe",
		DB:  "hagrid_pipeline_boe",
	})
	RegisterCommandHandler(context.Background())
	conf := map[string]*rmq.Config{
		"bacca_task": {
			Topic:         "bits_ai_bacca_demo_task",
			Cluster:       "rmq_sandbox2_ipv6",
			ConsumerGroup: "bacca_task_consumer",
		},
	}
	producer.MustInitialize(conf)
	config.Conf = new(config.Config)
	config.Conf.Rmq = conf

	type fields struct {
		baseCommandHandler *baseCommandHandler
	}
	type args struct {
		ctx          context.Context
		messageModel *model.LarkMessageModel
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantOutput string
		wantErr    bool
	}{
		{
			name: "test_unknown",
			args: args{
				ctx: context.Background(),
				messageModel: &model.LarkMessageModel{
					ChatId:            "oc_26f4cf374adbc8a3ebfa3b9e463713d2",
					OriginMessageText: "你好，下一步该做什么",
				},
			},
			fields: fields{
				baseCommandHandler: newBaseCommandHandler(context.Background(), MatchIntentCommand),
			},
			wantOutput: "unknown",
		},
		{
			name: "test_unknown_2",
			args: args{
				ctx: context.Background(),
				messageModel: &model.LarkMessageModel{
					ChatId:            "oc_26f4cf374adbc8a3ebfa3b9e463713d2",
					OriginMessageText: "查下最近线上有变更吗",
				},
			},
			fields: fields{
				baseCommandHandler: newBaseCommandHandler(context.Background(), MatchIntentCommand),
			},
			wantOutput: "unknown",
		},
		{
			name: "test_error_log",
			args: args{
				ctx: context.Background(),
				messageModel: &model.LarkMessageModel{
					ChatId:            "oc_26f4cf374adbc8a3ebfa3b9e463713d2",
					OriginMessageText: "查下线上有没有异常报错",
				},
			},
			fields: fields{
				baseCommandHandler: newBaseCommandHandler(context.Background(), MatchIntentCommand),
			},
			wantOutput: "query_error_log",
		},
		{
			name: "test_metrics",
			args: args{
				ctx: context.Background(),
				messageModel: &model.LarkMessageModel{
					ChatId:            "oc_26f4cf374adbc8a3ebfa3b9e463713d2",
					OriginMessageText: "查下redis大key的情况",
				},
			},
			fields: fields{
				baseCommandHandler: newBaseCommandHandler(context.Background(), MatchIntentCommand),
			},
			wantOutput: "query_metrics_data",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &MatchIntentCommandHandler{
				baseCommandHandler: tt.fields.baseCommandHandler,
			}
			answer, _ := a.Handle(tt.args.ctx, tt.args.messageModel)
			if answer != tt.wantOutput {
				t.Errorf("Handle() gotAnswer = %v, want %v", answer, tt.wantOutput)
				return
			}
		})
	}
}
