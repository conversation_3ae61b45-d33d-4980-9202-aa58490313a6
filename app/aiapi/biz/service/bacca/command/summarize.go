package command

import (
	"context"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"

	argos_alarm "code.byted.org/devinfra/hagrid/app/aiapi/biz/external/argos/alarm"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/external/lark/utils"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/service/argos"
)

type SummarizeCommandHandler struct {
	*baseCommandHandler
}

func (r *SummarizeCommandHandler) Handle(ctx context.Context, messageModel *model.LarkMessageModel) (output string, err error) {
	var (
		msgId       string
		summaryCtx  argos.SummaryContext
		alarmClient = argos_alarm.NewClient()
	)

	msgId = messageModel.ParentMessageId // @Bacca，再引用告警卡片
	logs.CtxInfo(ctx, "[summary] start summary, alarm card id: %s", msgId)

	summaryCtx = argos.SummaryContext{}

	// 1. get alarm by alarmCardMsgId
	alarmData, err := alarmClient.GetAlarmByMsgIdOrSendItemId(ctx, msgId)
	if err != nil {
		logs.CtxError(ctx, "fail to GetAlarmByMsgIdOrSendItemId, err: %v", err)
		utils.SendBadContentMsg(ctx, messageModel, "fail to GetAlarmByMsgIdOrSendItemId")
		return output, err
	}

	region, err := r.alarmService.GetAlarmRegion(ctx, *alarmData)
	if err != nil {
		logs.CtxError(ctx, "fail to GetAlarmRegion, err: %v", err)
		utils.SendBadContentMsg(ctx, messageModel, "fail to GetAlarmRegion")
		return output, err
	}

	summaryCtx.MetaData = alarmData
	summaryCtx.Region = region

	// 2. get alarm rule
	ruleID := alarmData.RuleID
	rule, err := r.alarmClient.GetRuleById(ctx, ruleID)

	if err != nil {
		utils.SendBadContentMsg(ctx, messageModel, "fail to GetAlarmRegion")
		return output, err
	}

	summaryCtx.RuleDetail = rule
	summaryCtx.Bosun = rule.Rule

	// 3. get metrics

	// 4. summary
	cardId, err := utils.CreateCommonCard(ctx, messageModel, "告警分析", "正在查询告警规则...")
	if err != nil {
		return "", err
	}
	ruleSummary, err := r.alarmService.SummarizeRule(ctx, *summaryCtx.RuleDetail, summaryCtx.Bosun)
	if err != nil {
		logs.CtxError(ctx, "fail to SummarizeRule, err: %v", err)
		return output, err
	}

	err = utils.UpdateCommonCard(ctx, cardId, "告警分析", "告警规则解读完成")
	if err != nil {
		return "", err
	}

	summaryObj := &Summary{
		Psm:            summaryCtx.MetaData.Psm,
		AlarmName:      summaryCtx.RuleDetail.Name,
		AlarmLevel:     summaryCtx.RuleDetail.Level,
		AlarmStartTime: summaryCtx.MetaData.AlertStartTime,
		AlarmTime:      summaryCtx.MetaData.AlertTime,
		AlarmRule:      ruleSummary,
	}

	summaryObjStr, err := json.MarshalString(summaryObj)
	if err != nil {
		logs.CtxError(ctx, "fail to getCardOutputFromSummery, err: %v", err)
		return "", err
	}
	summaryCtx.RuleSummary = ruleSummary
	summaryCtx.AlarmSummary = summaryObjStr

	return json.MarshalString(summaryCtx)
}

func removePrefix(s, prefix string) string {
	// 如果原字符串 s 以给定子字符串 prefix 开头，就把开头去掉
	if strings.HasPrefix(s, prefix) {
		s = s[len(prefix):]
	}
	return s
}

type SummarizeAlarmResponse struct {
	AlarmId           string            `json:"alarm_id"`
	UserId            string            `json:"user_id"`
	CreateAt          time.Time         `json:"create_at"`
	UpdateAt          time.Time         `json:"update_at"`
	Psm               string            `json:"psm"`
	Region            string            `json:"region"`
	Cluster           string            `json:"cluster"`
	CheckRegion       string            `json:"check_region"`
	AlarmName         string            `json:"alarm_name"`
	AlarmType         string            `json:"alarm_type"`
	AlarmLevel        string            `json:"alarm_level"`
	AlarmStatus       string            `json:"alarm_status"`
	AlarmRule         string            `json:"alarm_rule"`
	AlarmRuleAnalysis string            `json:"alarm_rule_analysis"`
	Extra             map[string]string `json:"extra"`
}

type Summary struct {
	Psm               string `json:"psm" mapstructure:"psm"`
	AlarmName         string `json:"alarm_name" mapstructure:"alarm_name"`
	AlarmLevel        string `json:"alarm_level" mapstructure:"alarm_level"`
	AlarmStartTime    string `json:"alarm_start_time" mapstructure:"alarm_start_time"`
	AlarmTime         string `json:"alarm_time" mapstructure:"alarm_time"`
	AlarmRule         string `json:"alarm_rule" mapstructure:"alarm_rule"`
	AlarmRuleAnalysis string `json:"alarm_rule_analysis" mapstructure:"alarm_rule_analysis"`
}

const summaryCardOutputTemplate = `	本次告警名称为 %s，服务为 **%s**，告警规则级别为 %s，开始时间为 %s，持续至 %s。
	告警bosun含义解读如下： 
	- **%s**
	- **%s**
`
