package actions

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/gopkg/env"
	"github.com/stretchr/testify/assert"

	"code.byted.org/argos/aaas-analyzers/pkg/rca"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/external/copilot"
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
)

func TestRcaSuggestAction_RunTool(t *testing.T) {
	if env.IsBoe() {
		assert.True(t, env.IsBoe())
		// todo 跑的时候注释掉这段env判断
		return
	}
	tcc.MustInitialize(&tcc.Config{Psm: "bits.devopsai.api"})
	type fields struct {
		rcaSdk         rca.AnalyzersProvider
		convMng        *copilot.ConversationManager
		httpclient     *httpclient.BytedHttpClient
		supportedTools map[string]*Tool
	}
	type args struct {
		ctx          context.Context
		tool         *Tool
		pvs          []ParameterValue
		messageModel *model.LarkMessageModel
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name:   "test",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				pvs: []ParameterValue{
					{Name: "psm", Value: "bits.devopsai.api"},
					{Name: "alarm_data", Value: "[P0] [Android] [release] [CI] [CI_CD_Pipeline] [ 服务 ]: bits.devopsai.api\n[ 集群 ]: China-North: default\n[ 规则 ]: TCE 实例进程退出报警 [查看配置]\n[ 报警时间 ]: 2024-04-08T17:38:30+08:00 (已持续2分钟)\n[ 值班人 ]：gengjie.02, isami.akasaka, lizeyu.wyxhh, shixiaoxue.111, tangjing.fisher, zhangxuanming.tiktok\n[ 通知方式 ]：Lark + Webhook\n[ 点击查看 ]：TCE Pod Info, Argos 监控诊断"},
					{Name: "alarm_rule", Value: "TCE 实例进程退出报警"},
				},
				messageModel: &model.LarkMessageModel{
					ChatId:            "oc_26f4cf374adbc8a3ebfa3b9e463713d2",
					ParentMessageText: "服务: bits.devopsai.api\n\n告警名称: argos inject go service panic log\n\n对提问者的回复: 您好，根据您提供的告警信息，我已经制定了一个分析定位故障的计划。这个计划将从分析告警日志开始，然后查看代码，最后进行根因分析和修复。\n\n当前步骤: 分析告警日志并获取有关故障的详细信息\n\n计划:\n- [ ] Step 1: 分析告警日志，了解故障的具体情况，如故障时间、故障类型等。\n- [ ] Step 2: 根据日志中的错误信息，查看对应的代码行，理解代码逻辑，找出可能的问题所在。\n- [ ] Step 3: 进行根因分析，确定导致故障的具体原因。\n- [ ] Step 4: 根据根因分析的结果，进行代码修复或配置调整，以解决故障。\n- [ ] Step 5: 修复后，再次观察告警和服务状态，确认问题是否已经解决。\n- [ ] Step 6: 编写故障报告，记录故障的情况、原因、解决方法以及后续改进措施。\n\n总结: 本计划主要通过分析告警日志和代码，找出故障的原因，并进行修复。关键的考虑因素是代码的逻辑和配置，以及告警日志中的错误信息。可能的挑战是需要深入理解代码逻辑和配置，以找出问题的根源。在整个过程中，可能需要开发人员的协助。",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := newRcaSuggestAction()
			_, err := a.Run(tt.args.ctx, tt.args.messageModel, "", tt.args.pvs)
			if (err != nil) != tt.wantErr {
				t.Errorf("Run() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
