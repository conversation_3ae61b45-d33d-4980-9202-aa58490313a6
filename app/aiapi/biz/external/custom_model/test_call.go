package custom_model

import (
	"context"
	"math/rand"
	"strconv"
	"time"

	"code.byted.org/gopkg/logs"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
)

var httpClient *httpclient.BytedHttpClient

type llmApiBody struct {
	Messages    []llmMsg `json:"messages"`
	Temperature float64  `json:"temperature"`
	Model       string   `json:"model"`
}

type llmMsg struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

func CallTestModel(ctx context.Context, content string) {
	if httpClient == nil {
		httpClient = httpclient.MustNewBytedHttpClient().
			SetRetryCount(1).
			SetTimeout(time.Minute * 10)
	}
	data, err := tcc.GetRawData(ctx, "custom_model_call_rate")
	if err != nil {
		logs.CtxError(ctx, "volcanoAction CallStream error: %v", err)
		data = "5" // 默认为1/5几率
	}
	n, err := strconv.Atoi(data)
	if err != nil {
		logs.CtxError(ctx, "volcanoAction CallStream error: %v", err)
		n = 5 // 默认为1/5几率
	}
	randomNumber := rand.Intn(n)

	// 1/n 的几率调用接口
	if randomNumber != 0 {
		logs.CtxInfo(ctx, "skip call deepseek")
		return
	}

	apiBody := &llmApiBody{
		Messages: []llmMsg{
			{
				Role:    "user",
				Content: content,
			},
		},
		Temperature: 0.15,
		Model:       "dsv2",
	}
	// call deepseek
	resp, err := httpClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(apiBody).
		SetContext(ctx).
		Post("http://bitsai-code-llm.bytedance.net/models/deepseekv25/bitsaici/v1/chat/completions")
	if err != nil {
		logs.CtxError(ctx, "call deepseek error: %v", err)
	}
	logs.CtxInfo(ctx, "call deepseek resp: %v", resp)

	//call qwen
	resp, err = httpClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(apiBody).
		SetContext(ctx).
		Post("http://bitsai-code-llm.bytedance.net/models/qwen/32b/ci_rootcause/v1/chat/completions")
	if err != nil {
		logs.CtxError(ctx, "call qwen error: %v", err)
	}
	logs.CtxInfo(ctx, "call qwen resp: %v", resp)
	return
}
