package utils

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/byted/kitexutil"
	"code.byted.org/lang/gg/choose"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/external/lark"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/utils"
)

const (
	commonCardId   = "AAqkOUsJPEXq7"
	commonTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "title":"%s",
          "content": "%s"
       }
    }
 }`
	askUserCardId   = "AAqkIhRpN01dD"
	askUserTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "title":"%s",
          "content": "%s"
       }
    }
 }`

	errorCardId   = "AAqkR8HUy2kyi"
	errorTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_version_name":"1.0.3",
       "template_variable":{
          "msg": "%s"
       }
    }
 }`
	interactiveCardId   = "AAqkYyqdF9jLY"
	interactiveTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_version_name":"1.0.5",
       "template_variable":{
          "summary":"%s",
          "plan": "%s"
       }
    }
 }`
	rcaSuggestCardId   = "AAqkBdBFuVYwa"
	rcaSuggestTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "strategies":"%s",
          "strategy1": "%s",
          "strategy2": "%s",
          "strategy3": "%s"
       }
    }
 }`
	rcaExecuteCardId   = "AAqkRi38I0FWl"
	rcaExecuteTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "rca_name":"%s",
          "summary": "%s",
          "case_link": "%s"
       }
    }
 }`

	analysisDirectionCardId   = "AAq3rtuw5TarO"
	analysisDirectionTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "advice":"%s",
          "psm": "%s"
       }
    }
 }`
	analysisSelectCardId   = "AAq3XvIzCaePs"
	analysisSelectTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "report":"%s",
          "analysis_directions": %s
       }
    }
 }`

	debugCardId   = "AAqklPDeIeQ5y"
	debugTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "title":"%s",
          "content": "%s"
       }
    }
 }`

	finishTemplate = `{
    "type":"template",
    "data":{
       "template_id":"AAqkZX57OXkrq"
    }
 }`

	analysisReplanCardId   = "AAq38MoCxPeRn"
	analysisReplanTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "advice":"%s",
          "psm": "%s"
       }
    }
 }`

	measurementCardId   = "AAq3Hw85F3kQ4"
	measurementTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "measurement_name":"%s",
          "url": "%s",
          "image": "%s",
          "pattern": "%s",
          "score": "%s",
          "abnormalInterval": "%s",
          "summary": "%s"
       }
    }
 }`

	measurementResultCardId   = "AAq3jlbyHmW2z"
	measurementResultTemplate = `{
    "type":"template",
    "data":{
       "template_id":"%s",
       "template_variable":{
          "summary":"%s",
          "hit": "%s",
          "reason": "%s",
          "next_step": "%s"
       }
    }
 }`
)

var (
	RcaDisplayMap = map[string]string{
		"Argos-Analyze-Availability-Problem":      "分析服务的可用性为什么下降",
		"Argos-Analyze-ServerError-Problem":       "分析服务Server端失败率升高的原因",
		"Argos-Analyze-Latency-Problem":           "分析服务延迟为什么上涨",
		"Argos-Analyze-Traffic-Problem":           "分析流量突增突降的原因",
		"Argos-Analyze-DownstreamCall-Fault":      "分析服务为什么调用下游失败率升高",
		"Argos-Analyze-ErrorLog-Problem":          "定位错误日志上涨的具体日志内容",
		"Argos-Analyze-HighLoad-Problem":          "分析Cpu, Mem高负载的原因",
		"Argos-Analyze-OutlierInstance-HttpApi":   "自定义http api接口, 返回需要迁移的pod单实例列表",
		"Argos-Analyze-OutlierInstance-TagValue":  "从当前的报警规则的上下文中的metrics tags或者bosun vars指定的key获得异常单实例",
		"Argos-Analyze-OutlierInstance-Drilldown": "基于measurement自定义的自定义下钻或者异常单实例列表并进行自动迁移",
		"Argos-Analyze-ProcessAbnormal-Problem":   "分析服务是否存在进程异常",
	}
)

func SendBadContentMsg(ctx context.Context, messageModel *model.LarkMessageModel, text string) {
	// 出错时添加trace信息，方便排查问题
	text = addTraceUrl(ctx, text)

	content := fmt.Sprintf(errorTemplate, errorCardId, JsonStringify(text))
	createCard(ctx, messageModel, content)
}

func UpdateCommonCard(ctx context.Context, msgId string, title string, text string) error {
	content := fmt.Sprintf(commonTemplate, commonCardId, JsonStringify(title), JsonStringify(text))
	return patchCard(ctx, msgId, content)
}

func UpdateAskUserCard(ctx context.Context, msgId string, title string, text string) error {
	content := fmt.Sprintf(askUserTemplate, askUserCardId, JsonStringify(title), JsonStringify(text))
	return patchCard(ctx, msgId, content)
}

func CreateCommonCard(ctx context.Context, messageModel *model.LarkMessageModel, title string, text string) (string, error) {
	content := fmt.Sprintf(commonTemplate, commonCardId, JsonStringify(title), JsonStringify(text))
	return createCard(ctx, messageModel, content)
}

// 如果是在话题中，则回复卡片，否则在当前会话中创建卡片
func CreateOrReplyCommonCard(ctx context.Context, messageModel *model.LarkMessageModel, title string, text string) (string, error) {
	content := fmt.Sprintf(commonTemplate, commonCardId, JsonStringify(title), JsonStringify(text))
	if utils.GetIsCustomChat(ctx) {
		return ReplyCardInThread(ctx, messageModel.ChatId, content)
	}
	return createCard(ctx, messageModel, content)
}

func CreateInteractiveCard(ctx context.Context, messageModel *model.LarkMessageModel, title string, text string) (string, error) {
	content := fmt.Sprintf(interactiveTemplate, interactiveCardId, JsonStringify(title), JsonStringify(text))
	return createCard(ctx, messageModel, content)
}

func CreateInteractiveCardOfRawContent(ctx context.Context, chatId string, rawContent string) (string, error) {
	return createCard(ctx, &model.LarkMessageModel{ChatId: chatId}, rawContent)
}

func CreateRcaSuggestCard(ctx context.Context, messageModel *model.LarkMessageModel,
	strategies []string) (string, error) {
	var strategyStr, strategy1, strategy2, strategy3 string
	for i, s := range strategies {
		strategyStr += fmt.Sprintf("%d. %s(%s)\\n", i+1, RcaDisplayMap[s], s)
		if i == 0 {
			strategy1 = s
		} else if i == 1 {
			strategy2 = s
		} else if i == 2 {
			strategy3 = s
		} else {
			break
		}
	}
	content := fmt.Sprintf(rcaSuggestTemplate, rcaSuggestCardId, strategyStr, strategy1, strategy2, strategy3)
	return createCard(ctx, messageModel, content)
}

func CreateAnalysisDirectionCard(ctx context.Context, messageModel *model.LarkMessageModel, advice string, psm string) (string, error) {
	content := fmt.Sprintf(analysisDirectionTemplate, analysisDirectionCardId, JsonStringify(advice), psm)
	return createCard(ctx, messageModel, content)
}

func UpdateAnalysisSelectCard(ctx context.Context, report string, directions string, msgID string) error {
	content := fmt.Sprintf(analysisSelectTemplate, analysisSelectCardId, JsonStringify(report), directions)
	return patchCard(ctx, msgID, content)
}

func CreateAnalysisReplanCard(ctx context.Context, messageModel *model.LarkMessageModel, advice string, psm string) (string, error) {
	content := fmt.Sprintf(analysisReplanTemplate, analysisReplanCardId, JsonStringify(advice), psm)
	if utils.GetIsCustomChat(ctx) {
		return ReplyCardInThread(ctx, messageModel.ChatId, content)
	}
	return createCard(ctx, messageModel, content)
}

func CreateDebugCard(ctx context.Context, messageModel *model.LarkMessageModel, title string, text string) (string, error) {
	if !env.IsBoe() {
		return "", nil
	}
	text = addTraceUrl(ctx, text)
	content := fmt.Sprintf(debugTemplate, debugCardId, JsonStringify(title), JsonStringify(text))
	return createCard(ctx, messageModel, content)
}

func UpdateDebugCard(ctx context.Context, msgId string, title string, text string) error {
	if !env.IsBoe() {
		return nil
	}
	text = addTraceUrl(ctx, text)
	content := fmt.Sprintf(debugTemplate, debugCardId, JsonStringify(title), JsonStringify(text))
	return patchCard(ctx, msgId, content)
}
func CreateMeasurementCard(ctx context.Context, messageModel *model.LarkMessageModel, measurement string, url string, imageKey string) (string, error) {
	s := "正在执行，请稍后"
	content := fmt.Sprintf(measurementTemplate, measurementCardId, measurement, url, imageKey, s, s, s, s)
	return createCard(ctx, messageModel, content)
}

func UpdateMeasurementCard(ctx context.Context, msgId string, measurement, url, imageKey, pattern, score, abnormal, summary string) error {
	s := "正在执行，请稍后"
	content := fmt.Sprintf(measurementTemplate, measurementCardId, measurement, url, imageKey,
		choose.If(len(pattern) == 0, s, pattern),
		choose.If(len(score) == 0, s, score),
		choose.If(len(abnormal) == 0, s, abnormal),
		choose.If(len(summary) == 0, s, summary))
	return patchCard(ctx, msgId, content)
}

func CreateMeasurementResultCard(ctx context.Context, messageModel *model.LarkMessageModel, summary, hit, reason, next_step string) (string, error) {
	content := fmt.Sprintf(measurementResultTemplate, measurementResultCardId, summary, hit, reason, next_step)
	return createCard(ctx, messageModel, content)
}

func createCard(ctx context.Context, messageModel *model.LarkMessageModel, content string) (string, error) {
	if utils.GetIsCustomChat(ctx) {
		logs.CtxInfo(ctx, "no need to create card, content:%s", content)
		return "", nil
	}
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(larkim.ReceiveIdTypeChatId).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeInteractive).
			Content(content).
			ReceiveId(messageModel.ChatId).
			Build()).
		Build()
	resp, err := lark.GetLarkClient().Im.Message.Create(ctx, req)
	if err != nil {
		//logs.CtxError(ctx, "create lark card failed, err = %v", err)
		return "", errors.Errorf("create lark card failed, err = %v", err)
	}
	if !resp.Success() {
		//logs.CtxError(ctx, "create lark card failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
		return "", errors.Errorf("create lark card failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	return *resp.Data.MessageId, nil
}

func patchCard(ctx context.Context, msgId string, content string) error {
	if msgId == "" {
		return nil
	}
	req := larkim.NewPatchMessageReqBuilder().
		MessageId(msgId).
		Body(larkim.NewPatchMessageReqBodyBuilder().
			Content(content).
			Build()).
		Build()
	resp, err := lark.GetLarkClient().Im.Message.Patch(ctx, req)
	if err != nil {
		//logs.CtxError(ctx, "patch lark card failed, err = %v", err)
		return errors.Errorf("patch lark card failed, err = %v", err)
	}
	if !resp.Success() {
		//logs.CtxError(ctx, "patch lark card failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
		return errors.Errorf("patch lark card failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	return nil
}

func SendDebugText(ctx context.Context, cnt string) error {
	if !env.IsBoe() {
		logs.CtxInfo(ctx, "debug content:%s", cnt)
		return nil
	}
	chatID := utils.GetChatIDCtx(ctx)
	if len(chatID) == 0 {
		return nil
	}
	return SendPlainText(ctx, &model.LarkMessageModel{ChatId: chatID}, cnt)
}

func SendPlainText(ctx context.Context, messageModel *model.LarkMessageModel, content string) error {
	if utils.GetIsCustomChat(ctx) {
		logs.CtxInfo(ctx, "no need to send text, content:%s", content)
		return nil
	}
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(larkim.ReceiveIdTypeChatId).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			MsgType(larkim.MsgTypeText).
			Content(larkim.NewTextMsgBuilder().
				Text(content).
				Build()).
			ReceiveId(messageModel.ChatId).
			Build()).
		Build()
	resp, err := lark.GetLarkClient().Im.Message.Create(ctx, req)
	if err != nil {
		//logs.CtxError(ctx, "send lark text failed, err = %v", err)
		return errors.Errorf("send lark text failed, err = %v", err)
	}
	if !resp.Success() {
		//logs.CtxError(ctx, "send lark text failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
		return errors.Errorf("send lark text failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	logs.CtxInfo(ctx, "send text, msgID = %v", resp.Data.MessageId)
	return nil
}

func CreateFinishCard(ctx context.Context, messageModel *model.LarkMessageModel) (string, error) {
	return createCard(ctx, messageModel, finishTemplate)
}

func JsonStringify(v any) string {
	b, err := jsonx.Marshal(v)
	if err != nil {
		return fmt.Sprintf("%+v", v)
	}
	s := string(b)
	if s[0] == '"' {
		return s[1 : len(s)-1]
	} else {
		return s
	}
}

func addTraceUrl(ctx context.Context, content string) string {
	logID, _ := kitexutil.GetLogID(ctx)
	logIDUrl := fmt.Sprintf("https://%s/argos/streamlog/info_overview/log_id_search?logId=%s&psm=bits.devopsai.api",
		choose.If(env.IsBoe(), "cloud-boe.bytedance.net", "cloud.bytedance.net"),
		logID)
	content = fmt.Sprintf("%s: [查看日志](%s)", content, logIDUrl)
	return content
}

func GetChatInfo(ctx context.Context, chatID string) (*larkim.GetChatRespData, error) {
	req := larkim.NewGetChatReqBuilder().
		ChatId(chatID).
		Build()
	resp, err := lark.GetLarkClient().Im.Chat.Get(ctx, req)
	if err != nil {
		return nil, errors.Errorf("get chat info failed, err = %v", err)
	}
	if !resp.Success() {
		return nil, errors.Errorf("get chat info failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	return resp.Data, nil
}

func ReplyCardInThread(ctx context.Context, messageID string, content string) (string, error) {
	req := larkim.NewReplyMessageReqBuilder().
		MessageId(messageID).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			Content(content).
			MsgType(larkim.MsgTypeInteractive).
			ReplyInThread(true).
			Build()).
		Build()

	resp, err := lark.GetLarkClient().Im.Message.Reply(ctx, req)
	if err != nil {
		return "", errors.Errorf("reply message failed, err = %v", err)
	}
	if !resp.Success() {
		return "", errors.Errorf("reply message failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	return *resp.Data.MessageId, nil
}

func GetMessageInfo(ctx context.Context, messageID string) (*larkim.GetMessageRespData, error) {
	req := larkim.NewGetMessageReqBuilder().
		MessageId(messageID).
		Build()

	resp, err := lark.GetLarkClient().Im.Message.Get(ctx, req)
	if err != nil {
		return nil, errors.Errorf("get message info failed, err = %v", err)
	}
	if !resp.Success() {
		return nil, errors.Errorf("get message info failed, code = %d, message = %s, requestID = %s", resp.Code, resp.Msg, resp.RequestId())
	}
	return resp.Data, nil
}
