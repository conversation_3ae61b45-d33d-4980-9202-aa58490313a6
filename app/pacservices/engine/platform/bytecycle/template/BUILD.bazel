load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "template",
    srcs = ["template.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/pacservices/engine/platform/bytecycle/template",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pacservices/engine/biz/intf",
        "//app/pacservices/engine/constvar",
        "//app/pacservices/engine/platform/bytecycle/aslpb",
        "//app/pacservices/engine/platform/bytecycle/pipeline",
        "//app/pacservices/engine/stub/bytecycle",
        "//app/pacservices/engine/stub/model",
        "//app/pacservices/engine/stub/resource",
        "//app/pacservices/engine/util",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_canal_bytecycle_sdk//template/model",
        "@org_byted_code_gopkg_logs_v2//log",
    ],
)
