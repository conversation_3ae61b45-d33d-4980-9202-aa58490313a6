// Code generated by COMMENTS_BUILD_TOOLS 2.0.72. DO NOT EDIT.
package mysql

import (
	"context"
	"database/sql"
	"errors"

	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

var GlobalErrReadCRUDRepo = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewReadCRUDRepo(handler *gorm.DB) ReadCRUDRepo {
	return &_ReadCRUDRepoStruct{
		handler: handler,
	}
}

type _ReadCRUDRepoStruct struct {
	handler *gorm.DB
}

func (interstruct *_ReadCRUDRepoStruct) CountAggregateIssueForMetric(ctx context.Context, hostIds []int64) (CountAggregateIssueResult, error) {
	_result, _retErr := func() (CountAggregateIssueResult, error) {
		_sqlText := "select sum(1) as all_count,sum(if(status='false_alarm', 1, 0)) as false_alarm_count from security_aggregate_issue where host_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CountAggregateIssueResult{}, _sdb.Error
		}
		var _ret CountAggregateIssueResult
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CountAggregateIssueResult{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) CountAggregateIssueForMetricWithJobNames(ctx context.Context, hostIds []int64, jobNames []string) (CountAggregateIssueResult, error) {
	_result, _retErr := func() (CountAggregateIssueResult, error) {
		_sqlText := "select sum(1) as all_count,sum(if(status='false_alarm', 1, 0)) as false_alarm_count from security_aggregate_issue where host_id in (?) and job_name in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds, jobNames)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CountAggregateIssueResult{}, _sdb.Error
		}
		var _ret CountAggregateIssueResult
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CountAggregateIssueResult{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) CountIssueApprovingByApproveInstanceId(ctx context.Context, approveInstanceId string, statuses []string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from security_issue_approve_info where approve_instance_id=? and approve_status in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, approveInstanceId, statuses)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) FilterAggregateIssueForApply(ctx context.Context, hostId int64, issueIds []string, statuses []string) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_aggregate_issue where host_id=? and issue_id in (?) and status in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, issueIds, statuses)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) FilterCheckIdByHostId(ctx context.Context, checkIds []int64, hostId int64) ([]int64, error) {
	_result, _retErr := func() ([]int64, error) {
		_sqlText := "select check_id from security_task_check_relation where check_id in (?) and host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []int64
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret int64
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetAggregateIssueById(ctx context.Context, id int64) (SecurityAggregateIssue, error) {
	_result, _retErr := func() (SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where id=? limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityAggregateIssue{}, _sdb.Error
		}
		var _ret SecurityAggregateIssue
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityAggregateIssue{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetAllAppsOfConfigV2(ctx context.Context) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select distinct group_name from security_config_v2 where group_name is not null and group_name!=''"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckByCheckId(ctx context.Context, checkId int64) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckByPMCJ(ctx context.Context, projectId int64, mrIid int64, commitId string, jobName string) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where project_id=? and mr_iid=? and commit_id=? and job_name=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, commitId, jobName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckByPMCJC(ctx context.Context, projectId int64, mrIid int64, commitId string, jobName string, cacheKey string) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where project_id=? and mr_iid=? and commit_id=? and job_name=? and cache_key=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, commitId, jobName, cacheKey)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckByPMJ(ctx context.Context, projectId int64, mrIid int64, jobName string) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where project_id=? and mr_iid=? and job_name=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, jobName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckByPMJC(ctx context.Context, projectId int64, mrIid int64, jobName string, cacheKey string) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where project_id=? and mr_iid=? and job_name=? and cache_key=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, jobName, cacheKey)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetCheckForCache(ctx context.Context, projectId int64, mrIid int64, commitId string, baseCommitId string, cacheKey string, jobName string) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where project_id=? and mr_iid=? and commit_id=? and base_commit_id=? and cache_key=? and job_name=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, commitId, baseCommitId, cacheKey, jobName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetConfigV2(ctx context.Context, groupName string) (SecurityConfigV2, error) {
	_result, _retErr := func() (SecurityConfigV2, error) {
		_sqlText := "select * from security_config_v2 where group_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityConfigV2{}, _sdb.Error
		}
		var _ret SecurityConfigV2
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityConfigV2{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetConfigV2ExportIsOpen(ctx context.Context, groupName string) (bool, error) {
	_result, _retErr := func() (bool, error) {
		_sqlText := "select export_is_open from security_config_v2 where group_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return false, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return false, gorm.ErrRecordNotFound
		}
		var _ret bool
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return false, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetConfigV2Jobs(ctx context.Context, groupName string) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select jobs from security_config_v2 where group_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetConfigV2SkipApprovers(ctx context.Context, groupName string) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select skip_approvers from security_config_v2 where group_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetForecastVersion(ctx context.Context, hostId int64) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select forecast_version from security_host where host_id=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetGlobalPermissionsAppByName(ctx context.Context, name string) (SecurityGlobalCustomPermission, error) {
	_result, _retErr := func() (SecurityGlobalCustomPermission, error) {
		_sqlText := "select bits_app_id,app_name from security_global_custom_permission where permission_name=? limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, name)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityGlobalCustomPermission{}, _sdb.Error
		}
		var _ret SecurityGlobalCustomPermission
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityGlobalCustomPermission{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetHostIdByCheckId(ctx context.Context, checkId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select host_id from security_task_check_relation where check_id=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetHostRectifiedStatus(ctx context.Context, hostId int64) (bool, error) {
	_result, _retErr := func() (bool, error) {
		_sqlText := "select has_rectified from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return false, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return false, gorm.ErrRecordNotFound
		}
		var _ret bool
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return false, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetIPAPermissionByIssueId(ctx context.Context, issueId string) (SecurityIpaPermissionIssue, error) {
	_result, _retErr := func() (SecurityIpaPermissionIssue, error) {
		_sqlText := "select * from security_ipa_permission_issue where issue_id=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, issueId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityIpaPermissionIssue{}, _sdb.Error
		}
		var _ret SecurityIpaPermissionIssue
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityIpaPermissionIssue{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetIPAPermissionsByIssueIds(ctx context.Context, issueId []string) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_ipa_permission_issue where issue_id in (?) order by id "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, issueId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetIPAPermissionsByName(ctx context.Context, name string, issueId string) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_ipa_permission_issue where name =(?) and issue_id!=(?) order by id "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, name, issueId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetLatestMainSecurityTask(ctx context.Context, hostId int64, projectId int64) (SecurityTask, error) {
	_result, _retErr := func() (SecurityTask, error) {
		_sqlText := "select * from security_task where host_id=? and project_id=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, projectId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityTask{}, _sdb.Error
		}
		var _ret SecurityTask
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityTask{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetMRInfo(ctx context.Context, hostId int64) (MRInfo, error) {
	_result, _retErr := func() (MRInfo, error) {
		_sqlText := "select project_id,mr_iid,mr_url,mr_title,user_email,app_id,group_name from security_task where host_id=? limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return MRInfo{}, _sdb.Error
		}
		var _ret MRInfo
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return MRInfo{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetPermissionTaskByPipelineJobId(ctx context.Context, pipelineJobId int64) (SecurityTaskCustomPermission, error) {
	_result, _retErr := func() (SecurityTaskCustomPermission, error) {
		_sqlText := "select * from security_task_custom_permission where pipeline_job_id=? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, pipelineJobId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityTaskCustomPermission{}, _sdb.Error
		}
		var _ret SecurityTaskCustomPermission
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityTaskCustomPermission{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetPermissionTaskStatusById(ctx context.Context, id int64) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select status from security_task_custom_permission where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetPermissionTasksByHostId(ctx context.Context, hostId int64) ([]SecurityTaskCustomPermission, error) {
	_result, _retErr := func() ([]SecurityTaskCustomPermission, error) {
		_sqlText := "select security_task_custom_permission.* from security_task_custom_permission inner join (select max(id) as id from security_task_custom_permission where host_id=? group by bits_app_id order by null) A on security_task_custom_permission.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTaskCustomPermission
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTaskCustomPermission
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetRuleApproveInfoByProcessInstanceId(ctx context.Context, approveProcessInstanceId string) (SecurityRuleApproveInfo, error) {
	_result, _retErr := func() (SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where approve_process_instance_id=? and status!='repaired'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, approveProcessInstanceId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityRuleApproveInfo{}, _sdb.Error
		}
		var _ret SecurityRuleApproveInfo
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityRuleApproveInfo{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityCheck(ctx context.Context, id int64) (SecurityCheck, error) {
	_result, _retErr := func() (SecurityCheck, error) {
		_sqlText := "select * from security_check where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityCheck{}, _sdb.Error
		}
		var _ret SecurityCheck
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityCheck{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityGlobalRuleDetail(ctx context.Context, id int64) (SecurityGlobalRuleDetail, error) {
	_result, _retErr := func() (SecurityGlobalRuleDetail, error) {
		_sqlText := "select * from security_global_rule_detail where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityGlobalRuleDetail{}, _sdb.Error
		}
		var _ret SecurityGlobalRuleDetail
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityGlobalRuleDetail{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityGlobalRuleDetailByRuleIndexInfo(ctx context.Context, engine string, ruleIndexInfo string) (SecurityGlobalRuleDetail, error) {
	_result, _retErr := func() (SecurityGlobalRuleDetail, error) {
		_sqlText := "select * from security_global_rule_detail where engine=? and rule_index_info=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, engine, ruleIndexInfo)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityGlobalRuleDetail{}, _sdb.Error
		}
		var _ret SecurityGlobalRuleDetail
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityGlobalRuleDetail{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityHostByHostId(ctx context.Context, hostId int64) (SecurityHost, error) {
	_result, _retErr := func() (SecurityHost, error) {
		_sqlText := "select * from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityHost{}, _sdb.Error
		}
		var _ret SecurityHost
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityHost{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityHostForVersion(ctx context.Context, hostId int64) (SecurityHost, error) {
	_result, _retErr := func() (SecurityHost, error) {
		_sqlText := "select id,version,forecast_version from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityHost{}, _sdb.Error
		}
		var _ret SecurityHost
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityHost{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityHostParamsByHostId(ctx context.Context, hostId int64) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select params from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityHostSimply(ctx context.Context, hostId int64) (SecurityHost, error) {
	_result, _retErr := func() (SecurityHost, error) {
		_sqlText := "select id,group_name,version,forecast_version from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityHost{}, _sdb.Error
		}
		var _ret SecurityHost
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityHost{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetSecurityTask(ctx context.Context, id int64) (SecurityTask, error) {
	_result, _retErr := func() (SecurityTask, error) {
		_sqlText := "select * from security_task where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityTask{}, _sdb.Error
		}
		var _ret SecurityTask
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityTask{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetTaskUserEmailByMrId(ctx context.Context, mrId int64) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select user_email from security_task where mr_id=? and host_id > 0 order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, mrId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetVersionByHostId(ctx context.Context, hostId int64) (string, error) {
	_result, _retErr := func() (string, error) {
		_sqlText := "select if(version='', forecast_version, version) from security_host where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return "", _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return "", gorm.ErrRecordNotFound
		}
		var _ret string
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return "", _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueByCDInfos(ctx context.Context, hostIds []int64, cdIssueIds []string) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where host_id in (?) and cd_issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds, cdIssueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueByCDIssueIds(ctx context.Context, cdIssueIds []string) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where cd_issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, cdIssueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueIdsByHost0(ctx context.Context, jobName string) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_aggregate_issue where host_id=0 and job_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, jobName)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueIdsByIssueIdsAndAppId(ctx context.Context, jobName string, issueIds []string, appId int64) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_aggregate_issue where job_name=? and issue_id in (?) and (status='approved' or status='false_alarm') and app_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, jobName, issueIds, appId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueIdsByIssueIdsHostId(ctx context.Context, jobName string, issueIds []string, hostId int64) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select issue_id from security_aggregate_issue where job_name=? and issue_id in (?) and (status='approved' or status='false_alarm') and host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, jobName, issueIds, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssueWithReasonByHostJobRule(ctx context.Context, hostId int64, jobName string, ruleId int64) ([]AggregateIssueWithReason, error) {
	_result, _retErr := func() ([]AggregateIssueWithReason, error) {
		_sqlText := "select security_aggregate_issue.issue_id,security_aggregate_issue.status,security_issue_approve_info.reason_info from security_aggregate_issue inner join security_issue_approve_info on security_aggregate_issue.id=security_issue_approve_info.aggregate_issue_id and security_aggregate_issue.host_id=? and security_aggregate_issue.job_name=? and security_aggregate_issue.rule_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, jobName, ruleId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []AggregateIssueWithReason
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret AggregateIssueWithReason
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssuesByHostId(ctx context.Context, hostId int64) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where host_id=? order by id desc"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssuesByHostIdSimply(ctx context.Context, hostId int64) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select issue_id,job_name from security_aggregate_issue where host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssuesByHostJobIssue(ctx context.Context, hostId int64, jobName string, issueIds []string) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where host_id=? and job_name=? and issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, jobName, issueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAggregateIssuesByHostJobRule(ctx context.Context, hostId int64, jobName string, ruleId int64) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where host_id=? and job_name=? and rule_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, jobName, ruleId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAllSecurityChecks(ctx context.Context) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select * from security_check"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAllSecurityTasks(ctx context.Context) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select * from security_task"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListAppVersions(ctx context.Context, groupName string) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select distinct(if(version='', forecast_version, version)) from security_host where group_name=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListApproveInstanceIdsByHostId(ctx context.Context, hostIds []int64) ([]string, error) {
	_result, _retErr := func() ([]string, error) {
		_sqlText := "select distinct(security_issue_approve_info.approve_instance_id) from security_issue_approve_info inner join security_aggregate_issue on security_issue_approve_info.aggregate_issue_id=security_aggregate_issue.id and security_aggregate_issue.host_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []string
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret string
			_err = _rows.Scan(&_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListApprovedIssuesByIssueIdsAndAppId(ctx context.Context, appId int64, issueIds []string) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select * from security_aggregate_issue where app_id =? and issue_id in (?) and (status='approved' or status='false_alarm')"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, appId, issueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCheckMrInfos(ctx context.Context, ids []int64) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select id,cache_key_origin,host_id from security_check where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCheckProjectNames(ctx context.Context, ids []int64) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select id,project_name from security_check where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListChecksForReportStatus(ctx context.Context, checkIds []int64) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select id,app_id,job_name,url,message,start_time,timeout_span,report_time from security_check where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCiTicketByIds(ctx context.Context, ids []int64) ([]SecurityCiTicket, error) {
	_result, _retErr := func() ([]SecurityCiTicket, error) {
		_sqlText := "select * from security_ci_ticket where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCiTicket
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCiTicket
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCiTicketItemByCheckIds(ctx context.Context, checkIds []int64) ([]SecurityCiTicketItem, error) {
	_result, _retErr := func() ([]SecurityCiTicketItem, error) {
		_sqlText := "select security_ci_ticket_item.* from security_ci_ticket_item inner join (select id from security_ci_ticket where check_id in (?)) A on security_ci_ticket_item.ticket_id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCiTicketItem
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCiTicketItem
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCiTicketItemByIssueIds(ctx context.Context, issueId []string) ([]SecurityCiTicketItem, error) {
	_result, _retErr := func() ([]SecurityCiTicketItem, error) {
		_sqlText := "select * from security_ci_ticket_item where issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, issueId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCiTicketItem
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCiTicketItem
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCiTicketItemByTicketId(ctx context.Context, ticketId int64) ([]SecurityCiTicketItem, error) {
	_result, _retErr := func() ([]SecurityCiTicketItem, error) {
		_sqlText := "select * from security_ci_ticket_item where ticket_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ticketId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCiTicketItem
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCiTicketItem
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListCiTicketsByCheckIds(ctx context.Context, checkIds []int64) ([]SecurityCiTicket, error) {
	_result, _retErr := func() ([]SecurityCiTicket, error) {
		_sqlText := "select * from security_ci_ticket where check_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCiTicket
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCiTicket
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListHostTitle(ctx context.Context, hostIds []int64) ([]HostTitle, error) {
	_result, _retErr := func() ([]HostTitle, error) {
		_sqlText := "select host_id,mr_title from security_task where host_id in (?) group by host_id order by null"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []HostTitle
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret HostTitle
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListHostsByAppVersionForMetric(ctx context.Context, groupName string, version string, hostStatus string) ([]SecurityHost, error) {
	_result, _retErr := func() ([]SecurityHost, error) {
		_sqlText := "select * from security_host where group_name=? and (version=? or version='' and forecast_version=?) and host_status!=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName, version, version, hostStatus)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityHost
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityHost
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssueApproveInfoStatuses(ctx context.Context, approveInstanceId string) ([]SecurityIssueApproveInfo, error) {
	_result, _retErr := func() ([]SecurityIssueApproveInfo, error) {
		_sqlText := "select aggregate_issue_id,approve_status,approve_instance_id from security_issue_approve_info where approve_instance_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, approveInstanceId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssueApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssueApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssueApproveInfos(ctx context.Context, aggregateIssueIds []int64) ([]SecurityIssueApproveInfo, error) {
	_result, _retErr := func() ([]SecurityIssueApproveInfo, error) {
		_sqlText := "select * from security_issue_approve_info where aggregate_issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, aggregateIssueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssueApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssueApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssueApproveInfosByHostId(ctx context.Context, hostId int64) ([]SecurityIssueApproveInfo, error) {
	_result, _retErr := func() ([]SecurityIssueApproveInfo, error) {
		_sqlText := "select security_issue_approve_info.* from security_issue_approve_info inner join security_aggregate_issue on security_issue_approve_info.aggregate_issue_id=security_aggregate_issue.id and security_aggregate_issue.host_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssueApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssueApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssueApproveInstanceId(ctx context.Context, aggregateIssueIds []int64) ([]SecurityIssueApproveInfo, error) {
	_result, _retErr := func() ([]SecurityIssueApproveInfo, error) {
		_sqlText := "select max(id) as id,aggregate_issue_id,approve_instance_id from security_issue_approve_info where aggregate_issue_id in (?) group by approve_instance_id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, aggregateIssueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssueApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssueApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssueByIssueIdsAndHostIds(ctx context.Context, hostIds []int64, issueIds []string) ([]SecurityIssue, error) {
	_result, _retErr := func() ([]SecurityIssue, error) {
		_sqlText := "select security_issue.issue_id,security_issue.check_id from security_task_check_relation inner join security_issue on security_task_check_relation.check_id=security_issue.check_id and security_task_check_relation.host_id in (?) and security_issue.issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds, issueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssuesByIssueIds(ctx context.Context, issueIds []string) ([]SecurityIssue, error) {
	_result, _retErr := func() ([]SecurityIssue, error) {
		_sqlText := "select * from security_issue where issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, issueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListIssuesForApply(ctx context.Context, checkIds []int64, issueIds []string) ([]SecurityIssue, error) {
	_result, _retErr := func() ([]SecurityIssue, error) {
		_sqlText := "select * from security_issue where check_id in (?) and issue_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds, issueIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostId(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select security_task.* from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostIdSimply(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select security_task.id,jobs,cony_status from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostIdSimply2(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select app_id,jobs,meego_id,meego_project_key from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostIdSimply3(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select app_id,security_task.id,project_name,jobs,cony_status,meego_id,meego_project_key from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostIdSimply4(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select security_task.id,group_name,jobs,cony_status from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTaskByHostIdSimply5(ctx context.Context, hostId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select security_task.id,app_id,host_id,start_time,group_name,jobs,cony_status,mr_task_id from security_task inner join (select max(id) as id from security_task where host_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLastestTasksByMeego(ctx context.Context, projectKey string, storyId int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select security_task.* from security_task inner join (select max(id) as id from security_task where meego_project_key=? and meego_id=? group by project_id order by null) A on security_task.id=A.id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectKey, storyId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListLatestBPEACheckIdsByHostId(ctx context.Context, hostId int64) (SecurityTaskCheckRelation, error) {
	_result, _retErr := func() (SecurityTaskCheckRelation, error) {
		_sqlText := "select check_id,job_name from security_task_check_relation inner join (select max(id) as id from security_task_check_relation where host_id=? group by job_name order by null) A on A.id=security_task_check_relation.id where job_name='ios_bpea_check' or job_name='android_bpea_check'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return SecurityTaskCheckRelation{}, _sdb.Error
		}
		var _ret SecurityTaskCheckRelation
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return SecurityTaskCheckRelation{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListPackagePermissionsByTaskId(ctx context.Context, taskId int64) ([]SecurityPackageCustomPermission, error) {
	_result, _retErr := func() ([]SecurityPackageCustomPermission, error) {
		_sqlText := "select * from security_package_custom_permission where task_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, taskId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityPackageCustomPermission
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityPackageCustomPermission
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleApproveInfoByHostId(ctx context.Context, hostId int64) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where host_id=? and status!='repaired'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleApproveInfoByHostIds(ctx context.Context, hostIds []int64) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where host_id in (?) and status!='repaired'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleApproveInfoByProcessInstanceIds(ctx context.Context, approveProcessInstanceIds []string) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where approve_process_instance_id in (?) and status!='repaired'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, approveProcessInstanceIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleApproveInfoWithSearch(ctx context.Context, hostId int64, status1 string, userEmail string, status2 string, userEmailLike string) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where host_id=? and (status=? and creator_email=? or status=? and approver_emails like ?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, status1, userEmail, status2, userEmailLike)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleApprovingInfoByHostId(ctx context.Context, hostId int64) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where host_id=? and status in ('approving', 'to_be_reported')"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListRuleCommon(ctx context.Context, ids []int64) ([]SecurityGlobalRuleCommon, error) {
	_result, _retErr := func() ([]SecurityGlobalRuleCommon, error) {
		_sqlText := "select * from security_global_rule_common where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityGlobalRuleCommon
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityGlobalRuleCommon
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityChecks(ctx context.Context, ids []int64) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select * from security_check where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityChecksByTaskIdJobName(ctx context.Context, taskIds []int64, jobName []string) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select * from security_check where task_id in (?) and job_name in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, taskIds, jobName)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityChecksByTaskIds(ctx context.Context, taskIds []int64) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select * from security_check where task_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, taskIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityGlobalRuleDetail(ctx context.Context, ids []int64) ([]SecurityGlobalRuleDetail, error) {
	_result, _retErr := func() ([]SecurityGlobalRuleDetail, error) {
		_sqlText := "select * from security_global_rule_detail where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityGlobalRuleDetail
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityGlobalRuleDetail
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityGlobalRuleDetailByRuleIdsSimplyForIds(ctx context.Context, ids []int64) ([]SecurityGlobalRuleDetail, error) {
	_result, _retErr := func() ([]SecurityGlobalRuleDetail, error) {
		_sqlText := "select id,rule_id from security_global_rule_detail where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityGlobalRuleDetail
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityGlobalRuleDetail
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityGlobalRuleDetailByRuleIndexInfo(ctx context.Context, engine []string, ruleIndexInfo []string) ([]SecurityGlobalRuleDetail, error) {
	_result, _retErr := func() ([]SecurityGlobalRuleDetail, error) {
		_sqlText := "select * from security_global_rule_detail where engine in (?) and rule_index_info in (?) and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, engine, ruleIndexInfo)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityGlobalRuleDetail
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityGlobalRuleDetail
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSecurityTasks(ctx context.Context, ids []int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select * from security_task where id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ids)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSimpleChecksForCache(ctx context.Context, projectId int64, mrIid int64, commitId string, baseCommitId string, cacheKeys []string, jobNames []string) ([]SecurityCheck, error) {
	_result, _retErr := func() ([]SecurityCheck, error) {
		_sqlText := "select id,status,param,job_name,cache_key from security_check where project_id=? and mr_iid=? and commit_id=? and base_commit_id=? and cache_key in (?) and job_name in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectId, mrIid, commitId, baseCommitId, cacheKeys, jobNames)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityCheck
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListSkipAggregateIssueMysqlIds(ctx context.Context, hostId int64, statusNotIn []string) ([]SecurityAggregateIssue, error) {
	_result, _retErr := func() ([]SecurityAggregateIssue, error) {
		_sqlText := "select id,issue_id from security_aggregate_issue where host_id=? and status not in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId, statusNotIn)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityAggregateIssue
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityAggregateIssue
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListTaskCheckRelationByCheckId(ctx context.Context, checkId int64) ([]SecurityTaskCheckRelation, error) {
	_result, _retErr := func() ([]SecurityTaskCheckRelation, error) {
		_sqlText := "select * from security_task_check_relation where check_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTaskCheckRelation
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTaskCheckRelation
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) ListTasksByCheckIds(ctx context.Context, checkIds []int64) ([]SecurityTask, error) {
	_result, _retErr := func() ([]SecurityTask, error) {
		_sqlText := "select distinct(security_task.id),security_task.* from bits.security_task inner join bits.security_task_check_relation on security_task.id=security_task_check_relation.task_id and security_task_check_relation.check_id in (?)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, checkIds)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityTask
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityTask
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) RepairListApproveInfosNeedFixOaStatus(ctx context.Context) ([]SecurityRuleApproveInfo, error) {
	_result, _retErr := func() ([]SecurityRuleApproveInfo, error) {
		_sqlText := "select * from security_rule_approve_info where status in ('to_be_reported','approving') and approve_process_instance_id!=''"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityRuleApproveInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityRuleApproveInfo
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) RepairListConfigV2(ctx context.Context) ([]SecurityConfigV2, error) {
	_result, _retErr := func() ([]SecurityConfigV2, error) {
		_sqlText := "select * from security_config_v2"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []SecurityConfigV2
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret SecurityConfigV2
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) RepairListLostSecurityApproveInstance(ctx context.Context) ([]ApproveInstance, error) {
	_result, _retErr := func() ([]ApproveInstance, error) {
		_sqlText := "select * from approve_instance where id>=37 and business_key not in (3916408, 3903167) and update_time>=1627228800 and update_time<=1627833600 and approve_status in ('approved','rejected') order by id"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []ApproveInstance
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret ApproveInstance
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) TransferHostId2AppId(ctx context.Context, hostId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select app_id from security_task where host_id=? limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, hostId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
