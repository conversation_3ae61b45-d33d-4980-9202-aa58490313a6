package meego_handler

import (
	"code.byted.org/devinfra/hagrid/app/check/security/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/service/tcc"
	"code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bytedance/bits/security"
	"code.byted.org/devinfra/hagrid/app/check/security/meego/model"
	"context"
	"testing"
)

var ctx = context.Background()

func init() {
	rpc.Init()
}

func TestSDLCSkip(t *testing.T) {
	tcc.Init()
	req := &security.SDLCSkipReq{
		ProjectId: 0,
		MrIid:     0,
		Operator:  "<EMAIL>",
	}
	resp, err := SDLCSkip(ctx, req)
	if err != nil {
		t.Fatalf("error: %v", err)
	}
	t.Logf("resp: %v", resp)
}

func TestExportExecute(t *testing.T) {
	req := &security.MeegoExecuteReq{
		DevId:     0,
		ProjectId: 114467,
		MrIid:     50902,
		Hosts: []*security.Host{
			{Id: 5387440, GroupName: "TikTok_iOS"},
		},
		UserEmail: "<EMAIL>",
	}
	resp, err := ExportExecute(ctx, req)
	if err != nil {
		t.Fatalf("ExportExecute failed: %v", err)
	}
	t.Logf("resp: %+v", resp)
}

func TestItemChange(t *testing.T) {
	project2Items := []security.ProjectToItems{
		{
			ProjectStatus: model.ExportStatusPending,
		},
	}
	t.Logf("project2Items_1: %+v", project2Items)
	for i := range project2Items {
		project2Items[i].ProjectStatus = model.ExportStatusSuccess
	}
	t.Logf("project2Items_2: %+v", project2Items)
}
