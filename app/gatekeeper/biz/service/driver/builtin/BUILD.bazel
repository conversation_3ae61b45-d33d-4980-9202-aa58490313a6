load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "builtin",
    srcs = [
        "base.go",
        "code_merge.go",
        "code_review.go",
        "codebase_ci.go",
        "dependency_conflict.go",
        "dev_task_stage_pipeline_running.go",
        "integration_check.go",
        "integration_status.go",
        "mobile_mr_check.go",
        "mr_created.go",
        "release_ticket_check.go",
        "release_ticket_lock.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/gatekeeper/biz/service/driver/builtin",
    visibility = ["//visibility:public"],
    deps = [
        "//app/gatekeeper/biz/dal/fg",
        "//app/gatekeeper/biz/dal/rds",
        "//app/gatekeeper/biz/model",
        "//app/gatekeeper/biz/model/protocol",
        "//app/gatekeeper/biz/thirdparty",
        "//app/gatekeeper/biz/utils",
        "//app/gatekeeper/kitex_gen/bits/devops/deploy",
        "//app/gatekeeper/kitex_gen/bits/gatekeeper/process",
        "//app/gatekeeper/kitex_gen/bits/integration/multi",
        "//app/gatekeeper/kitex_gen/bytedance/bits/config_service",
        "//app/gatekeeper/kitex_gen/bytedance/bits/dev",
        "//app/gatekeeper/kitex_gen/bytedance/bits/gatekeeper_mobile",
        "//app/gatekeeper/kitex_gen/bytedance/bits/git_server",
        "//app/gatekeeper/kitex_gen/bytedance/bits/message_center",
        "//idls/byted/devinfra/cd/mono_manager:mono_manager_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/release_ticket/shared:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//libs/bits_err",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "//libs/lark-kit/templates",
        "//libs/lark-kit/templates/lark_card",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_jinzhu_copier//:copier",
        "@com_github_tidwall_gjson//:gjson",
        "@org_byted_code_gopkg_lang_v2//slicex",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "builtin_test",
    srcs = [
        "code_merge_test.go",
        "code_review_test.go",
        "codebase_ci_test.go",
        "dependency_conflict_test.go",
        "integration_check_test.go",
        "integration_status_test.go",
        "mr_created_test.go",
        "release_ticket_check_test.go",
        "release_ticket_lock_test.go",
    ],
    embed = [":builtin"],
    deps = [
        "//app/gatekeeper/biz/dal/fg",
        "//app/gatekeeper/biz/model",
        "//app/gatekeeper/biz/model/protocol",
        "//app/gatekeeper/biz/thirdparty",
        "//app/gatekeeper/biz/utils",
        "//app/gatekeeper/kitex_gen/bits/gatekeeper/process",
        "//app/gatekeeper/kitex_gen/bits/integration/multi",
        "//app/gatekeeper/kitex_gen/bytedance/bits/config_service",
        "//app/gatekeeper/kitex_gen/bytedance/bits/dev",
        "//app/gatekeeper/kitex_gen/bytedance/bits/git_server",
        "//app/gatekeeper/kitex_gen/bytedance/bits/rd_process/tasks",
        "//idls/byted/devinfra/cd/mono_manager:mono_manager_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/release_ticket/shared:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//libs/bits_err",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_smartystreets_goconvey//convey",
        "@com_github_tidwall_gjson//:gjson",
        "@org_byted_code_lang_gg//gptr",
    ],
)
