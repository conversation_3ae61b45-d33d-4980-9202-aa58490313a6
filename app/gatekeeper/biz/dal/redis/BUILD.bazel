load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "redis",
    srcs = [
        "cache.go",
        "exception_counter.go",
        "frequency_control.go",
        "lock.go",
        "poll_record.go",
        "redis.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/gatekeeper/biz/dal/redis",
    visibility = ["//visibility:public"],
    deps = [
        "//app/gatekeeper/biz/model",
        "//app/gatekeeper/biz/utils",
        "//app/gatekeeper/kitex_gen/bits/gatekeeper/process",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_kv_redis_v6//:redis-v6",
        "@org_byted_code_lang_gg//choose",
    ],
)

go_test(
    name = "redis_test",
    srcs = [
        "cache_test.go",
        "exception_counter_test.go",
        "frequency_control_test.go",
        "lock_test.go",
        "poll_record_test.go",
        "redis_test.go",
    ],
    embed = [":redis"],
    deps = [
        "//app/gatekeeper/biz/model",
        "//app/gatekeeper/biz/utils",
        "//app/gatekeeper/kitex_gen/bits/gatekeeper/process",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_smartystreets_goconvey//convey",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_kv_redis_v6//:redis-v6",
    ],
)
