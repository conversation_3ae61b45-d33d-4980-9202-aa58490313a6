load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handler",
    srcs = ["service.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/portal/navigation_bar/api/biz/handler",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/portal:portal_go_proto",
        "//idls/byted/devinfra/portal:portal_go_proto_xrpc_and_kitex_NavigationBarService",
        "//libs/common_lib/consts",
        "//pkg/middlewares/auth",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_middleware_hertz//pkg/app",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
