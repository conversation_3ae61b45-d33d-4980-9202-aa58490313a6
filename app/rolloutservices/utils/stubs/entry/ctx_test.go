package entry

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/constvar"
)

func TestWithRequestContext(t *testing.T) {
	ctx := context.Background()
	ctx = WithRequestContext(ctx,
		WithUserContext("alice", "alice_jwt_token"),
		WithHeaderContext("key1", "value1"),
		WithHeaderContext("key2", "value2"))
	assert.Equal(t, "alice", ctx.Value(constvar.KeyUserName))
	assert.Equal(t, "alice_jwt_token", ctx.Value(constvar.KeyJwtToken))
	assert.Equal(t, "value1", ctx.Value("key1"))
	assert.Equal(t, "value2", ctx.Value("key2"))
}
