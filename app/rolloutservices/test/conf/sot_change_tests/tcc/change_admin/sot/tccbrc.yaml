metadata:
  logicalId: tcc-brc.pac.sot-change-admin
  type: TCC_BATCH_RELEASE_CONFIG
spec:
  regionalResources:
    VGeo-RoW:
      propertyOverrides:
        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCBatchReleaseConfig
        Configs:
          - Region: US-East
            Directory: /default
            Name: test-config
        VRegions:
          - US-East
  template:
    '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCBatchReleaseConfig
    CanaryRelease:
      Interval: 0
      NextBatchMode: Manual
      RollingRatio: 30
    EmergencyRelease: false
    EnvName: prod
    ForceCreateHash: ""
    ProductionRelease:
      Interval: 0
      NextBatchMode: Manual
      RollingRatio: 30
    ReleaseStrategy: Parallel
    ServiceNamespace: rollout.yichao.test
    SingleDCRelease:
      Interval: 0
      NextBatchMode: Manual
      RollingRatio: 30
