package main

import (
	"context"
	"sync/atomic"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/kitexdemopb"
	"code.byted.org/gopkg/env"
)

// KitexDemoServiceImpl implements the last service interface defined in the IDL.
type KitexDemoServiceImpl struct{}

var calledCount atomic.Uint32

// Echo implements the KitexDemoServiceImpl interface.
func (s *KitexDemoServiceImpl) Echo(ctx context.Context, req *kitexdemopb.EchoRequest) (*kitexdemopb.EchoResponse, error) {
	resp := &kitexdemopb.EchoResponse{
		Vdc:     env.VDC(),
		Region:  env.GetCurrentVRegion(),
		Cluster: env.Cluster(),
		Env:     env.Env(),
		Counter: calledCount.Add(1),
		Msg:     req.Msg,
	}
	return resp, nil
}
