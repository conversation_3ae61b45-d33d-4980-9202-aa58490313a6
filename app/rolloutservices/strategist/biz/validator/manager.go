package validator

import (
	"sync"

	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
	strategist "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/strategistpb"
)

var (
	instance *ValidatorManager
	once     sync.Once
)

type ValidatorManager struct {
	validators []Validator
}

// RegisterValidator adds a new validator to the registry.
func (vm *ValidatorManager) RegisterValidator(v Validator) {
	vm.validators = append(vm.validators, v)
}

func vtorExistInList(list *rollout.PreDeployStrategy_ValidatorList, item Validator) bool {
	for _, i := range list.Validators {
		if i == item.GetName() {
			return true
		}
	}
	return false
}

// ValidateResources takes a list of resources and validates each one with all compatible validators.
func (vm *ValidatorManager) ValidateResources(resources []*rollout.Resource, predeployStrategy *rollout.PreDeployStrategy) map[string]*strategist.ValidatorResultList {
	result := make(map[string]*strategist.ValidatorResultList)
	for _, resource := range resources {
		var resultList []*strategist.ValidatorResult
		for _, validator := range vm.validators {
			if _, exist := predeployStrategy.TypeValidators[resource.Metadata.Type.String()]; !exist {
				continue
			}
			if !vtorExistInList(predeployStrategy.TypeValidators[resource.Metadata.Type.String()], validator) {
				continue
			}
			if validator.Compatible(resource) {
				resultList = append(resultList, validator.Validate(resource))
			}
		}
		if len(resultList) > 0 {
			result[resource.Metadata.LogicalID] = &strategist.ValidatorResultList{Results: resultList}
		}

	}
	return result
}

// GetValidatorManager returns the singleton instance of the ValidatorManager.
func GetValidatorManager() *ValidatorManager {
	once.Do(func() {
		instance = &ValidatorManager{}
	})
	return instance
}
