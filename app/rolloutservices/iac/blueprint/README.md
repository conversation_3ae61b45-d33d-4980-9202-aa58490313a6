# Blueprint Service

Blueprint service is a ByteFaas service deployed in CN region that acts as a server for iac-cli and iac action

## Background, Issues and Proposals
Please refer to this [documentation](https://bytedance.feishu.cn/wiki/wikcnTjDLK3JXelSeZqtBaky9gf)

## Design
Please refer to this [documentation](https://bytedance.feishu.cn/wiki/wikcnembfH1rIV48UBqC7H9YlHb)

## Development
### Pre Merge Request
Run `bazel run //:gazelle-update`

## Links
[SCM](https://cloud.bytedance.net/scm/detail/291160/versions)
[FaaS CN prod](https://cloud.bytedance.net/faas/function/ofqqha6h/cluster/detail?cluster=faas-cn-north&region=cn-north)   
[FaaS BOE](https://cloud-boe.bytedance.net/faas/function/qwnebwdx/cluster/detail?cluster=faas-cn-north&region=cn-north)   
[Event Bus CN Prod](https://cloud.bytedance.net/eventbus_cn/eventv2/infra.rd.iac.execute?region=CN)   
[Event Bus BOE](https://cloud-boe.bytedance.net/eventbus_cn/eventv2/test.yongninghu.platoform.execute?region=BOE)

