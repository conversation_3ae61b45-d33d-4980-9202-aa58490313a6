package main

import (
	"code.byted.org/bytefaas/eventbus"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/consumers/alert/handler"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/kms"
)

func main() {
	// no need to change
	// Start input params
	// 1. eventbus single event handler, cannot be nil
	// 2. eventbus batch event handler, cannot be nil
	// 3. option other handlers
	kms.Init()
	eventbus.Start(handler.EBHandler, handler.EBBatchHandler, eventbus.With<PERSON>om<PERSON>Handler(handler.CommonHandler))
}
