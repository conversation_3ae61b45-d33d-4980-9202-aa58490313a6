load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "batchquery",
    srcs = ["batchquery.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/cli/cmd/deployment/deprecated/batchquery",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutservices/cli/cmd/util",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//idls/byted/devinfra/rolloutapp/entry:entry_go_proto",
        "//libs/errors",
        "@com_github_gookit_color//:color",
        "@com_github_spf13_cobra//:cobra",
    ],
)

go_test(
    name = "batchquery_test",
    srcs = ["batchquery_test.go"],
    embed = [":batchquery"],
    env = {
        "HOME": "$(HOME)",
    },
    deps = [
        "//app/rolloutservices/iac/cli/security",
        "//app/rolloutservices/utils/parser",
        "//app/rolloutservices/utils/stubs/entry",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//idls/byted/devinfra/rolloutapp/entry:entry_go_proto",
        "//libs/errors",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_stretchr_testify//assert",
    ],
)
