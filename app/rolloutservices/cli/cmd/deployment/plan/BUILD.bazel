load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "plan",
    srcs = ["plan.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/cli/cmd/deployment/plan",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutdeploymentrpc/pkg/specutil",
        "//app/rolloutservices/cli/cmd/util",
        "//app/rolloutservices/cli/rolloutcfg",
        "//app/rolloutservices/utils/comparator",
        "//app/rolloutservices/utils/parser",
        "//idls/byted/devinfra/rolloutapp/entry:entry_go_proto",
        "//idls/byted/devinfra/rolloutapp/generator:generator_go_proto",
        "//libs/errors",
        "@com_github_briandowns_spinner//:spinner",
        "@com_github_spf13_cobra//:cobra",
    ],
)

go_test(
    name = "plan_test",
    srcs = ["plan_test.go"],
    embed = [":plan"],
    env = {
        "HOME": "$(HOME)",
    },
    deps = [
        "//app/rolloutservices/iac/cli/security",
        "//app/rolloutservices/utils/stubs/entry",
        "//idls/byted/devinfra/rolloutapp/entry:entry_go_proto",
        "@com_github_bytedance_mockey//:mockey",
    ],
)
