package hagrid

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/app/space/pkg/dal/cache"
	"code.byted.org/devinfra/hagrid/app/space/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/space/pkg/dal/mysql/entity/hagrid"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"
)

func GetEnabledPluginList(ctx context.Context, spaceID uint64) ([]*hagrid.Plugin, error) {
	if plugins, found := cache.GetList[hagrid.Plugin](ctx, "get_enabled_plugin_list_"+strconv.FormatUint(spaceID, 10)); found && len(plugins) > 0 {
		return plugins, nil
	}
	enablePluginIDs := make([]int64, 0)
	res := mysql.HagridDB.WithContext(ctx).Clauses(dbresolver.Write).Model(&hagrid.SpacePlugin{}).Where("space_id = ? ", spaceID).Pluck("plugin_id", &enablePluginIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	plugins := make([]*hagrid.Plugin, 0)
	res = mysql.HagridDB.WithContext(ctx).Where("id in (?)", enablePluginIDs).Find(&plugins)
	if res.Error != nil {
		return nil, res.Error
	}
	if len(plugins) > 0 {
		_ = cache.Set(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(spaceID, 10), plugins, time.Hour)
	}
	return plugins, nil
}

func ListSpacePlugins(ctx context.Context, spaceIDs []uint64, pluginIDs []int64) (r []*hagrid.SpacePlugin, err error) {
	err = mysql.HagridDB.Clauses(dbresolver.Write).WithContext(ctx).Where("space_id in (?) and plugin_id in (?)", spaceIDs, pluginIDs).Find(&r).Error
	if utils.MysqlNoRows(err) {
		err = nil
	}
	return
}

func GetSpacePluginByID(ctx context.Context, spaceID uint64, plugin int64) (*hagrid.SpacePlugin, error) {
	if spacePlugin, found := cache.Get[hagrid.SpacePlugin](ctx, "get_space_plugin_by_id_"+strconv.FormatUint(spaceID, 10)+"_"+strconv.FormatInt(plugin, 10)); found {
		return spacePlugin, nil
	}
	spacePlugin := &hagrid.SpacePlugin{}
	res := mysql.HagridDB.WithContext(ctx).Where("space_id = ? and plugin_id = ?", spaceID, plugin).First(spacePlugin)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		return nil, res.Error
	}
	if spacePlugin == nil || res.Error == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("plugin %d not enabled for space %d", plugin, spaceID)
	}
	_ = cache.Set(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(spaceID, 10)+"_"+strconv.FormatInt(plugin, 10), spacePlugin, 5*time.Minute)
	return spacePlugin, nil
}

func GetPluginByIDs(ctx context.Context, ids []int64) ([]*hagrid.Plugin, error) {
	plugins := make([]*hagrid.Plugin, 0)
	res := mysql.HagridDB.WithContext(ctx).Where("id in (?)", ids).Find(&plugins)
	if res.Error != nil {
		return nil, res.Error
	}
	return plugins, nil
}

func GetPluginByID(ctx context.Context, id int64) (*hagrid.Plugin, error) {
	if plugin, found := cache.Get[hagrid.Plugin](ctx, "get_plugin_by_id_"+strconv.FormatInt(id, 10)); found && plugin.ID != 0 {
		return plugin, nil
	}
	plugin := &hagrid.Plugin{}
	res := mysql.HagridDB.WithContext(ctx).Where("id = ?", id).First(plugin)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		return nil, res.Error
	}
	if plugin == nil || res.Error == gorm.ErrRecordNotFound {
		return nil, nil
	}
	_ = cache.Set(ctx, "get_plugin_by_id_"+strconv.FormatInt(id, 10), plugin, 5*time.Minute)
	return plugin, nil
}

func EnablePluginForSpace(ctx context.Context, spaceID uint64, pluginID, bizID int64, tx *gorm.DB) (*hagrid.SpacePlugin, error) {
	if tx == nil {
		tx = mysql.HagridDB.WithContext(ctx)
	}
	sp := &hagrid.SpacePlugin{
		SpaceID:  spaceID,
		PluginID: pluginID,
		BizID:    bizID,
	}
	res := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "space_id"}, {Name: "plugin_id"}},
		DoNothing: true,
	}).Create(sp)
	if res.Error != nil {
		return nil, res.Error
	}
	_ = cache.Set(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(spaceID, 10)+"_"+strconv.FormatInt(pluginID, 10), sp, 5*time.Minute)
	_ = cache.Set(ctx, "get_space_by_biz_id_and_plugin_id_"+strconv.FormatInt(bizID, 10)+"_"+strconv.FormatInt(pluginID, 10), sp, 5*time.Minute)
	_ = cache.Del(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(spaceID, 10))
	_ = cache.Del(ctx, "get_enable_plugin_info_"+strconv.FormatUint(spaceID, 10))
	_ = cache.Del(ctx, "get_space_list_with_plugin_info")
	return sp, nil
}

func GetSpaceByBizIDAndPluginID(ctx context.Context, bizID, pluginID int64, useCache bool) (*hagrid.Space, error) {
	// if useCache {
	// 	if space, found := cache.Get[hagrid.Space](ctx, "get_space_by_biz_id_and_plugin_id_"+strconv.FormatInt(bizID, 10)+"_"+strconv.FormatInt(pluginID, 10)); found {
	// 		return space, nil
	// 	}
	// }
	sp := &hagrid.SpacePlugin{}
	res := mysql.HagridDB.Clauses(dbresolver.Write).WithContext(ctx).Where("plugin_id = ? and biz_id = ?", pluginID, bizID).First(sp)
	if res.Error != nil && !errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, res.Error
	}
	if sp == nil || errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	space, err := GetSpaceByID(ctx, sp.SpaceID)
	if err != nil {
		return nil, err
	}
	// _ = cache.Set(ctx, "get_space_by_biz_id_and_plugin_id_"+strconv.FormatInt(bizID, 10)+"_"+strconv.FormatInt(pluginID, 10), space, 5*time.Minute)
	return space, nil
}

func MigratePlugin(ctx context.Context, newSpaceID uint64, sp *hagrid.SpacePlugin, tx *gorm.DB) (*hagrid.SpacePlugin, error) {
	if tx == nil {
		tx = mysql.HagridDB.WithContext(ctx)
	}
	oldSpaceID := sp.SpaceID
	sp.SpaceID = newSpaceID
	res := tx.Save(sp)
	if res.Error != nil {
		return nil, res.Error
	}
	_ = cache.Del(ctx, "get_enable_plugin_info_"+strconv.FormatUint(oldSpaceID, 10))
	_ = cache.Del(ctx, "get_enable_plugin_info_"+strconv.FormatUint(newSpaceID, 10))
	_ = cache.Del(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(oldSpaceID, 10))
	_ = cache.Del(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(newSpaceID, 10))
	_ = cache.Del(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(oldSpaceID, 10)+"_"+strconv.FormatInt(sp.PluginID, 10))
	_ = cache.Del(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(newSpaceID, 10)+"_"+strconv.FormatInt(sp.PluginID, 10))
	_ = cache.Del(ctx, "get_space_list_with_plugin_info")
	return sp, nil
}

func DisablePluginForSpace(ctx context.Context, spaceID uint64, pluginID int64, tx *gorm.DB) error {
	if pluginID == 0 {
		return nil
	}
	if tx == nil {
		tx = mysql.HagridDB.WithContext(ctx)
	}
	sp := &hagrid.SpacePlugin{}
	res := tx.Where("space_id = ? and plugin_id = ?", spaceID, pluginID).Find(sp)
	if res.Error != nil {
		return res.Error
	}
	if sp == nil || res.Error == gorm.ErrRecordNotFound {
		return nil
	}
	res = tx.Delete(sp)
	if res.Error != nil {
		return res.Error
	}
	_ = cache.Del(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(spaceID, 10)+"_"+strconv.FormatInt(pluginID, 10))
	_ = cache.Del(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(spaceID, 10))
	_ = cache.Del(ctx, "get_space_list_with_plugin_info")
	return nil
}

func CreatePlugin(ctx context.Context, spaceID uint64, pluginID, bizID int64, tx *gorm.DB) (*hagrid.SpacePlugin, error) {
	if tx == nil {
		tx = mysql.HagridDB.WithContext(ctx)
	}
	sp := &hagrid.SpacePlugin{
		SpaceID:  spaceID,
		PluginID: pluginID,
		BizID:    bizID,
	}
	res := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "space_id"}, {Name: "plugin_id"}, {Name: "biz_id"}},
		DoNothing: true,
	}).Create(sp)
	if res.Error != nil {
		return nil, res.Error
	}
	_ = cache.Set(ctx, "get_space_plugin_by_id_"+strconv.FormatUint(spaceID, 10)+"_"+strconv.FormatInt(pluginID, 10), sp, 5*time.Minute)
	_ = cache.Set(ctx, "get_space_by_biz_id_and_plugin_id_"+strconv.FormatInt(bizID, 10)+"_"+strconv.FormatInt(pluginID, 10), sp, 5*time.Minute)
	_ = cache.Del(ctx, "get_enabled_plugin_list_"+strconv.FormatUint(spaceID, 10))
	_ = cache.Del(ctx, "get_enable_plugin_info_"+strconv.FormatUint(spaceID, 10))
	_ = cache.Del(ctx, "get_space_list_with_plugin_info")
	return sp, nil
}
