package plugin

import (
	entity "code.byted.org/devinfra/hagrid/app/space/pkg/dal/mysql/entity/hagrid"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cdpb/cdrpc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/gopkg/context"
)

type ByteCycleNewPlugin struct {
}

const BizId_Default = 1

var CDRPCClient cdrpc.Client

func init() {
	CDRPCClient = cdrpc.MustNewClient("bits.cd.rpc")
}
func (plugin ByteCycleNewPlugin) Initialize(ctx context.Context, config *entity.Plugin, spaceID uint64, bizID int64, extraData string) error {
	return nil
}

func (plugin ByteCycleNewPlugin) Ping(ctx context.Context, config *entity.Plugin) (bool, error) {
	return false, nil
}

func (plugin ByteCycleNewPlugin) Create(ctx context.Context, config *entity.Plugin, space *entity.Space, story []*rpcpb.RelatedStorySpace, extra string, nameI18n map[string]string) (int64, error) {
	/*
		// 在pluginId=2插件中实现了
		resp, err := CDRPCClient.CreateWorkspaceVarGroup(ctx, &variablepb.CreateWorkspaceVarGroupReq{
			WorkspaceId: space.ID,
			Username:    space.Creator,
		})
		if err != nil {
			return 0, nil
		}
	*/
	return BizId_Default, nil
}

func (plugin ByteCycleNewPlugin) Update(ctx context.Context, config *entity.Plugin, bizID int64, space *entity.Space, story []*rpcpb.RelatedStorySpace, operator, extra string, nameI18n map[string]string) error {
	return nil
}

func (plugin ByteCycleNewPlugin) Delete(ctx context.Context, config *entity.Plugin, spaceID uint64, bizID int64, operator string) error {
	return nil
}

func (plugin ByteCycleNewPlugin) CheckUsable(ctx context.Context, config *entity.Plugin, username string, name string, bytetreeID int64) (bool, string, error) {
	return true, "", nil
}
