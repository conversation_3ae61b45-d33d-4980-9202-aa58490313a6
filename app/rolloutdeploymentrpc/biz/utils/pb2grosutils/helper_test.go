package pb2grosutils

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
)

func TestGetResourceStatusProperties(t *testing.T) {
	data := `{
	 "Meta":{
	   "Id": "123"
	 },
	 "Auth": {
	   "GalaxyInfo": {
	     "NodeId": "123"
	   }
	 }
	}`
	_, err := GetResourceStatusProperties([]byte(data), "ByteDance::TCE::Service")
	assert.NoError(t, err)
}

func TestFetchResourcePropertyMessage(t *testing.T) {
	data := `{
	 "Meta":{
	   "Id": "123"
	 },
	 "Auth": {
	   "GalaxyInfo": {
	     "NodeId": "123"
	   }
	 }
	}`
	m, err := FetchResourcePropertyMessage([]byte(data), "ByteDance::TCE::Service", resourcespb.PropertyCategory_STATUS)
	status, ok := m.(*resourcespb.TCEServiceStatus)
	assert.True(t, ok)
	assert.Equal(t, uint64(123), *status.Meta.Id)
	assert.Equal(t, uint32(123), *status.Auth.GalaxyInfo.NodeId)
	assert.NoError(t, err)
}

func Test_getRawContentByResourceType(t *testing.T) {
	res, err := getRawContentByResourceType(rolloutpb.ResourceType_TCE_CLUSTER, nil)
	assert.NoError(t, err)
	fmt.Println(res)
}

func Test_validateResourceTypeAndGetRawContent(t *testing.T) {
	getAny := func(msg proto.Message) *anypb.Any {
		v, _ := anypb.New(msg)
		return v
	}
	type args struct {
		resType rolloutpb.ResourceType
		any     *anypb.Any
	}

	tests := []struct {
		name    string
		args    args
		want    JsonObject
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "TceCluster",
			args: args{
				resType: rolloutpb.ResourceType_TCE_CLUSTER,
				any: getAny(&resourcespb.TCEClusterProvisionOption{
					BitsPipelineConfig: &resourcespb.TCEClusterProvisionOption_BitsPipelineConfig{},
				}),
			},
			want: map[string]json.RawMessage{
				"BitsPipelineConfig": []byte(`{}`),
			},
			wantErr: assert.NoError,
		},
		{
			name: "TceService",
			args: args{
				resType: rolloutpb.ResourceType_TCE_SERVICE,
				any: getAny(&resourcespb.TCEClusterProvisionOption{
					BitsPipelineConfig: &resourcespb.TCEClusterProvisionOption_BitsPipelineConfig{},
				}),
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "TccBatchReleaseConfig",
			args: args{
				resType: rolloutpb.ResourceType_TCC_BATCH_RELEASE_CONFIG,
				any: getAny(&resourcespb.TCCBatchReleaseConfigProvisionOption{
					TccDeploymentMode: proto.String("AutoRun"),
				}),
			},
			want: map[string]json.RawMessage{
				"TCCDeploymentMode": []byte(`"AutoRun"`),
			},
			wantErr: assert.NoError,
		},
		{
			name: "TccService",
			args: args{
				resType: rolloutpb.ResourceType_TCC_SERVICE,
				any: getAny(&resourcespb.TCCBatchReleaseConfigProvisionOption{
					TccDeploymentMode: proto.String("AutoRun"),
				}),
			},
			want:    nil,
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := validateResourceTypeAndGetRawContent(tt.args.resType, tt.args.any)
			if !tt.wantErr(t, err, fmt.Sprintf("validateResourceTypeAndGetRawContent(%v, %v)", tt.args.resType, tt.args.any)) {
				return
			}
			assert.Equalf(t, tt.want, got, "validateResourceTypeAndGetRawContent(%v, %v)", tt.args.resType, tt.args.any)
		})
	}
}
