load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "tea",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/tea",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_gogo_protobuf//proto",
        "@org_byted_code_data_mario_collector//:mario_collector",
        "@org_byted_code_data_mario_collector//pb_event",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
