package internal

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	. "github.com/bytedance/mockey"
	"github.com/pkg/errors"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/authz"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pauthz"
	authzpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
)

func Test_BatchMigrateTemplateSpaceAuth(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	authz.InitMockClient(ctrl)

	t.Run("success", func(t *testing.T) {
		defer mockey.Mock(mockey.GetMethod(authz.RpcCli, "BatchMigrateResource")).Return(nil, nil).Build().UnPatch()

		err := BatchMigrateTemplateSpaceAuth(ctx, []uint64{123}, 456)
		assert.NoError(t, err)

		err = BatchMigrateTemplateSpaceAuth(ctx, nil, 0)
		assert.NoError(t, err)
	})

	t.Run("failed", func(t *testing.T) {
		defer mockey.Mock(mockey.GetMethod(authz.RpcCli, "BatchMigrateResource")).Return(nil, fmt.Errorf("mock err")).Build().UnPatch()

		err := BatchMigrateTemplateSpaceAuth(ctx, []uint64{123}, 456)
		assert.ErrorContains(t, err, "mock")
	})
}

func Test_GetTemplateRoleBindingWithCache(t *testing.T) {
	ctx := context.Background()
	templateID := uint64(123)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	authz.InitMockClient(ctrl)
	PatchConvey("Test GetTemplateRoleBindingsCache failed", t, func() {
		Mock(redis.GetTemplateRoleBindingsCache).Return(nil, errors.New("cache error")).Build()
		actual, err := GetTemplateRoleBindingWithCache(ctx, templateID)
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
	PatchConvey("Test GetTemplateRoleBindingsCache success and cache not nil", t, func() {
		cache := []*authzpb.RoleBinding{
			{
				ResourceBrn: "resource1",
				RoleBrn:     "role1",
				Principals:  []string{"principal1"},
			},
		}
		Mock(redis.GetTemplateRoleBindingsCache).Return(cache, nil).Build()
		actual, err := GetTemplateRoleBindingWithCache(ctx, templateID)
		So(actual, ShouldEqual, cache)
		So(err, ShouldBeNil)
	})
	PatchConvey("Test GetTemplateRoleBindingsCache success and cache nil", t, func() {
		Mock(redis.GetTemplateRoleBindingsCache).Return(nil, nil).Build()
		Mock(GetMethod(authz.RpcCli, "QueryRoleBinding")).Return(&authzpb.QueryRoleBindingResponse{
			Bindings: []*authzpb.RoleBinding{
				{
					ResourceBrn: "resource1",
					RoleBrn:     "role1",
					Principals:  []string{"principal1"},
				},
			},
		}, nil).Build()
		Mock(redis.SetTemplateRoleBindingCache).Return(nil).Build()
		actual, err := GetTemplateRoleBindingWithCache(ctx, templateID)
		expected := []*authzpb.RoleBinding{
			{
				ResourceBrn: "resource1",
				RoleBrn:     "role1",
				Principals:  []string{"principal1"},
			},
		}
		So(actual, ShouldEqual, expected)
		So(err, ShouldBeNil)
	})
	PatchConvey("Test QueryRoleBinding failed", t, func() {
		Mock(redis.GetTemplateRoleBindingsCache).Return(nil, nil).Build()
		Mock(GetMethod(authz.RpcCli, "QueryRoleBinding")).Return(nil, errors.New("query error")).Build()
		actual, err := GetTemplateRoleBindingWithCache(ctx, templateID)
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
	PatchConvey("Test SetTemplateRoleBindingCache failed", t, func() {
		Mock(redis.GetTemplateRoleBindingsCache).Return(nil, nil).Build()
		Mock(GetMethod(authz.RpcCli, "QueryRoleBinding")).Return(&authzpb.QueryRoleBindingResponse{
			Bindings: []*authzpb.RoleBinding{
				{
					ResourceBrn: "resource1",
					RoleBrn:     "role1",
					Principals:  []string{"principal1"},
				},
			},
		}, nil).Build()
		Mock(redis.SetTemplateRoleBindingCache).Return(errors.New("cache set error")).Build()
		actual, err := GetTemplateRoleBindingWithCache(ctx, templateID)
		expected := []*authzpb.RoleBinding{
			{
				ResourceBrn: "resource1",
				RoleBrn:     "role1",
				Principals:  []string{"principal1"},
			},
		}
		So(actual, ShouldEqual, expected)
		So(err, ShouldBeNil)
	})
}

func Test_GetTemplateAuthWithCache(t *testing.T) {
	ctx := context.Background()
	templateID := uint64(123)
	PatchConvey("Test GetTemplateRoleBindingWithCache failed", t, func() {
		Mock(GetTemplateRoleBindingWithCache).Return(nil, errors.New("get template role binding failed")).Build()
		actual, err := GetTemplateAuthWithCache(ctx, templateID)
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
	PatchConvey("Test GetTemplateRoleBindingWithCache success", t, func() {
		var bindings []*authzpb.RoleBinding
		Mock(GetTemplateRoleBindingWithCache).Return(bindings, nil).Build()
		expectedAuthorizations := RoleBindingToAuthorizations(bindings)
		actual, err := GetTemplateAuthWithCache(ctx, templateID)
		So(actual, ShouldEqual, expectedAuthorizations)
		So(err, ShouldBeNil)
	})
}

func Test_RoleBindingToAuthorizations(t *testing.T) {
	// Mock the external function GetRoleNameFromBrn
	defer Mock(pauthz.GetRoleNameFromBrn).Return("admin").Build().UnPatch()

	PatchConvey("Test with empty bindings", t, func() {
		bindings := []*authzpb.RoleBinding{}
		actual := RoleBindingToAuthorizations(bindings)
		So(actual, ShouldBeEmpty)
	})

	PatchConvey("Test with non-empty bindings", t, func() {
		bindings := []*authzpb.RoleBinding{
			{Principals: []string{"user1"}, RoleBrn: "brn:role:admin"},
			{Principals: []string{"user2", "user3"}, RoleBrn: "brn:role:editor"},
		}

		expected := []*authzpb.Authorization{
			{Principals: []string{"user1"}, RoleName: "admin"},
			{Principals: []string{"user2", "user3"}, RoleName: "admin"},
		}

		actual := RoleBindingToAuthorizations(bindings)
		So(actual, ShouldHaveLength, 2)
		So(actual, ShouldResemble, expected)
	})
}

func Test_BatchGetTemplateAuthWithCache(t *testing.T) {
	ctx := context.Background()
	templateIDs := []uint64{1, 2, 3}

	PatchConvey("Test empty templateIDs", t, func() {
		actual, err := BatchGetTemplateAuthWithCache(ctx, []uint64{})
		So(actual, ShouldBeNil)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test redis.BatchGetTemplateRoleBindingCache failed", t, func() {
		Mock(redis.BatchGetTemplateRoleBindingCache).Return(nil, errors.New("redis error")).Build()
		actual, err := BatchGetTemplateAuthWithCache(ctx, templateIDs)
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test redis.BatchGetTemplateRoleBindingCache success", t, func() {
		cached := map[uint64][]*authzpb.RoleBinding{
			1: {{
				ResourceBrn: "brn::iam:::role:owner",
				RoleBrn:     "brn::iam:::role:role1",
				Principals:  []string{"brn::iam:::user_account:lusong.chn1"},
			}},
			2: {{
				ResourceBrn: "brn::iam:::role:owner",
				RoleBrn:     "brn::iam:::role:role2",
				Principals:  []string{"brn::iam:::user_account:lusong.chn2"},
			}},
		}
		Mock(redis.BatchGetTemplateRoleBindingCache).Return(cached, nil).Build()

		PatchConvey("Test all templateIDs hit cache", func() {
			actual, err := BatchGetTemplateAuthWithCache(ctx, []uint64{1, 2})
			expected := map[uint64][]*authzpb.Authorization{
				1: {{RoleName: "role1", Principals: []string{"brn::iam:::user_account:lusong.chn1"}}},
				2: {{RoleName: "role2", Principals: []string{"brn::iam:::user_account:lusong.chn2"}}},
			}
			So(actual, ShouldEqual, expected)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test some templateIDs miss cache", func() {
			Mock(GetTemplateAuthFromAuthz).Return(map[uint64][]*authzpb.Authorization{
				3: {{RoleName: "role3", Principals: []string{"brn::iam:::user_account:lusong.chn3"}}},
			}, nil).Build()
			actual, err := BatchGetTemplateAuthWithCache(ctx, templateIDs)
			expected := map[uint64][]*authzpb.Authorization{
				1: {{RoleName: "role1", Principals: []string{"brn::iam:::user_account:lusong.chn1"}}},
				2: {{RoleName: "role2", Principals: []string{"brn::iam:::user_account:lusong.chn2"}}},
				3: {{RoleName: "role3", Principals: []string{"brn::iam:::user_account:lusong.chn3"}}},
			}
			So(actual, ShouldEqual, expected)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test GetTemplateAuthFromAuthz failed", func() {
			Mock(GetTemplateAuthFromAuthz).Return(nil, errors.New("authz error")).Build()
			actual, err := BatchGetTemplateAuthWithCache(ctx, templateIDs)
			So(actual, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})
	})
}

func Test_GetTemplateAuthFromAuthz(t *testing.T) {
	ctx := context.Background()
	templateIDs := []uint64{1, 2, 3}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	authz.InitMockClient(ctrl)

	//PatchConvey("Test QueryRoleBinding failed", t, func() {
	//	Mock(GetMethod(authz.RpcCli, "QueryRoleBinding")).Return(nil, errors.New("query role binding failed")).Build()
	//	actual, err := GetTemplateAuthFromAuthz(ctx, templateIDs)
	//	So(actual, ShouldBeNil)
	//	So(err, ShouldNotBeNil)
	//})

	PatchConvey("Test QueryRoleBinding success", t, func() {
		mockResp := &authzpb.QueryRoleBindingResponse{
			Bindings: []*authzpb.RoleBinding{
				{
					ResourceBrn: pauthz.GetTemplateBrnStr(1),
					RoleBrn:     "brn::iam:::role:role1",
					Principals:  []string{"brn::iam:::user_account:lusong.chn1"},
				},
				{
					ResourceBrn: pauthz.GetTemplateBrnStr(2),
					RoleBrn:     "brn::iam:::role:role2",
					Principals:  []string{"brn::iam:::user_account:lusong.chn2"},
				},
				{
					ResourceBrn: pauthz.GetTemplateBrnStr(3),
					RoleBrn:     "brn::iam:::role:role3",
					Principals:  []string{"brn::iam:::user_account:lusong.chn3"},
				},
			},
		}
		Mock(GetMethod(authz.RpcCli, "QueryRoleBinding")).Return(mockResp, nil).Build()

		authMap := map[uint64][]*authzpb.Authorization{
			1: {{RoleName: "role1", Principals: []string{"brn::iam:::user_account:lusong.chn1"}}},
			2: {{RoleName: "role2", Principals: []string{"brn::iam:::user_account:lusong.chn2"}}},
			3: {{RoleName: "role3", Principals: []string{"brn::iam:::user_account:lusong.chn3"}}},
		}

		PatchConvey("Test BatchSetTemplateRoleBindingCache failed", func() {
			Mock(redis.BatchSetTemplateRoleBindingCache).Return(errors.New("set cache failed")).Build()
			actual, err := GetTemplateAuthFromAuthz(ctx, templateIDs)
			So(actual, ShouldEqual, authMap)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test BatchSetTemplateRoleBindingCache success", func() {
			Mock(redis.BatchSetTemplateRoleBindingCache).Return(nil).Build()
			actual, err := GetTemplateAuthFromAuthz(ctx, templateIDs)
			So(actual, ShouldEqual, authMap)
			So(err, ShouldBeNil)
		})
	})

}
