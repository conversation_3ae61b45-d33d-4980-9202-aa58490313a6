// Code generated by COMMENTS_BUILD_TOOLS 2.0.6. DO NOT EDIT.
package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"code.byted.org/appmonitor/orm_tools/bulkinsert"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

type _DBBitsWriteInterfaceStruct struct {
	handler *gorm.DB
}

func NewDBBitsWriteInterface(handler *gorm.DB) DBBitsWriteInterface {
	return &_DBBitsWriteInterfaceStruct{
		handler: handler,
	}
}

var _CacheKeyDBBitsWriteInterfaceTemplate = struct {
}{}
var _DBBitsWriteInterfaceTemplate = struct {
}{}
var GlobalErrDBBitsWriteInterface = struct {
	CacheDemotionErr        error
	DBDemotionErr           error
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
}{
	errors.New("CacheDemotionError"),
	errors.New("DBDemotionError"),
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
}

func (interstruct *_DBBitsWriteInterfaceStruct) InsertPeriodJob(ctx context.Context, periodJob *PeriodJob) (int64, error) {
	if periodJob == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(periodJob)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertPeriodJob occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdatePeriodJob(ctx context.Context, periodJob *PeriodJob) (int64, error) {
	if periodJob == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(periodJob)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdatePeriodJob occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegration(ctx context.Context, integration *Integration) (int64, error) {
	if integration == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(integration)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertIntegration occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertPeriodJobPipeline(ctx context.Context, pipeline *PeriodJobPipelineHistory) (int64, error) {
	if pipeline == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(pipeline)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertPeriodJobPipeline occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegration(ctx context.Context, integration *Integration) (int64, error) {
	if integration == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(integration)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateIntegration occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationHistory(ctx context.Context, integrationHistory *IntegrationHistory) (int64, error) {
	if integrationHistory == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(integrationHistory)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertIntegrationHistory occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationHistory(ctx context.Context, integrationHistory *IntegrationHistory) (int64, error) {
	if integrationHistory == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(integrationHistory)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateIntegrationHistory occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationComponentsArr(ctx context.Context, components []*IntegrationComponents) (int64, error) {
	if len(components) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]*IntegrationComponents, 0, int(math.Ceil(float64(len(components))/2048)))
			chunk    []*IntegrationComponents
		)
		for len(components) > 2048 {
			chunk, components = components[:2048], components[2048:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(components) > 0 {
			chunkSet = append(chunkSet, components[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(components[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				if obj == nil {
					continue
				}
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationComponents(ctx context.Context, component *IntegrationComponents) (int64, error) {
	if component == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(component)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateIntegrationComponents occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationComponentsByIds(ctx context.Context, ids []int64) (int64, error) {
	if len(ids) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_components where id in (?) "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, ids)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationStatus(ctx context.Context, status int, updateTime time.Time, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set status=?,update_time=? where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, status, updateTime, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationWorkflowStatus(ctx context.Context, status int, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set workflow_status=? where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, status, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationGrayTime(ctx context.Context, greyTime int64, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set grey_time=? where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, greyTime, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationGrayEndTime(ctx context.Context, greyEndTime int64, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set grey_end_time=? where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, greyEndTime, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationIsFreeze(ctx context.Context, isFreeze int, freezeTime int64, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set is_freeze=?, freeze_time=? where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, isFreeze, freezeTime, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationById(ctx context.Context, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration set deleted_at=now()  where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayAppCheck(ctx context.Context, grayAppCheck *GrayAppCheck) (int64, error) {
	if grayAppCheck == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(grayAppCheck)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayAppCheck occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteGrayAppCheck(ctx context.Context, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_app_check set deleted=1 where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertAppCheckSceneSetting(ctx context.Context, bitsAppID int64, phase string, types string, updatedAt int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "insert into app_check_scene_setting(`bits_app_id`,`phase`,`types`,`updated_at`) values(?,?, ?, ?) ON DUPLICATE KEY UPDATE types=?, updated_at=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, bitsAppID, phase, types, updatedAt, types, updatedAt)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertGrayIntegrationCheck(ctx context.Context, grayIntegrationCheck *GrayIntegrationCheck) (int64, error) {
	if grayIntegrationCheck == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(grayIntegrationCheck)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertGrayIntegrationCheck occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayIntegrationCheck(ctx context.Context, grayIntegrationCheck *GrayIntegrationCheck) (int64, error) {
	if grayIntegrationCheck == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(grayIntegrationCheck)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayIntegrationCheck occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteGrayIntegrationCheck(ctx context.Context, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set deleted=1 where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckDeadline(ctx context.Context, basicTime int64, integrationID int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set deadline=?+period_to_deadline where integration_id=? and change_time_type = 'relative'"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, basicTime, integrationID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckBeforeNotification(ctx context.Context, time time.Time, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set before_notification_status=1, last_notification_time=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, time, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckAfterNotification(ctx context.Context, time time.Time, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set last_notification_time=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, time, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckTimesAfterNotification(ctx context.Context, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set notification_times=notification_times+1 where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckDisable(ctx context.Context, atomExecId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set disable=1 where deleted=0 and atom_exec_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, atomExecId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckRemarkId(ctx context.Context, remarkId int64, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set remark_id=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, remarkId, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayDeadlineNotificationBlock(ctx context.Context, grayDeadlineNotificationBlock *GrayDeadlineNotificationBlock) (int64, error) {
	if grayDeadlineNotificationBlock == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(grayDeadlineNotificationBlock)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayDeadlineNotificationBlock occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayInfo(ctx context.Context, grayInfo *GrayInfo) (int64, error) {
	if grayInfo == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(grayInfo)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayInfo occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CloseGrayInfo(ctx context.Context, grayId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_info set status_close=1 where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, grayId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayBuild(ctx context.Context, build *GrayBuild) (int64, error) {
	if build == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(build)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayBuild occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayBuildBatch(ctx context.Context, buildBatch []GrayBuild) (int64, error) {
	if len(buildBatch) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]GrayBuild, 0, int(math.Ceil(float64(len(buildBatch))/256)))
			chunk    []GrayBuild
		)
		for len(buildBatch) > 256 {
			chunk, buildBatch = buildBatch[:256], buildBatch[256:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(buildBatch) > 0 {
			chunkSet = append(chunkSet, buildBatch[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(buildBatch[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertGrayLRDemands(ctx context.Context, records []GrayLRDemand) (int64, error) {
	if len(records) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]GrayLRDemand, 0, int(math.Ceil(float64(len(records))/256)))
			chunk    []GrayLRDemand
		)
		for len(records) > 256 {
			chunk, records = records[:256], records[256:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(records) > 0 {
			chunkSet = append(chunkSet, records[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(records[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListAllGrayLRDemandsImmediately(ctx context.Context, integrationId int64) ([]GrayLRDemand, error) {
	_result, _retErr := func() ([]GrayLRDemand, error) {
		_sqlText := "select * from gray_LR_demand where integration_id=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []GrayLRDemand
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = GrayLRDemand{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGrayLRDemand(ctx context.Context, demand *GrayLRDemand) (int64, error) {
	if demand == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(demand)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGrayLRDemand occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListGrayLRDemandsImmediately(ctx context.Context, integrationId int64, pageLeftNotIn int64, pageNum int64) ([]GrayLRDemand, error) {
	_result, _retErr := func() ([]GrayLRDemand, error) {
		_sqlText := "select * from gray_LR_demand where integration_id=? and deleted=0 limit ?,?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, pageLeftNotIn, pageNum)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []GrayLRDemand
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = GrayLRDemand{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountGrayLRDemandsImmediately(ctx context.Context, integrationId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_LR_demand where integration_id=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountGrayLRDemandsImmediatelyIgnoreDeleted(ctx context.Context, integrationId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_LR_demand where integration_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountGrayLRDemandsByStatusImmediately(ctx context.Context, integrationId int64, status string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_LR_demand where integration_id=? and status=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, status)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertOfficialBuildBatch(ctx context.Context, buildBatch []OfficialBuild) (int64, error) {
	if len(buildBatch) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]OfficialBuild, 0, int(math.Ceil(float64(len(buildBatch))/256)))
			chunk    []OfficialBuild
		)
		for len(buildBatch) > 256 {
			chunk, buildBatch = buildBatch[:256], buildBatch[256:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(buildBatch) > 0 {
			chunkSet = append(chunkSet, buildBatch[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(buildBatch[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertOfficialBuild(ctx context.Context, build *OfficialBuild) (int64, error) {
	if build == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(build)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertOfficialBuild occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertRegressionBuildBatch(ctx context.Context, buildBatch []RegressionBuild) (int64, error) {
	if len(buildBatch) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]RegressionBuild, 0, int(math.Ceil(float64(len(buildBatch))/256)))
			chunk    []RegressionBuild
		)
		for len(buildBatch) > 256 {
			chunk, buildBatch = buildBatch[:256], buildBatch[256:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(buildBatch) > 0 {
			chunkSet = append(chunkSet, buildBatch[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(buildBatch[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertRegressionBuild(ctx context.Context, build *RegressionBuild) (int64, error) {
	if build == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(build)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertRegressionBuild occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteOfficialBuild(ctx context.Context, officialBuildId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from official_build where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, officialBuildId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateOfficialBuildTicketIdById(ctx context.Context, reviewTicketId int64, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update official_build set review_ticket_id=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, reviewTicketId, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) GetOfficialBuildByArtifactIdImmediately(ctx context.Context, jobId int64) (OfficialBuild, error) {
	_result, _retErr := func() (OfficialBuild, error) {
		_sqlText := "select * from official_build where artifact_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, jobId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return OfficialBuild{}, _sdb.Error
		}
		var _ret = OfficialBuild{}
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return _ret, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListOfficialBuilds(ctx context.Context, integrationId int64, limitStart int64, limitLength int64) ([]OfficialBuild, error) {
	_result, _retErr := func() ([]OfficialBuild, error) {
		_sqlText := "select * from official_build where integration_id=? order by batch desc,id asc limit ?,?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, limitStart, limitLength)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []OfficialBuild
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = OfficialBuild{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListReservedSubPackagesNeedSyncedByParentJobId(ctx context.Context, parentJobId int64) ([]OfficialBuild, error) {
	_result, _retErr := func() ([]OfficialBuild, error) {
		_sqlText := "select * from official_build where parent_job_id=? and artifact_type in (11,14)"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, parentJobId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []OfficialBuild
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = OfficialBuild{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertGraySetting(ctx context.Context, setting *GraySetting) (int64, error) {
	if setting == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(setting)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertGraySetting occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListRocketDemandsImmediately(ctx context.Context, integrationId int64) ([]GrayLRDemand, error) {
	_result, _retErr := func() ([]GrayLRDemand, error) {
		_sqlText := "select * from gray_LR_demand where integration_id=? and rocket_id!=0 and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []GrayLRDemand
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = GrayLRDemand{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteLRDemand(ctx context.Context, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_LR_demand set deleted=1 where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayLRDemandBeforeNotification(ctx context.Context, time time.Time, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_LR_demand set before_notification_status=1, last_notification_time=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, time, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayLRDemandAfterNotification(ctx context.Context, time time.Time, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_LR_demand set last_notification_time=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, time, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountIntegrationCheckImmediately(ctx context.Context, integrationId int64, grayId int64, t string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_integration_check where integration_id=? and gray_id=? and type=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, grayId, t)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountIntegrationCheckImmediatelyByAtomExecID(ctx context.Context, atomExecID int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_integration_check where atom_exec_id=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, atomExecID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountIntegrationCheckImmediatelyIgnoreDeleted(ctx context.Context, integrationId int64, grayId int64, t string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_integration_check where integration_id=? and gray_id=? and type=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, grayId, t)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) GetIntegrationByIdImmediately(ctx context.Context, id int64) (Integration, error) {
	_result, _retErr := func() (Integration, error) {
		_sqlText := "select * from integration where id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return Integration{}, _sdb.Error
		}
		var _ret = Integration{}
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return _ret, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayInfo(ctx context.Context, grayInfo *GrayInfo) (int64, error) {
	if grayInfo == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(grayInfo)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateGrayInfo occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountGrayIntegrationCheckAccessedImmediately(ctx context.Context, integrationId int64, grayId int64, t string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_integration_check where integration_id=? and gray_id=? and type=? and access=1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, grayId, t)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) CountGrayIntegrationCheckAccessedImmediatelyByAtomExecID(ctx context.Context, atomExecID int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select count(1) from gray_integration_check where atom_exec_id=? and access=1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, atomExecID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) ListGrayIntegrationCheckImmediately(ctx context.Context, integrationId int64, grayId int64, t string) ([]GrayIntegrationCheck, error) {
	_result, _retErr := func() ([]GrayIntegrationCheck, error) {
		_sqlText := "select * from gray_integration_check where integration_id=? and gray_id=? and type=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, integrationId, grayId, t)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []GrayIntegrationCheck
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = GrayIntegrationCheck{}
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationChecksDeadline(ctx context.Context, grayTimestamp int64, integrationId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set deadline=?+period_to_deadline where integration_id=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, grayTimestamp, integrationId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateGrayIntegrationCheckAccess(ctx context.Context, access int8, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update gray_integration_check set access=? where id=? and deleted=0"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, access, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationPhase(ctx context.Context, phase *IntegrationPhase) (int64, error) {
	if phase == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(phase)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertIntegrationPhase occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdaterIntegrationPhase(ctx context.Context, phase *IntegrationPhase) (int64, error) {
	if phase == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(phase)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdaterIntegrationPhase occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationEvent(ctx context.Context, event *IntegrationEvent) (int64, error) {
	if event == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(event)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertIntegrationEvent occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdaterIntegrationEvent(ctx context.Context, event *IntegrationEvent) (int64, error) {
	if event == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(event)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdaterIntegrationEvent occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationArea(ctx context.Context, area *IntegrationArea) (int64, error) {
	if area == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(area)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertIntegrationArea occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationArea(ctx context.Context, area *IntegrationArea) (int64, error) {
	if area == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(area)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateIntegrationArea occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateIntegrationAreaStatus(ctx context.Context, status int, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update integration_area set status = ? where id = ?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, status, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationAreaByIDs(ctx context.Context, areaIDs []int64) (int64, error) {
	if len(areaIDs) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_area where id in (?) "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, areaIDs)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertAreaAppRelationShip(ctx context.Context, relationship *IntegrationAreaAppRelationship) (int64, error) {
	if relationship == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(relationship)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertAreaAppRelationShip occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteAreaAppRelationShipByID(ctx context.Context, relationshipId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_area_app_relationship where id =?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, relationshipId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteAreaAppRelationShip(ctx context.Context, relationshipId int64, applicationId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_area_app_relationship where integration_area_id = ? and application_id =? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, relationshipId, applicationId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertAreaVersionRelationShip(ctx context.Context, relationship *IntegrationAreaVersionRelationship) (int64, error) {
	if relationship == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(relationship)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertAreaVersionRelationShip occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteAreaVersionRelationShipByID(ctx context.Context, relationshipId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_area_version_relationship where id =?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, relationshipId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteAreaVersionRelationShip(ctx context.Context, relationshipId int64, versionId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_area_version_relationship where integration_area_id = ? and version_id =? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, relationshipId, versionId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpsertNotificationSetting(ctx context.Context, setting *NotificationSetting) (int64, error) {
	if setting == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(setting)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpsertNotificationSetting occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationMRByBitsMRID(ctx context.Context, bitsMrID int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_merge_request where bits_mr_id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, bitsMrID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationMRByBitsMRIDAndAppID(ctx context.Context, bitsMrID int64, appID int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_merge_request where bits_mr_id = ? and app_id = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, bitsMrID, appID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) DeleteIntegrationMRByBitsMRIDAppIDAndVersion(ctx context.Context, bitsMrID int64, appID int64, appVersion string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "delete from integration_merge_request where bits_mr_id = ? and app_id = ? and app_version = ? "
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, bitsMrID, appID, appVersion)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertIntegrationMergeRequests(ctx context.Context, mrs []*IntegrationMergeRequest) (int64, error) {
	if len(mrs) == 0 {
		return 0, GlobalErrDBBitsWriteInterface.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		var (
			cnt      int64 = 0
			chunkSet       = make([][]*IntegrationMergeRequest, 0, int(math.Ceil(float64(len(mrs))/64)))
			chunk    []*IntegrationMergeRequest
		)
		for len(mrs) > 64 {
			chunk, mrs = mrs[:64], mrs[64:]
			chunkSet = append(chunkSet, chunk)
		}
		if len(mrs) > 0 {
			chunkSet = append(chunkSet, mrs[:])
		}
		attrsMap, err := bulkinsert.ExtractMapValue(mrs[0], nil)
		if err != nil {
			return cnt, err
		}
		attrs := bulkinsert.SortedKeys(attrsMap)
		attrSize := len(attrs)
		_db := interstruct.handler.Context(ctx)
		dbColumns := make([]string, 0, attrSize)
		for _, key := range attrs {
			dbColumns = append(dbColumns, bulkinsert.Quote(gorm.ToDBName(key), _db.Dialect()))
		}
		for _, objects := range chunkSet {
			mainScope := _db.NewScope(objects[0])
			placeholders := make([]string, 0, len(objects))
			for _, obj := range objects {
				if obj == nil {
					continue
				}
				objAttrs, err := bulkinsert.ExtractMapValue(obj, nil)
				if err != nil {
					return cnt, err
				}
				if len(objAttrs) != attrSize {
					return cnt, GlobalErrDBBitsWriteInterface.AttrSizeInConsistentErr
				}
				scope := _db.NewScope(obj)
				variables := make([]string, 0, attrSize)
				for _, key := range attrs {
					scope.AddToVars(objAttrs[key])
					variables = append(variables, "?")
				}
				valueQuery := "(" + strings.Join(variables, ", ") + ")"
				placeholders = append(placeholders, valueQuery)
				mainScope.SQLVars = append(mainScope.SQLVars, scope.SQLVars...)
			}
			if len(placeholders) != 0 {
				mainScope.Raw(fmt.Sprintf("INSERT INTO %s (%s) VALUES %s",
					mainScope.QuotedTableName(),
					strings.Join(dbColumns, ", "),
					strings.Join(placeholders, ", "),
				))
				err := _db.Exec(mainScope.SQL, mainScope.SQLVars...).Error
				if err != nil {
					return cnt, err
				}
				cnt += int64(len(objects))
			}
		}
		return cnt, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) Begin() (DBBitsWriteInterface, error) {
	tx := interstruct.handler.Begin()
	e := _DBBitsWriteInterfaceStruct(*interstruct)
	e.handler = tx
	return &e, tx.Error
}
func (interstruct *_DBBitsWriteInterfaceStruct) Commit() (*gorm.DB, error) {
	_db := interstruct.handler.Commit()
	return _db, _db.Error
}
func (interstruct *_DBBitsWriteInterfaceStruct) Rollback() (*gorm.DB, error) {
	_db := interstruct.handler.Rollback()
	return _db, _db.Error
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertOpRecord(data *ReleaseOperateRecord) (int64, error) {
	if data == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler
		_sdb := _db.Create(data)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.InsertOpRecord occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) InsertCheckItemRemark(ctx context.Context, checkItemRemark *CheckItemRemark) (int64, error) {
	if checkItemRemark == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(checkItemRemark)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBBitsWriteInterface.InsertCheckItemRemark occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateCheckItemRemark(ctx context.Context, checkItemRemark *CheckItemRemark) (int64, error) {
	if checkItemRemark == nil {
		return 0, GlobalErrDBBitsWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Save(checkItemRemark)
		if _sdb.Error != nil {
			logs.Errorf("DBBitsWriteInterface.UpdateCheckItemRemark occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_DBBitsWriteInterfaceStruct) UpdateCheckItemRemarkContentById(ctx context.Context, content string, id int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update check_item_remark set content=? where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Exec(_sqlText, content, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
