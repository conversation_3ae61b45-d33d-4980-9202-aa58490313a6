load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "conf",
    srcs = [
        "config.go",
        "init.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/conf",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/common_lib/model",
        "@org_byted_code_gopkg_dbutil//conf",
        "@org_byted_code_gopkg_env//:env",
    ],
)
