package integration_test

import (
	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration/integrationservice"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/integration"
	"context"
	"testing"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/devinfra/hagrid/libs/notification-client/consts"
	notification_lark "code.byted.org/devinfra/hagrid/libs/notification-client/lark"
)

var IntegrationClient integrationservice.Client

//func init() {
//	var err error
//
//	conf.Init()
//	db.Init(&conf.Conf.MysqlConf)
//	redis.Init(conf.Conf.RedisConf)
//	rpc.Init()
//
//	if env.IsProduct() {
//		IntegrationClient, err = integrationservice.NewClient("bytedance.bits.integration")
//	} else {
//		IntegrationClient, err = integrationservice.NewClient("bytedance.bits.integration", client.WithHostPorts("127.0.0.1:8888"))
//	}
//	if err != nil {
//		panic(err)
//	}
//}

func TestTfGpReviewCallback(t *testing.T) {
	t.SkipNow()
	failReason := "abcde"
	startTime := time.Now().Unix() - 3700
	endTime := time.Now().Unix()

	msg := &BytedanceBitsIntegration.TfGpReviewCallbackRequest{
		Type:       BytedanceBitsIntegration.ReviewCallbackType_GooglePlay,
		IsSuccess:  true,
		Operator:   "<EMAIL>",
		ArtifactID: 316419,
		StartTime:  &startTime,
		EndTime:    &endTime,
		FailReason: &failReason,
	}

	integration.TfGpReviewCallback(context.Background(), msg)
}

func TestQueryUserInfoByIds(t *testing.T) {
	t.SkipNow()
	ctx := context.Background()
	userID := "c5b6e482"

	userInfos, err := notification_lark.QueryUserInfoByIds(ctx, consts.BitsBot, []string{userID})
	if err != nil || len(userInfos) == 0 {
		logs.CtxError(ctx, "[QueryUserInfoByIds] error:%v", err)
		return
	}
	logs.Info("userInfos: %+v", userInfos[0])
}
