package lib

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/integrate"
	pkgUtils "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/overpass/bytedance_bits_release_workflow/kitex_gen/bytedance/bits/release_workflow"
)

func FormatDBIntegrationForList(ctx context.Context, dbIntegration *db.Integration, appProjectID *int64) *integration.Integration {
	integrationInfo := FormatDBIntegration(ctx, dbIntegration, appProjectID)
	if integrationInfo == nil {
		logs.CtxWarn(ctx, "integration is nil, dbIntegration %v, appProjectID %v", utils.ToJson(dbIntegration), appProjectID)
		return nil
	}
	integrationInfo.TechGrayStatus = GetTechGrayStatus(ctx, integrationInfo.ID)
	integrationInfo.WorkflowTechGrayStatus = getIntegrationWorkflowTechGrayStatus(ctx, integrationInfo.UniqueID, integrationInfo.ID)
	setReleaseStage(ctx, integrationInfo)
	return integrationInfo
}

func FormatDBIntegration(ctx context.Context, integration *db.Integration, appProjectID *int64) *integration.Integration {
	item := integrate.AdaptIntegrationToView(ctx, integration)

	if item == nil {
		logs.CtxWarn(ctx, "item is nil")
		return nil
	}

	if appProjectID == nil || *appProjectID == 0 {
		return item
	}
	if env.IsBoe() {
		logs.CtxWarn(ctx, "The GetMrSimpleInfosByProjectAndVersion interface of the bytedance.bits.optimus service of the boe environment is unavailable, skip it for now")
		return item
	}
	// 兼容头条切换主仓的情形
	if integration.UniqueId == 1301 {
		if legacyRepoID := pkgUtils.AdaptTouTiaoIosRepo(integration.Version); legacyRepoID != nil {
			appProjectID = legacyRepoID
		}
	}
	// 兼容 tiktok lite 与 tiktok 合仓的情形
	reqTags := ""
	if integration.UniqueId == 2020092290 {
		adaptedRepoID, tags := pkgUtils.AdaptTiktokUltraLiteAndroidRepo(integration.Version)
		if adaptedRepoID > 0 {
			appProjectID = &adaptedRepoID
		}
		if tags != nil && len(*tags) > 0 {
			reqTags = *tags
		}
	}

	req := &optimus.GetMrSimpleInfosByProjectAndVersionQuery{
		ProjetcID: *appProjectID,
		Version:   integration.Version,
	}
	if len(reqTags) > 0 {
		req.Tags = reqTags
	}
	resp, err := rpc.OptimusClient.GetMrSimpleInfosByProjectAndVersion(ctx, req, callopt.WithRPCTimeout(time.Second*2))
	if resp == nil || len(resp.MrSimpleInfos) == 0 || err != nil {
		utils.LogCtxInfo(ctx, "GetMrSimpleInfosByProjectAndVersion failed. appId: %v, version: %v, err: %v", appProjectID, integration.Version, err)
		return item
	}
	var mergedCount, closedCount, openingCount int64
	for _, mr := range resp.MrSimpleInfos {
		if mr.Status == optimus.MrState_merged {
			mergedCount += 1
		}
		if mr.Status == optimus.MrState_closed {
			closedCount += 1
		}
		if mr.Status == optimus.MrState_opened {
			openingCount += 1
		}
	}

	item.TaskIntegratedCount = mergedCount
	item.TaskClosedCount = closedCount
	item.TaskOpenCount = openingCount
	item.TaskTotalCount = mergedCount + closedCount + openingCount

	return item
}

func GetIntegrationByID(ctx context.Context, integrationID int64) (*integration.Integration, error) {
	item, err := db.BitsReadDB.GetIntegrationById(ctx, integrationID)
	if item.Id == 0 || err != nil {
		return nil, bits_err.COMMON.ErrRecordNotFound
	}

	return getIntegrationByRaw(ctx, &item)
}

func getIntegrationByRaw(ctx context.Context, integration *db.Integration) (*integration.Integration, error) {
	whereFunc := func(db *gorm.DB) *gorm.DB {
		return db.Where("unique_id = ? and unique_type = ?", integration.UniqueId, integration.UniqueType)
	}

	// 这里主要是为了排序，然后根据开始时间
	allIntegrations, err := GetAllIntegrations(ctx, whereFunc)
	if len(allIntegrations) == 0 || err != nil {
		return nil, bits_err.COMMON.ErrRecordNotFound
	}

	var found *db.Integration
	for i := range allIntegrations {
		if allIntegrations[i].Id == integration.Id {
			found = allIntegrations[i]
			break
		}
	}
	if found == nil {
		return nil, bits_err.COMMON.ErrRecordNotFound
	}

	gitResp, err := rpc.GitServerClient.QueryRepoInfoByAppID(ctx, &git_server.QueryRepoInfoByAppIDRequest{
		MetaAppID: found.UniqueId,
	})
	appProjectID := int64(0)
	if gitResp == nil || gitResp.Repo == nil || err != nil {
		logs.CtxWarn(ctx, "app_id = %v has no repo info, err := %v", integration.UniqueId, err)
	} else {
		appProjectID = gitResp.Repo.ProjectID
	}
	return FormatDBIntegration(ctx, found, &appProjectID), nil
}

func setReleaseStage(ctx context.Context, integrationItem *integration.Integration) {
	if integrationItem.GetWorkflowStatus() == integration.IntegrationWorkflowStatus_Publishing {
		resp, err := rpc.ReleaseWorkflowClient.FindIntegrationReleaseStage(ctx, &release_workflow.FindIntegrationReleaseStageReq{
			IntegrationId: integrationItem.ID,
		})
		if rpcErr := pkgUtils.CheckRPCRespWithErr(resp, err); rpcErr != nil {
			logs.CtxError(ctx, "[setReleaseStage] FindIntegrationReleaseStage failed, rpcErr = %v", rpcErr)
			return
		}

		logs.CtxInfo(ctx, "integration(%d) set release_stage(%s)", integrationItem.ID, resp.Stage)
		integrationItem.SetReleaseStage(&resp.Stage)
	}
}
