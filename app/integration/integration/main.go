package main

import (
	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration/integrationservice"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/conf"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/tos"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/mq"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/pool"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/es"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/period"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/tcc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/utils"
	bits_metrics "code.byted.org/devinfra/hagrid/libs/common_lib/metrics"
	"code.byted.org/devinfra/hagrid/libs/common_lib/middleware"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	_ "code.byted.org/devinfra/hagrid/libs/prelude"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/server"
)

func main() {
	defer utils.Recover()
	conf.Init()
	bits_metrics.Init(env.PSM())
	dal.Init()
	tos.InitTosClient()
	rpc.Init()
	mq.Init()
	tcc.Init()
	es.Init()
	//calender.Init()
	utils.Init()
	// 周期性任务启动
	engine := period.PeriodEngine{
		Duriation: 1,
	}
	engine.Start()
	//协程池
	pool.Init()
	options := []server.Option{
		server.WithMiddleware(middleware.PrintRPCRequest),
		server.WithMiddleware(middleware.FillError),
		server.WithMiddleware(kitexmw.LogRequestResponse),
		server.WithMiddleware(kitexmw.BitsMetrics),
		server.WithMiddleware(kitexmw.WarpError),
	}
	svr := BytedanceBitsIntegration.NewServer(new(IntegrationServiceImpl), options...)
	err := svr.Run()

	if err != nil {
		log.V2.Fatal().Str("failed to run server").Error(err).Emit()
	}
	mq.Stop()
}
