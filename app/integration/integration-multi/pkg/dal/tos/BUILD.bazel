load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "tos",
    srcs = [
        "conn.go",
        "version.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/tos",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration-multi/pkg/dal/tcc",
        "//app/integration/integration-multi/pkg/model",
        "//libs/connections/tos",
        "@com_github_bytedance_sonic//:sonic",
    ],
)

go_test(
    name = "tos_test",
    srcs = [
        "tccConfigSvc_impl_for_test.go",
        "tos_test.go",
    ],
    embed = [":tos"],
    tags = ["known-to-fail"],
    deps = [
        "//app/integration/integration-multi/kitex_gen/bits/integration/multi",
        "//app/integration/integration-multi/kitex_gen/bytedance/bits/dev",
        "//app/integration/integration-multi/kitex_gen/bytedance/bits/devops",
        "//app/integration/integration-multi/pkg/dal/tcc",
        "//app/integration/integration-multi/pkg/model",
    ],
)
