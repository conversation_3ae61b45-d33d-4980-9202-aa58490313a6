package main

import (
	"time"

	"code.byted.org/bytecycle/go-middlewares/xhertz/route"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/i18n"
	"github.com/cloudwego/kitex/client"
	"github.com/cloudwego/kitex/pkg/remote/codec/thrift"
	"github.com/cloudwego/kitex/pkg/remote/trans/nphttp2/grpc"
	"github.com/cloudwego/kitex/pkg/retry"
)

func I18NOption() route.Option {
	storage := i18n.NewServiceStorage("bitsportaltoolset", i18n.WithCacheTTL(time.Second*10))

	mw := i18n.NewMiddleware(storage, i18n.WithUploadText(true))

	return route.WithInterceptor(mw.GetInterceptor())
}

func NewRPCOptions(protobuf bool) []client.Option {
	fp := retry.NewFailurePolicy()
	fp.WithMaxRetryTimes(3)   // 失败重试三次
	fp.WithMaxDurationMS(500) // 间隔 500 毫秒

	var opts = []client.Option{
		client.WithRPCTimeout(1 * time.Minute),
		client.WithMiddleware(kitexmw.LogClientSideRequestResponse),
		client.WithFailureRetry(fp),
	}

	if protobuf { // protobuf
		opts = append(opts, client.WithGRPCKeepaliveParams(grpc.ClientKeepalive{Time: 10 * time.Second, Timeout: 1 * time.Second, PermitWithoutStream: true}))
	} else { // thrift
		opts = append(opts, client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)))
	}
	return opts
}
