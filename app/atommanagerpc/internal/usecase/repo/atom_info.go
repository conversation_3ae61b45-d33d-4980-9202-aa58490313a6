package repo

import (
	"context"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/entity"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/usecase/repo/orm"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/db"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
)

type atomInfoRepo struct{}

// NewAtomInfoRepo ...
func NewAtomInfoRepo() *atomInfoRepo {
	return &atomInfoRepo{}
}

// GetAtomInfoByUniqueId ...
// SELECT * FROM `atom_info` where `unique_id` = ?
func (h *atomInfoRepo) GetAtomInfoByUniqueId(ctx context.Context, dbHandler *db.DB, uniqueId string) *entity.AtomInfo {
	var ormInfo *orm.AtomInfo
	err := dbHandler.WithContext(ctx).Table(orm.AtomInfo{}.TableName()).Clauses(dbresolver.Write).
		Where("unique_id = ?", uniqueId).First(&ormInfo).Error
	if err != nil {
		return MapAtomInfoOrmToEntity(&orm.AtomInfo{UniqueId: uniqueId})
	}
	return MapAtomInfoOrmToEntity(ormInfo)
}

// GetAtomInfoByUniqueIdAndRegion ...
// SELECT * FROM `atom_info` where `unique_id` = ? and `region` = ?;
func (h *atomInfoRepo) GetAtomInfoByUniqueIdAndRegion(ctx context.Context, dbHandler *db.DB, uniqueId string, region atommanagepb.RegionEnum) *entity.AtomInfo {
	var ormInfo *orm.AtomInfo
	err := dbHandler.WithContext(ctx).Table(orm.AtomInfo{}.TableName()).Clauses(dbresolver.Write).
		Where("unique_id = ?", uniqueId).Where("region = ?", int(region)).
		First(&ormInfo).Error
	if err != nil {
		return MapAtomInfoOrmToEntity(&orm.AtomInfo{UniqueId: uniqueId})
	}
	return MapAtomInfoOrmToEntity(ormInfo)
}

// GetAtomInfoLastId ...
// SELECT `id` FROM `atom_info` ORDER BY `id` DESC limit 1
func (h *atomInfoRepo) GetAtomInfoLastId(ctx context.Context, dbHandler *db.DB) (int64, error) {
	var ormInfo *orm.AtomInfo
	err := dbHandler.WithContext(ctx).Table(orm.AtomInfo{}.TableName()).Order("id desc").First(&ormInfo).Error
	if err != nil {
		return 0, err
	}
	return ormInfo.Id, nil
}

// GetAtomInfosByLimitAndOffset ...
// SELECT * FROM `atom_info` limit ? offset ?
func (h *atomInfoRepo) GetAtomInfosByLimitAndOffset(ctx context.Context, dbHandler *db.DB, first int64, limit int) ([]*entity.AtomInfo, error) {
	var ormInfos []*orm.AtomInfo
	err := dbHandler.WithContext(ctx).Table(orm.AtomInfo{}.TableName()).Where("id > ?", first).Limit(limit).Find(&ormInfos).Error
	if err != nil {
		return nil, err
	}
	return BatchMapAtomInfoOrmToEntity(ormInfos), nil
}
