// Code generated from service_atom_meta.sql by cbt. DO NOT EDIT.

package orm

import (
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/entity"
	"gorm.io/gorm"
)

func (ServiceAtomMeta) TableName() string {
	return "service_atom_meta"
}

type ServiceAtomMeta struct {
	Callbackable          bool            `gorm:"column:callbackable" json:"callbackable"`                 // 是否可回调
	Cancellable           bool            `gorm:"column:cancellable" json:"cancellable"`                   // 是否可终止
	ChangeActor           bool            `gorm:"column:change_actor" json:"change_actor"`                 // 原子服务是否可以改写操作人
	CheckBeforeExecute    bool            `gorm:"column:check_before_execute" json:"check_before_execute"` // 运行前是否检查
	Disabled              bool            `gorm:"column:disabled" json:"disabled"`                         // 原子服务是否被禁止
	IsAtomCustomForm      bool            `gorm:"column:is_atom_custom_form" json:"is_atom_custom_form"`   // 是否原子自定义入参
	OverrideJobContext    bool            `gorm:"column:override_job_context" json:"override_job_context"` // 原子服务是否可以覆盖写job context
	Rerunnable            bool            `gorm:"column:rerunnable" json:"rerunnable"`                     // 是否可重试
	Rollbackable          bool            `gorm:"column:rollbackable" json:"rollbackable"`                 // 是否可回滚
	Skippable             bool            `gorm:"column:skippable" json:"skippable"`                       // 是否可跳过
	DefaultTimeout        uint32          `gorm:"column:default_timeout" json:"default_timeout"`           // 原子服务调度默认超时时间设置
	ServiceLanguage       int32           `gorm:"column:service_language" json:"service_language"`         // 服务语言
	ServiceType           int32           `gorm:"column:service_type" json:"service_type"`                 // 服务类型
	Id                    int64           `gorm:"column:id;primary_key" json:"id"`                         // 主键id
	JobAtomId             int64           `gorm:"column:job_atom_id" json:"job_atom_id"`                   // 对应的job atom id
	PipelineId            uint64          `gorm:"column:pipeline_id" json:"pipeline_id"`                   // 正式发布流水线id
	GalaxyId              int64           `gorm:"column:galaxy_id" json:"galaxy_id"`                       // 正式发布流水线id
	CustomizedForm        string          `gorm:"column:customized_form" json:"customized_form"`           // 自定义表单
	DestroySwitch         bool            `gorm:"column:destroy_switch" json:"destroy_switch"`
	DestroyMethod         string          `gorm:"column:destroy_method" json:"destroy_method"`
	DestroyTrigger        string          `gorm:"column:destroy_trigger" json:"destroy_trigger"`
	AtomDestroySwitch     bool            `gorm:"column:atom_destroy_switch" json:"atom_destroy_switch"`
	AtomDestroyMethod     string          `gorm:"column:atom_destroy_method" json:"atom_destroy_method"`
	AtomDestroyTrigger    string          `gorm:"column:atom_destroy_trigger" json:"atom_destroy_trigger"`
	Domain                string          `gorm:"column:domain" json:"domain"`                                       // 调用domain
	GitRepo               string          `gorm:"column:git_repo" json:"git_repo"`                                   // 仓库地址
	Outputs               string          `gorm:"column:outputs" json:"outputs"`                                     // 输出配置
	ParamsCheckMethod     string          `gorm:"column:params_check_method" json:"params_check_method"`             // 要检查的函数名
	Psm                   string          `gorm:"column:psm" json:"psm"`                                             // psm
	PublishRecord         string          `gorm:"column:publish_record" json:"publish_record"`                       // 发布记录（跳转链接）
	Reason                string          `gorm:"column:reason" json:"reason"`                                       // 申请原因
	RollbackAtomUniqueId  string          `gorm:"column:rollback_atom_unique_id" json:"rollback_atom_unique_id"`     // 绑定的处理回滚原子服务的unique id
	ScmRepoName           string          `gorm:"column:scm_repo_name" json:"scm_repo_name"`                         // scm仓库名称
	DeletedAt             *gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                               // 删除时间
	ArtifactOutputs       string          `gorm:"column:artifact_outputs" json:"artifact_outputs"`                   // 产物输出
	Region                int32           `gorm:"column:region" json:"region"`                                       // 部署机房
	IsI18n                bool            `gorm:"column:is_i18n" json:"is_i18n"`                                     // 是否部署在 i18n（仅对 region 为 1 的数据有效，region 为 1 的数据包括 CN 控制面和 i18n 控制面）
	Status                int32           `gorm:"column:status" json:"status"`                                       // status
	GoofyID               string          `gorm:"column:fe_goofy_id" json:"goofy_id"`                                // 前端原子 goofy id
	FERepo                string          `gorm:"column:fe_repo" json:"fe_repo"`                                     // 前端原子 新建前端codebase repo
	FEUpgradePipelineId   uint64          `gorm:"column:fe_upgrade_pipeline_id" json:"fe_upgrade_pipeline_id"`       // 前端原子 升级流水线pipeline id
	FECreatePipelineRunId uint64          `gorm:"column:fe_create_pipeline_run_id" json:"fe_create_pipeline_run_id"` // 前端原子 创建新仓库流水线pipeline run id
	FEDetailDevType       int32           `gorm:"column:fe_detail_dev_type" json:"fe_detail_dev_type"`               // 前端原子 fe_detail_dev_type
	FeIframeAction        int32           `gorm:"column:fe_iframe_action" json:"fe_iframe_action"`                   // 前端原子 fe_iframe_action
	FEMfEntry             string          `gorm:"column:fe_mf_entry" json:"fe_mf_entry"`                             // 前端原子 fe_mf_entry
	CreatePipelineRunID   uint64          `gorm:"column:create_pipeline_run_id" json:"create_pipeline_run_id"`       // 前端原子 fe_mf_entry
	SubRegion             int32           `gorm:"sub_region" json:"sub_region"`                                      // 应对i18n控制面拆分，目前初步确定i18n拆分为i18n-tt和i18n-bd，仅档region为1时有效，后面完成控制面拆分后，考虑下掉 is_i18n字段

}

func NewServiceAtomMeta() *ServiceAtomMeta {
	return &ServiceAtomMeta{}
}

func ParseServiceAtomMeta(meta *entity.ServiceAtomMeta) *ServiceAtomMeta {
	m := &ServiceAtomMeta{
		IsI18n: meta.IsI18n,
	}
	if meta.GitRepo != "" {
		m.GitRepo = meta.GitRepo
	}
	if meta.Psm != "" {
		m.Psm = meta.Psm
	}
	if meta.Domain != "" {
		m.Domain = meta.Domain
	}
	if meta.ScmRepoName != "" {
		m.ScmRepoName = meta.ScmRepoName
	}
	if meta.GalaxyId > 0 {
		m.GalaxyId = meta.GalaxyId
	}
	if meta.Status > 0 {
		m.Status = int32(meta.Status)
	}
	if meta.PipelineID != 0 {
		m.PipelineId = meta.PipelineID
	}
	if len(meta.GoofyID) > 0 {
		m.GoofyID = meta.GoofyID
	}
	if len(meta.FERepo) > 0 {
		m.FERepo = meta.FERepo
	}
	if meta.FEUpgradePipelineID > 0 {
		m.FEUpgradePipelineId = meta.FEUpgradePipelineID
	}
	if meta.FECreatePipelineRunID > 0 {
		m.FECreatePipelineRunId = meta.FECreatePipelineRunID
	}
	if meta.FEDetailDevType > 0 {
		m.FEDetailDevType = meta.FEDetailDevType
	}
	if meta.FeIframeAction > 0 {
		m.FeIframeAction = meta.FeIframeAction
	}
	if len(meta.FEMfEntry) > 0 {
		m.FEMfEntry = meta.FEMfEntry
	}
	return m
}
