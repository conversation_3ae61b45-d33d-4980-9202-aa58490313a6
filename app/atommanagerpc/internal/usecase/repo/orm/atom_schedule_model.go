// Code generated from atom_schedule_model.sql by cbt. DO NOT EDIT.

package orm

import (
	"gorm.io/gorm"
)

func (AtomScheduleModel) TableName() string {
	return "atom_schedule_model"
}

type AtomScheduleModel struct {
	Deleted     int8            `gorm:"column:deleted" json:"deleted"`           // 是否删除
	InputType   int32           `gorm:"column:input_type" json:"input_type"`     // 1 normal 2 dynamic
	Timeout     int32           `gorm:"column:timeout" json:"timeout"`           // 服务类默认timeout
	AtomId      int64           `gorm:"column:atom_id" json:"atom_id"`           // 原子能力id
	Id          int64           `gorm:"column:id;primary_key" json:"id"`         // 主键id
	AtomType    string          `gorm:"column:atom_type" json:"atom_type"`       // 原子类型
	CancelUrl   string          `gorm:"column:cancel_url" json:"cancel_url"`     // 取消url
	EnglishName string          `gorm:"column:english_name" json:"english_name"` // 服务名
	InitUrl     string          `gorm:"column:init_url" json:"init_url"`         // 初始化url
	Input       string          `gorm:"column:input" json:"input"`               // 输入信息
	Output      string          `gorm:"column:output" json:"output"`             // 输出信息
	ServiceDesc string          `gorm:"column:service_desc" json:"service_desc"` // 服务描述
	SkipUrl     string          `gorm:"column:skip_url" json:"skip_url"`         // 跳过三方任务的url
	TriggerUrl  string          `gorm:"column:trigger_url" json:"trigger_url"`   // 触发url
	Version     string          `gorm:"column:version" json:"version"`           // 版本信息表
	FromType    int8            `gorm:"column:from_type" json:"from_type"`       // 1表示来自OneSite的数据
	DeletedAt   *gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`     // 删除时间
}

func NewAtomScheduleModel() *AtomScheduleModel {
	return &AtomScheduleModel{}
}
