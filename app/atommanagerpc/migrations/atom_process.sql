CREATE TABLE `atom_process` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `process_instance_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核id',
    `editor` text COLLATE utf8mb4_unicode_ci COMMENT '修改信息',
    `process_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '审核状态',
    `reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '原因',
    `author` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '作者',
    `process_reason` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批原因',
    `is_partial_published` tinyint(1) unsigned zerofill NOT NULL COMMENT '是否部分发布',
    `process_status_enum` int DEFAULT NULL COMMENT 'onesite 审核状态',
    `approvers` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批人',
    `job_atom_id` bigint unsigned NOT NULL COMMENT '原子能力id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原子能力审核信息暂存表';