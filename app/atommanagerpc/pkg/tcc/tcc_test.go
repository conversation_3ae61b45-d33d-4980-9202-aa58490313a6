package tcc

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"code.byted.org/gopkg/tccclient"
	"github.com/bytedance/mockey"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestGetStringSlice(t *testing.T) {
	Init("bits.atommanage.rpc")

	administrators := GetStringSlice(context.Background(), "administrators")
	fmt.Println(administrators)
	assert.NotEqual(t, len(administrators), 0)
}

// Test_GetJobAtomOfficeTag 测试 GetJobAtomOfficeTag 函数
func Test_GetJobAtomOfficeTag(t *testing.T) {
	Init("bits.atommanage.rpc")

	mockey.PatchConvey("测试GetJobAtomOfficeTag函数", t, func() {
		mockey.PatchConvey("获取配置失败的场景", func() {
			// 模拟client.Get返回错误
			mockGet := mockey.Mock((*tccclient.ClientV2).Get).Return("", errors.New("get config error")).Build()
			ctx := context.Background()
			result, err := GetJobAtomOfficeTag(ctx)
			fmt.Println(result, err, mockGet)
		})

		mockey.PatchConvey("JSON解析失败的场景", func() {
			// 模拟client.Get返回非JSON格式的字符串
			mockGet := mockey.Mock((*tccclient.ClientV2).Get).Return("not a valid json", nil).Build()
			// 模拟json.Unmarshal返回错误
			mockUnmarshal := mockey.Mock(json.Unmarshal).Return(errors.New("json unmarshal error")).Build()
			ctx := context.Background()
			result, err := GetJobAtomOfficeTag(ctx)
			fmt.Println(result, err, mockGet, mockUnmarshal)
		})

		mockey.PatchConvey("正常获取配置并解析的场景", func() {
			// 模拟client.Get返回有效的JSON字符串
			mockJSON := `[{"cn": "中文标签", "en": "English tag", "unique_ids": ["id1", "id2"]}]`
			mockGet := mockey.Mock((*tccclient.ClientV2).Get).Return(mockJSON, nil).Build()
			// 模拟json.Unmarshal成功解析
			mockUnmarshal := mockey.Mock(json.Unmarshal).Return(nil).Build()
			ctx := context.Background()
			result, err := GetJobAtomOfficeTag(ctx)
			fmt.Println(result, err, mockGet, mockUnmarshal)
		})
	})
}
