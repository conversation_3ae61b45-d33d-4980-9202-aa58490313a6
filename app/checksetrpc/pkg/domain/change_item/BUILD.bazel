load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "change_item",
    srcs = [
        "change_item.go",
        "service.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/domain/change_item",
    visibility = ["//visibility:public"],
    deps = [
        "//app/checksetrpc/pkg/dal/mysql/entity",
        "//app/checksetrpc/pkg/dal/mysql/repository",
        "//idls/byted/devinfra/cd/project:project_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "change_item_test",
    srcs = ["change_item_test.go"],
    embed = [":change_item"],
    deps = [
        "//app/checksetrpc/pkg/dal/mysql",
        "//app/checksetrpc/pkg/dal/mysql/entity",
        "//app/checksetrpc/pkg/dal/mysql/repository/mock",
        "//app/checksetrpc/testfactory",
        "//idls/byted/devinfra/cd/project:project_go_proto",
        "@com_github_golang_mock//gomock",
        "@com_github_stretchr_testify//suite",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_bits_monkey//:monkey",
    ],
)
