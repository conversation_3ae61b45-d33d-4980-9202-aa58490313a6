load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = ["utils.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/storagerpc/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/storage/shared:shared_go_proto",
        "//pkg/tcc/apis",
    ],
)

go_test(
    name = "utils_test",
    srcs = ["utils_test.go"],
    embed = [":utils"],
    deps = [
        "//idls/byted/devinfra/storage/shared:shared_go_proto",
        "//pkg/tcc/apis",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
        "@com_github_stretchr_testify//assert",
    ],
)
