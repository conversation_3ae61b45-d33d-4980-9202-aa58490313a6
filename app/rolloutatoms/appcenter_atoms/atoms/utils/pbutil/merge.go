package pbutil

import (
	"code.byted.org/iesarch/cdaas_utils/erri"
	"fmt"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/anypb"
)

func Any2ProtoMsg(v *anypb.Any) (proto.Message, error) {
	if v == nil {
		return nil, nil
	}
	res, err := anypb.UnmarshalNew(v, proto.UnmarshalOptions{})
	if err != nil {
		return nil, err
	}
	return res, nil
}

// TODO add some test cases
func MergeAny(dstAny, srcAny *anypb.Any, appendList bool) (proto.Message, error) {
	dst, err := anypb.UnmarshalNew(dstAny, proto.UnmarshalOptions{})
	if err != nil {
		return nil, err
	}
	if srcAny == nil {
		return dst, nil
	}

	src, err := anypb.UnmarshalNew(srcAny, proto.UnmarshalOptions{})
	if err != nil {
		return nil, err
	}

	if err = Merge(dst, src, appendList); err != nil {
		return nil, err
	}
	return dst, nil
}

func Merge(dst, src proto.Message, appendList bool) error {
	if src == nil {
		return nil
	}
	dstMsg, srcMsg := dst.ProtoReflect(), src.ProtoReflect()
	if dstMsg.Descriptor() != srcMsg.Descriptor() {
		if got, want := dstMsg.Descriptor().FullName(), srcMsg.Descriptor().FullName(); got != want {
			return fmt.Errorf("descriptor mismatch: %v != %v", got, want)
		}
		return fmt.Errorf("descriptor mismatch")
	}

	if !appendList {
		clearRepeatedFieldsInDstFromSrc(src, dst)
	}
	proto.Merge(dst, src)
	return nil
}

func MergePbWithAny[T proto.Message](expected proto.Message, propertyOverrides *anypb.Any) (T, error) {
	overrideMsg, err := Any2ProtoMsg(propertyOverrides)
	if err != nil {
		return expected.(T), erri.Error(err)
	}
	err = Merge(expected, overrideMsg, false)
	if err != nil {
		return expected.(T), erri.Error(err)
	}
	return expected.(T), nil
}

func clearRepeatedFieldsInDstFromSrc(src, dst proto.Message) {
	srcReflect := src.ProtoReflect()
	dstReflect := dst.ProtoReflect()

	// Clear repeated fields in dst that also appear in src.
	clearRepeatedFields(srcReflect, dstReflect)
}

// clearRepeatedFields is a recursive helper function that clears repeated fields in dst.
func clearRepeatedFields(src, dst protoreflect.Message) {
	fields := src.Descriptor().Fields()
	for i := 0; i < fields.Len(); i++ {
		fd := fields.Get(i)
		// Clear the field if it's repeated (not map) in the dst message only if it shows up in src.
		if !fd.IsMap() && fd.Cardinality() == protoreflect.Repeated && src.Get(fd).List().Len() != 0 {
			if dst.Has(fd) {
				dst.Clear(fd)
			}
		}
		// Recursively clear nested message fields if they are message types but not repeated.
		if fd.Kind() == protoreflect.MessageKind && fd.Cardinality() != protoreflect.Repeated {
			subSrc, subDst := src.Get(fd).Message(), dst.Get(fd).Message()
			clearRepeatedFields(subSrc, subDst)
		}
	}
}
