package splitutil

import (
	"strings"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/nsutil"
	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/slices"
)

var vgeoMap = map[string]rollout.VGeo{
	"VGeo-RoW": rollout.VGeo_VGEO_ROW,
	"VGeo-US":  rollout.VGeo_VGEO_US,
	"VGeo-EU":  rollout.VGeo_VGEO_EU,
	"VGeo-CN":  rollout.VGeo_VGEO_CN,
	"VGeo-BOE": rollout.VGeo_VGEO_BOE,
}

var vregionMap = map[string]rollout.VRegion{
	env.VREGION_USTTP2:              rollout.VRegion_VREGION_US_TTP2,
	env.VREGION_USTTP:               rollout.VRegion_VREGION_US_TTP1,
	env.VREGION_SINGAPORECENTRAL:    rollout.VRegion_VREGION_SINGAPORE_CENTRAL,
	env.VREGION_USEASTRED:           rollout.VRegion_VREGION_US_EASTRED,
	env.VREGION_EUTTP:               rollout.VRegion_VREGION_EU_TTP,
	env.VREGION_USEAST:              rollout.VRegion_VREGION_US_EAST,
	env.VREGION_CHINANORTH:          rollout.VRegion_VREGION_CN_NORTH,
	env.VREGION_USBOE:               rollout.VRegion_VREGION_I18N_BOE,
	env.VREGION_CHINABOE:            rollout.VRegion_VREGION_CN_BOE,
	env.VREGION_SINGAPORECOMPLIANCE: rollout.VRegion_VREGION_SINGAPORE_COMPLIANCE,
	env.VREGION_IDCOMPLIANCE:        rollout.VRegion_VREGION_ID_COMPLIANCE,
	env.VREGION_IDCOMPLIANCE2:       rollout.VRegion_VREGION_ID_COMPLIANCE2,
	env.VREGION_USWEST:              rollout.VRegion_VREGION_US_WEST,
	env.VREGION_EUTTP2:              rollout.VRegion_VREGION_EU_TTP2,
}

func SplitDeploymentByResourceTypes(deployment *rolloutpb.Deployment, resourceTypes []rolloutpb.ResourceType, vGeos []rollout.VGeo, vRegions []rollout.VRegion) *rolloutpb.Deployment {
	resources := make([]*rolloutpb.Resource, 0)
	for _, resource := range deployment.GetResources() {
		if resource == nil {
			continue
		}
		if resource.GetMetadata().GetType() != rolloutpb.ResourceType_PAC_PIPELINE {
			if slices.Contains(resourceTypes, resource.GetMetadata().GetType()) || len(resourceTypes) == 0 {
				resource.Status = nil
				resourceAfterSplit := SplitResourceByPlacements(resource, vGeos, vRegions)
				if resourceAfterSplit != nil {
					resources = append(resources, resourceAfterSplit)
				}
			}
		}
	}
	deployment.Resources = resources
	return deployment
}

func SplitResourceByPlacements(resource *rolloutpb.Resource, vGeos []rollout.VGeo, vRegions []rollout.VRegion) *rolloutpb.Resource {
	if len(vRegions) == 0 && len(vGeos) == 0 {
		// no regional resource will be selected if vRegions and vGeos are both set nil
		return nil
	}
	for _, vRegion := range vRegions {
		vGeo := nsutil.VRegionToVGeo[vRegion]
		if !slices.Contains(vGeos, vGeo) {
			vGeos = append(vGeos, vGeo)
		}
	}

	regionalResources := make(map[string]*rolloutpb.RegionalResourceOverride)
	for key, regionalResource := range resource.GetSpec().GetRegionalResources() {
		items := strings.Split(key, "|")
		vgeoStr := items[0]
		vgeo := vgeoMap[vgeoStr]
		var vregionStr string
		if len(items) >= 2 {
			vregionStr = items[1]
		}
		vregion := vregionMap[vregionStr]
		if slices.Contains(vGeos, vgeo) {
			if slices.Contains(vRegions, vregion) || len(vRegions) == 0 || vregionStr == "" {
				regionalResources[key] = regionalResource
			}
		}
	}
	if regionalResources == nil || len(regionalResources) == 0 {
		return nil
	}
	resource.Spec.RegionalResources = regionalResources
	return resource
}
