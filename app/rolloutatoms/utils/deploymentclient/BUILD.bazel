load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "deploymentclient",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutatoms/utils/deploymentclient",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/rolloutapp/deployment:deployment_go_proto_xrpc_and_kitex_DeploymentService",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_kite_kitex//transport",
    ],
)
