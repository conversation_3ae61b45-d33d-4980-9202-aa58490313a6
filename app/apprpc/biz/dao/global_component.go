package dao

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/bytedoc/mongo-go-driver/bson"
	"code.byted.org/bytedoc/mongo-go-driver/bson/primitive"
	"code.byted.org/bytedoc/mongo-go-driver/mongo"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/bytedoc"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/idgen"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/iesarch/cdaas_utils/erri"
)

type GlobalComponentDao struct{}

func GetGlobalComponentDao() *GlobalComponentDao {
	return &GlobalComponentDao{}
}

func (d *GlobalComponentDao) Create(ctx context.Context, globalComponentDocument *GlobalComponentDocument) error {
	return createGlobalComponent(ctx, globalComponentDocument)
}

func createGlobalComponent(ctx context.Context, globalComponentDocument *GlobalComponentDocument) error {
	globalComponentDocument.CreateAt = time.Now().Unix()
	globalComponentDocument.UpdateAt = time.Now().Unix()
	if globalComponentDocument.GlobalComponentID == "" {
		globalComponentID, err := idgen.GetID(ctx)
		if err != nil {
			return erri.Error(err)
		}
		globalComponentDocument.GlobalComponentID = fmt.Sprintf("%d", globalComponentID)
	}
	if globalComponentDocument.Resources.TCEServices == nil {
		globalComponentDocument.Resources.TCEServices = make([]*TCEServiceResource, 0)
	}
	if globalComponentDocument.Resources.TCEClusters == nil {
		globalComponentDocument.Resources.TCEClusters = make([]*TCEClusterResource, 0)
	}
	if globalComponentDocument.ComponentType == "" {
		globalComponentDocument.ComponentType = app.GlobalComponentType_GLOBAL_COMPONENT_TYPE_UI.String()
	}
	_, err := bytedoc.InsertOne(ctx, GlobalComponentCollection, globalComponentDocument)
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) SearchByName(ctx context.Context, keyword string, mine bool, pageNo, pageSize int64,
	componentTypes []string) (int64, []*GlobalComponentDocument, error) {
	query := bson.M{
		"$or": bson.A{
			bson.M{
				"name": primitive.Regex{
					Pattern: keyword,
				},
			},
			bson.M{
				"alias": primitive.Regex{
					Pattern: keyword,
				},
			},
		},
	}
	if len(componentTypes) > 0 {
		query["componentType"] = bson.M{
			"$in": componentTypes,
		}
	}
	options := bytedoc.PaginateOptions{
		SortField: "create_at",
	}
	if mine {
		username := util.GetUsernameFromCtx(ctx)
		if username == "" {
			return 0, nil, erri.Errorf("username is empty")
		}
		query["creator"] = username
	}
	total, items, err := bytedoc.Paginate[GlobalComponentDocument](ctx, GlobalComponentCollection, query, pageNo, pageSize, &options)
	if err != nil {
		return total, nil, erri.Error(err)
	}

	return total, items, nil
}

func (d *GlobalComponentDao) SearchByNameOrCreator(ctx context.Context, keyword string, creator *string, pageNo, pageSize int64,
	componentTypes []string) (int64, []*GlobalComponentDocument, error) {
	query := bson.M{
		"$or": bson.A{
			bson.M{
				"name": primitive.Regex{
					Pattern: keyword,
				},
			},
			bson.M{
				"alias": primitive.Regex{
					Pattern: keyword,
				},
			},
		},
	}
	if len(componentTypes) > 0 {
		query["componentType"] = bson.M{
			"$in": componentTypes,
		}
	}
	if creator != nil {
		query["creator"] = creator
	}
	options := bytedoc.PaginateOptions{
		SortField: "create_at",
	}
	total, items, err := bytedoc.Paginate[GlobalComponentDocument](ctx, GlobalComponentCollection, query, pageNo, pageSize, &options)
	if err != nil {
		return total, nil, erri.Error(err)
	}

	return total, items, nil
}

func (d *GlobalComponentDao) GetByName(ctx context.Context, name string, componentTypes []string) (*GlobalComponentDocument, bool, error) {
	query := bson.M{
		"name": name,
	}
	if len(componentTypes) > 0 {
		query["componentType"] = bson.M{
			"$in": componentTypes,
		}
	}
	item, err := bytedoc.FindOne[GlobalComponentDocument](ctx, GlobalComponentCollection, query)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, false, nil
		}
		return nil, false, erri.Error(err)
	}
	return item, true, nil
}

func (d *GlobalComponentDao) GetByID(ctx context.Context, globalComponentID string) (*GlobalComponentDocument, bool, error) {
	item, err := bytedoc.FindOne[GlobalComponentDocument](ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	})
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, false, nil
		}
		return nil, false, erri.Error(err)
	}
	return item, true, nil
}

func (d *GlobalComponentDao) MustGetByID(ctx context.Context, globalComponentID string) (*GlobalComponentDocument, error) {
	item, err := bytedoc.FindOne[GlobalComponentDocument](ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	})
	if err != nil {
		return nil, erri.Error(err)
	}
	return item, nil
}

func (d *GlobalComponentDao) RemoveByID(ctx context.Context, globalComponentID string) (bool, error) {
	_, err := bytedoc.DeleteOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	})
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *GlobalComponentDao) AddTCEClusterResources(ctx context.Context, globalComponentID string, clusterResources []interface{}) error {
	change := bson.M{"$push": bson.M{"resources.tce_clusters": bson.M{"$each": clusterResources}}}
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
		"update_at":           time.Now().Unix(),
	}, change)
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) UpdateConstraint(ctx context.Context, globalComponentID string, constrained bool) error {
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	}, bson.M{"$set": bson.M{
		"constrained": constrained,
		"update_at":   time.Now().Unix(),
	},
	})
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) UpdateTime(ctx context.Context, globalComponentID string) error {
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	}, bson.M{"$set": bson.M{
		"update_at": time.Now().Unix(),
	},
	})
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) UpdateResources(ctx context.Context, globalComponentID string, status int64, doc ResourceDocument) error {
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	}, bson.M{"$set": bson.M{
		"resources": doc,
		"update_at": time.Now().Unix(),
		"status":    status,
	}})
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) UpdateApplicationInfo(ctx context.Context, globalComponentID string, appInfo *ApplicationDetail) error {
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	}, bson.M{"$set": bson.M{
		"application_detail": appInfo,
		"update_at":          time.Now().Unix(),
	}})
	if err != nil {
		return erri.Error(err)
	}
	return nil
}

func (d *GlobalComponentDao) UpdateName(ctx context.Context, globalComponentID, origin, name string) error {
	_, err := bytedoc.UpdateOne(ctx, GlobalComponentCollection, bson.M{
		"global_component_id": globalComponentID,
	}, bson.M{"$set": bson.M{
		"name":      name,
		"alias":     origin,
		"update_at": time.Now().Unix(),
	}})
	if err != nil {
		return erri.Error(err)
	}
	return nil
}
