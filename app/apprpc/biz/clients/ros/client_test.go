package ros

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/jwt"
)

func TestGetStackFullDetail(t *testing.T) {
	t.Skip()

	InitClient()
	detail, events, resources, err := GetStackFullDetail(context.Background(), "cn", "7ad88ce5-c19b-4a75-b510-47004e6f4792")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(detail)
	t.Log(events)
	t.Log(resources)
}

func TestCreateStack(t *testing.T) {
	t.Skip()

	body := `
{
    "TemplateFormatVersion": "1.0",
    "Description": "应用中心一键创建动态生成模板",
    "Parameters": {},
    "Resources": {
        "Codebase": {
            "Type": "ByteDance::Codebase::Repository",
            "Properties": {
                "Creator": "zhangshuisheng",
                "Framework": "faas",
                "FrameworkParam": {},
                "GalaxyNodeId": "4873875",
                "Name": "zhangshuisheng/bits_zss_testfaas_0326",
                "Platform": "gitlab",
                "VisibilityLevel": "normal"
            }
        },
        "FaaSClusterCN": {
            "Type": "ByteDance::FaaS::Cluster",
            "Partition": "cn",
            "DependsOn": [
                "FaaSCodeRevisionCN"
            ],
            "Properties": {
                "ClusterName": "default",
                "CodeRevisionNumber": {
                    "Fn::GetAtt": [
                        "FaaSCodeRevisionCN",
                        "Version"
                    ]
                },
                "Region": "cn-north",
                "ServiceEnv": "",
                "ServicePsm": "bits.zss.testfaas_0326"
            }
        },
        "FaaSCodeRevisionCN": {
            "Type": "ByteDance::FaaS::CodeRevision",
            "Partition": "cn",
            "DependsOn": [
                "FaaSServiceCN"
            ],
            "Properties": {
                "DeployMethod": "scm",
                "Description": "",
                "DisableBuildInstall": false,
                "LazyLoad": false,
                "Protocol": "",
                "RunCmd": "",
                "Runtime": "",
                "ServiceEnv": "",
                "ServicePsm": "bits.zss.testfaas_0326",
                "Source": {
                    "Fn::Join": [
                        ":",
                        [
                            "toutiao/zhangshuisheng/bits_zss_testfaas_0326",
                            {
                                "Fn::GetAtt": [
                                    "SCMVersion",
                                    "Number"
                                ]
                            }
                        ]
                    ]
                },
                "SourceType": "scm"
            }
        },
        "FaaSServiceCN": {
            "Type": "ByteDance::FaaS::Service",
            "Partition": "cn",
            "DependsOn": [
                "SCMVersion"
            ],
            "Properties": {
                "Category": "event",
                "Description": "",
                "EnvName": "",
                "Name": "bits.zss.testfaas_0326",
                "Owner": "zhangshuisheng",
                "Protocol": "",
                "Psm": "bits.zss.testfaas_0326",
                "PsmParentID": 4873875,
                "Runtime": "",
                "ServiceLevel": "P2",
                "ServicePurpose": "convention"
            }
        },
        "SCMRepo": {
            "Type": "ByteDance::SCM::Repo",
            "DependsOn": [
                "Codebase"
            ],
            "Properties": {
                "AutoBuild": false,
                "CompileScriptPath": "build.sh",
                "CompileType": 0,
                "ConfigurePermission": "developer",
                "CreateUser": "zhangshuisheng",
                "DeepClone": false,
                "DeployPermission": "developer",
                "Desc": "created from AppCenter",
                "DownloadPermission": "developer",
                "EnableCache": true,
                "GitSource": "gitlab",
                "GitURL": "https://code.byted.org/zhangshuisheng/bits_zss_testfaas_0326.git",
                "HasPreonline": false,
                "Image": "",
                "Language": "",
                "LintCheckType": "no",
                "Name": "toutiao/zhangshuisheng/bits_zss_testfaas_0326",
                "OnlineBranches": "",
                "ParallelConcurrent": 10,
                "ProductUploadType": "output_dir",
                "ProxySwitch": false,
                "SyncAws": false,
                "SyncBvc": false,
                "SyncOss": false
            }
        },
        "SCMVersion": {
            "Type": "ByteDance::SCM::Version",
            "DependsOn": [
                "SCMRepo"
            ],
            "Properties": {
                "BranchName": "master",
                "CreateUser": "zhangshuisheng",
                "Desc": "init version",
                "PubBase": "branch_base",
                "RepoId": {
                    "Ref": "SCMRepo"
                },
                "SyncAws": false,
                "SyncBvc": false,
                "SyncOss": false,
                "Type": "online"
            }
        }
    },
    "Outputs": {
        "CodebaseOutput": {
            "Description": "Codebase output",
            "Value": {
                "Ref": "Codebase"
            }
        },
        "FaaSClusterCNOutput": {
            "Description": "FaaSClusterCN output",
            "Value": {
                "Ref": "FaaSClusterCN"
            }
        },
        "FaaSCodeRevisionCNOutput": {
            "Description": "FaaSCodeRevisionCN output",
            "Value": {
                "Ref": "FaaSCodeRevisionCN"
            }
        },
        "FaaSServiceCNOutput": {
            "Description": "FaaSServiceCN output",
            "Value": {
                "Ref": "FaaSServiceCN"
            }
        },
        "SCMRepoOutput": {
            "Description": "SCMRepo output",
            "Value": {
                "Ref": "SCMRepo"
            }
        },
        "SCMVersionOutput": {
            "Description": "SCMVersion output",
            "Value": {
                "Ref": "SCMVersion"
            }
        }
    }
}
`
	userJwt := ""
	jwt.InitClient()
	InitClient()
	stackID, err := CreateStack(context.Background(), "cn", &CreateStackRequest{
		StackName:    "zss-test",
		TemplateBody: body,
	}, userJwt)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(stackID)
}
