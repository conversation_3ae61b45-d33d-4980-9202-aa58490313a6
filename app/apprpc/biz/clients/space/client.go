package space

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/request"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/gslice"
	json "github.com/bytedance/sonic"
)

type Detail struct {
	ID             string   `json:"id"`
	Name           string   `json:"name"`
	Type           int32    `json:"type"`
	BytetreeID     string   `json:"bytetreeId"`
	Avatar         string   `json:"avatar"`
	Description    string   `json:"description"`
	Identification string   `json:"identification"`
	HasPermission  bool     `json:"hasPermission"`
	Admins         []string `json:"admins"`
	Extra          string   `json:"extra"`
}

type Extra struct {
	TechnologyStack string `json:"technologyStack"`
	AppId           string `json:"appId"`
	DevScene        string `json:"devScene"`
}

const (
	SpaceType_QA     int32 = 0
	SpaceType_BEFE   int32 = 1
	SpaceType_Client int32 = 2
)

var baseURL string
var httpClient *httpclient.BytedHttpClient

const (
	SpaceRoleOwner          = "brn::iam:::role:bits/bytedance/bits.serviceSpaceOwner"          // 负责人
	SpaceRoleAdmin          = "brn::iam:::role:bits/bytedance/bits.serviceSpaceAdmin"          // 管理员
	SpaceRoleDeveloper      = "brn::iam:::role:bits/bytedance/bits.serviceSpaceDeveloper"      // 开发者
	SpaceRoleReleaseManager = "brn::iam:::role:bits/bytedance/bits.serviceSpaceReleaseManager" // 发布人员
)

func InitClient() {
	baseURL = "https://bits-boe.bytedance.net"
	if env.IsProduct() {
		baseURL = "https://bits.bytedance.net"
	}

	if httpClient == nil {
		httpClient = httpclient.MustNewBytedHttpClient()
	}
}

func GetSpaceDetail(ctx context.Context, spaceId int64, userJwt string) (*Detail, error) {
	if spaceId == 0 {
		return nil, fmt.Errorf("spaceId [%d] error", spaceId)
	}

	url := fmt.Sprintf("%s/api/v1/space/%d/detail", baseURL, spaceId)

	headers := map[string]string{
		"X-JWT-Token":     userJwt,
		"Accept-Language": util.GetLanguageFromCtx(ctx),
	}

	resp, err := httpClient.R().SetContext(ctx).SetHeaders(headers).Get(url)
	if err != nil {
		return nil, err
	}
	data := &Detail{}
	err = json.Unmarshal(resp.Body(), &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func BatchGetSpaceDetail(ctx context.Context, spaceIds []int64, userJwt string) (map[int64]*Detail, error) {
	spaceIds = gslice.Uniq(spaceIds)

	res := make(map[int64]*Detail, 0)
	wg := new(sync.WaitGroup)
	mutex := new(sync.Mutex)
	for _, spaceID := range spaceIds {
		wg.Add(1)
		go func(id int64) {
			defer utils.PanicGuard(ctx)
			defer wg.Done()

			resp, err := GetSpaceDetail(ctx, id, userJwt)
			if err == nil && resp != nil {
				mutex.Lock()
				res[id] = resp
				mutex.Unlock()
			}
		}(spaceID)
	}
	wg.Wait()

	return res, nil
}

type RoleMeta struct {
	SourceRoleBrn string `json:"sourceRoleBrn"`
}

type PermissionMeta struct {
	RoleMetas []RoleMeta `json:"roleMetas"`
}

type PermissionResponse struct {
	PermissionInfoGroupByPrincipals []*PermissionMeta `json:"permissionInfoGroupByPrincipals"`
}

func GetSpacePermission(ctx context.Context, spaceId int64, username string, jwtToken string) ([]*PermissionMeta, error) {
	brn := fmt.Sprintf("brn::iam:::user_account:%s", username)
	url := fmt.Sprintf("%s/api/v1/authz/principals/workspace_permissions?pageNum=1&pageSize=10&searchPrincipalBrns=%s", baseURL, brn)

	headers := map[string]string{
		"X-JWT-Token":     jwtToken,
		"Accept-Language": auth.Language(ctx),
		"X-Resource-Info": fmt.Sprintf("brn::bits:::space:%d", spaceId),
	}
	resp, err := request.Get[PermissionResponse](ctx, url, headers)
	if err != nil {
		return nil, err
	}

	return resp.PermissionInfoGroupByPrincipals, nil
}

func GetSpaceExtraInfo(ctx context.Context, extra string) *Extra {
	extraInfo := &Extra{}
	if extra == "" {
		return extraInfo
	}

	if err := json.Unmarshal([]byte(extra), &extraInfo); err != nil {
		logs.CtxError(ctx, "unmarshal space extra info error: %s", err.Error())
		return extraInfo
	}

	return extraInfo
}

func GetSpaceDefaultAvatar(spaceType int32) string {
	result := "http://tosv.byted.org/obj/bits-space-cn/%s"
	if spaceType == SpaceType_QA {
		return fmt.Sprintf(result, "qa.png")
	} else {
		return fmt.Sprintf(result, "be_fe.png")
	}
}

func HasBindSpacePermission(ctx context.Context, spaceId int64, username string, jwtToken string) bool {
	permissions, err := GetSpacePermission(ctx, spaceId, username, jwtToken)
	if err != nil {
		return false
	}

	if len(permissions) == 0 {
		// space not exist  or  no permission
		return false
	}

	for _, permission := range permissions {
		if len(permission.RoleMetas) == 0 {
			continue
		}

		for _, roleMeta := range permission.RoleMetas {
			if gslice.Contains([]string{SpaceRoleOwner, SpaceRoleAdmin, SpaceRoleDeveloper, SpaceRoleReleaseManager}, roleMeta.SourceRoleBrn) {
				return true
			}
		}
	}

	return false
}
