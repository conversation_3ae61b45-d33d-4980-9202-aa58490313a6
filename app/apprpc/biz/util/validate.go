package util

import (
	"regexp"
	"strings"
)

func CheckPSM(psm string) (bool, string) {
	reg := regexp.MustCompile("^[A-Za-z0-9_]+\\.[A-Za-z0-9_]+\\.[A-Za-z0-9_]+$")
	if !reg.MatchString(psm) {
		return false, "psm 格式不正确"
	}
	if len(psm) > 63 {
		return false, "psm 长度不能超过 63 位"
	}
	if strings.HasSuffix(psm, ".service") {
		return false, "参数 psm 不能以 .service 结尾"
	}
	if strings.HasSuffix(psm, ".test") {
		return false, "参数 psm 不能以 .test 结尾"
	}
	return true, ""
}

func CheckSCM(scm string) (bool, string) {
	reg := regexp.MustCompile("^[a-zA-Z0-9_]+(/[a-zA-Z0-9_]+){2,}$")
	if !reg.MatchString(scm) {
		return false, "SCM 仓库名必须以 / 分割成至少三段，且只包含字母数字和下划线，不允许中文，例如：aaa/bbb/ccc"
	}
	return true, ""
}

func CheckClusterName(name string) (bool, string) {
	pattern := `^[a-zA-Z0-9][a-zA-Z0-9_-]{0,22}[a-zA-Z0-9]$`

	// 编译正则表达式
	regex := regexp.MustCompile(pattern)

	// 匹配字符串
	if !regex.MatchString(name) {
		return false, "集群名称必须以字母、数字开头和结尾，且长度不超过 24，只能包含字母、数字、下划线和中划线。"
	}

	return true, ""
}

func CheckGitFullPath(path string) (bool, string) {
	pattern := `^[a-zA-Z0-9_.\-]+/[a-zA-Z0-9_.\-]+$`
	regex := regexp.MustCompile(pattern)
	if !regex.MatchString(path) {
		return false, "代码仓库名必须以 / 分割成两段，且只包含字母数字和下划线，不允许中文，例如：aaa/bbb"
	}

	arr := strings.Split(path, "/")
	if len(arr[1]) > 64 {
		return false, "代码仓库名长度不能超过 64 位"
	}

	return true, ""
}
