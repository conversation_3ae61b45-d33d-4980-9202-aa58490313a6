load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "reconsile",
    srcs = ["reconcile.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/apprpc/biz/service/global/reconsile",
    visibility = ["//visibility:public"],
    deps = [
        "//app/apprpc/biz/clients/cache",
        "//app/apprpc/biz/dao",
        "//app/apprpc/biz/internal/global/application/controller",
        "//app/apprpc/biz/pkg/auth",
        "//app/apprpc/biz/util/jwtutil",
        "//app/apprpc/biz/util/statusutil",
        "//idls/byted/devinfra/app:app_go_proto",
        "//libs/routinerecover",
        "@org_byted_code_gopkg_ctxvalues//:ctxvalues",
        "@org_byted_code_gopkg_logid//:logid",
        "@org_byted_code_gopkg_logs_v2//log",
    ],
)

go_test(
    name = "reconsile_test",
    srcs = ["reconcile_test.go"],
    embed = [":reconsile"],
    deps = ["//app/apprpc/biz/dao"],
)
