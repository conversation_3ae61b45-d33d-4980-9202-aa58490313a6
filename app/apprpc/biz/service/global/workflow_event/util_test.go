package workflow_event

import (
	"testing"
)

func Test_getCreateEventID(t *testing.T) {
	type args struct {
		globalComponentID string
		workflowID        string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "generate name",
			args: args{
				globalComponentID: "123",
				workflowID:        "123",
			},
			want: "AppCenter|CreateWorkflow|EventID|123:123",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCreateEventID(tt.args.globalComponentID, tt.args.workflowID); got != tt.want {
				t.Errorf("getCreateEventID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getRawEventID(t *testing.T) {
	type args struct {
		globalComponentID string
		workflowID        string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "generate raw ID",
			args: args{
				globalComponentID: "123",
				workflowID:        "123",
			},
			want: "123:123",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getRawEventID(tt.args.globalComponentID, tt.args.workflowID); got != tt.want {
				t.Errorf("getRawEventID() = %v, want %v", got, tt.want)
			}
		})
	}
}
