package tcesolution

import (
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/creation/solution/solutionconsts"
)

var FlaskConfig = dao.SolutionConfig{
	Key:           solutionconsts.Key_Flask,
	ComponentType: constants.ComponentType_TCE,
	Category:      dao.SolutionCategoryGeneral,
	Language:      solutionconsts.Language_Python,
	Name:          "Flask",
	NameI18N:      "Flask",
	Desc:          "基于 Flask 与 BytedUnicorn 创建 HTTP server",
	DescI18N:      "",
	Icon:          "",
	Link:          "https://bytedance.larkoffice.com/wiki/wikcnPoyFgYwVn4gTZWjvq9Satc",
	Status:        solutionconsts.Status_Online,
	Order:         5,
	Features:      []string{solutionconsts.Feature_PSM, solutionconsts.Feature_SCM, solutionconsts.Feature_Cluster},
	Resources:     []string{solutionconsts.Resource_PSM, solutionconsts.Resource_Codebase, solutionconsts.Resource_SCM, solutionconsts.Resource_TCE},
	CodeConfig: dao.CodeConfig{
		Type:      dao.CodeConfigTypeTemplate,
		Framework: solutionconsts.Codebase_Framework_Flask,
	},
	SCMConfig: dao.SCMConfig{
		Language:          solutionconsts.SCM_Language_Python,
		Image:             solutionconsts.SCM_Image_Python_39,
		CompileType:       solutionconsts.SCM_Compile_Type_Script,
		ProductUploadType: solutionconsts.SCM_UploadType_RootDir,
		BuildScript:       "build.sh",
	},
	TCEConfig: dao.TCEConfig{
		Language:  solutionconsts.SCM_Language_Python,
		Framework: solutionconsts.Framework_Flask,
		Protocol:  solutionconsts.Protocol_HTTP,
	},
}
