package creation

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"github.com/bytedance/mockey"
)

func TestProgressService_UpdateCreateComponentStatus(t *testing.T) {
	type args struct {
		ctx  context.Context
		list []*dao.CreateComponent
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				list: []*dao.CreateComponent{
					{
						ComponentType: constants.ComponentType_TCE,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer mockey.UnPatchAll()

			mockey.Mock(mockey.GetMethod(dao.GetCreateComponentDao(), "FindList")).Return(tt.args.list, nil).Build()

			s := &ProgressService{}
			s.UpdateCreateComponentStatus(tt.args.ctx)
		})
	}
}

func TestProgressService_UpdateByStackID(t *testing.T) {
	type args struct {
		ctx     context.Context
		stackID string
		region  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test",
			args: args{
				ctx:     context.Background(),
				stackID: "123",
				region:  "cn",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ProgressService{}
			s.UpdateByStackID(tt.args.ctx, tt.args.stackID, tt.args.region)
		})
	}
}
