// Code generated by MockGen. DO NOT EDIT.
// Source: project_attr.go

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockProjectAttrDao is a mock of ProjectAttrDao interface.
type MockProjectAttrDao struct {
	ctrl     *gomock.Controller
	recorder *MockProjectAttrDaoMockRecorder
}

// MockProjectAttrDaoMockRecorder is the mock recorder for MockProjectAttrDao.
type MockProjectAttrDaoMockRecorder struct {
	mock *MockProjectAttrDao
}

// NewMockProjectAttrDao creates a new mock instance.
func NewMockProjectAttrDao(ctrl *gomock.Controller) *MockProjectAttrDao {
	mock := &MockProjectAttrDao{ctrl: ctrl}
	mock.recorder = &MockProjectAttrDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectAttrDao) EXPECT() *MockProjectAttrDaoMockRecorder {
	return m.recorder
}

// BatchGetNodeIDByProjectIDs mocks base method.
func (m *MockProjectAttrDao) BatchGetNodeIDByProjectIDs(ctx context.Context, projectIDs []uint64) (map[uint64]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetNodeIDByProjectIDs", ctx, projectIDs)
	ret0, _ := ret[0].(map[uint64]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNodeIDByProjectIDs indicates an expected call of BatchGetNodeIDByProjectIDs.
func (mr *MockProjectAttrDaoMockRecorder) BatchGetNodeIDByProjectIDs(ctx, projectIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNodeIDByProjectIDs", reflect.TypeOf((*MockProjectAttrDao)(nil).BatchGetNodeIDByProjectIDs), ctx, projectIDs)
}
