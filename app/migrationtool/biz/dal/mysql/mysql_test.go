package mysql

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"code.byted.org/gopkg/logs"
)

func TestConfig_useIPPortConnection(t *testing.T) {
	type fields struct {
		DB       string
		PSM      string
		User     string
		Password string
		Host     string
		Port     int64
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "normal",
			fields: fields{
				DB:       "test",
				PSM:      "",
				User:     "user",
				Password: "1234",
				Host:     "127.0.0.1",
				Port:     1230,
			},
			want: true,
		},
		{
			name: "DB is empty string",
			fields: fields{
				DB:       "   ",
				PSM:      "",
				User:     "user",
				Password: "1234",
				Host:     "127.0.0.1",
				Port:     1230,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				DB:       tt.fields.DB,
				PSM:      tt.fields.PSM,
				User:     tt.fields.User,
				Password: tt.fields.Password,
				Host:     tt.fields.Host,
				Port:     tt.fields.Port,
			}
			c.trimSpace()
			if got := c.useIPPortConnection(); got != tt.want {
				t.Errorf("useIPPortConnection() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfig_usePSMConnection(t *testing.T) {
	type fields struct {
		DB       string
		PSM      string
		User     string
		Password string
		Host     string
		Port     int64
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "normal",
			fields: fields{
				DB:       "test",
				PSM:      "a.b.c",
				User:     "user",
				Password: "1234",
				Host:     "127.0.0.1",
				Port:     1230,
			},
			want: true,
		},
		{
			name: "DB is empty string",
			fields: fields{
				DB:       "   ",
				PSM:      "a.b.c",
				User:     "user",
				Password: "1234",
				Host:     "127.0.0.1",
				Port:     1230,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				DB:       tt.fields.DB,
				PSM:      tt.fields.PSM,
				User:     tt.fields.User,
				Password: tt.fields.Password,
				Host:     tt.fields.Host,
				Port:     tt.fields.Port,
			}
			c.trimSpace()
			if got := c.usePSMConnection(); got != tt.want {
				t.Errorf("usePSMConnection() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetMasterConn(t *testing.T) {
	var err error
	MasterDB, err = gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{Logger: logger.Default.LogMode(logger.Info)})
	if err != nil {
		logs.Errorf("failed to connect to db, err: %v", err)
		panic(err)
	}
	_, err = GetMasterConn(context.Background())
	assert.Equal(t, err, nil)
}
