load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "devjobs_lib",
    srcs = [
        "main.go",
        "model.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/devjobs",
    visibility = ["//visibility:private"],
    deps = [
        "//app/devjobs/biz",
        "//app/devjobs/bytegate",
        "//app/devjobs/db",
        "//app/devjobs/dutyv5",
        "//app/devjobs/lib",
        "//app/devjobs/rpc",
        "//app/devjobs/service/larkbot",
        "//app/devjobs/service/tcc",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/release_ticket/shared:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/team_flow_config:team_flow_config_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "//idls/byted/devinfra/translation:translation_go_proto",
        "//infra/config_service/kitex_gen/bytedance/bits/config_service",
        "//infra/config_service/pkg/dal/db",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_gorhill_cronexpr//:cronexpr",
        "@com_github_jinzhu_copier//:copier",
        "@org_byted_code_bytefaas_faas_go//bytefaas",
        "@org_byted_code_bytefaas_faas_go//events",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//gslice",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "devjobs",
    embed = [":devjobs_lib"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "devjobs_test",
    srcs = ["main_test.go"],
    embed = [":devjobs_lib"],
    deps = [
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_stretchr_testify//assert",
    ],
)
