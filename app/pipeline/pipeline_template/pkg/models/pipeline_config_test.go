/**
 * @Date: 2021/10/29
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJob_NeedFetchConfiguration(t *testing.T) {
	t.<PERSON><PERSON><PERSON>ow()

	job := &Job{ServiceName: "build"}
	assert.True(t, job.NeedFetchConfiguration())

	job = &Job{ServiceName: "other"}
	assert.False(t, job.NeedFetchConfiguration())
}

func TestJob_GetCloudBuildTemplateConfigId(t *testing.T) {
	t.Ski<PERSON>Now()

	settings := map[string]interface{}{"template_config_id": 10}
	job := &Job{Settings: settings}

	configId, has := job.GetCloudBuildTemplateConfigId()
	assert.True(t, has)
	assert.Equal(t, 10, configId)
}

func TestJob_GetCloudBuildAtomConfigId(t *testing.T) {
	job := &Job{Params: "{\"atom.config.id\":12, \"key\":\"value\"}"}

	atomConfId, has := job.GetCloudBuildAtomConfigId()
	assert.True(t, has)
	assert.Equal(t, 12, atomConfId)
}
