load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "restymiddleware",
    srcs = [
        "afterresponse.go",
        "beforerequest.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/pipeline/pipeline_template/pkg/backend/restymiddleware",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_go_resty_resty_v2//:resty",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
        "@org_byted_code_gopkg_logs_v2//log",
    ],
)
