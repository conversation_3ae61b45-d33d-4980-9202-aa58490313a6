// Code generated by COMMENTS_BUILD_TOOLS 2.0.50. DO NOT EDIT.
package repository

import (
	"context"
	"database/sql"
	"errors"

	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline_template/pkg/dal/mysql/entity"
	"code.byted.org/gopkg/logs"
	"gorm.io/gorm"
)

var GlobalErrPipelineTemplateEnvVariable = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewPipelineTemplateEnvVariable(handler *gorm.DB) PipelineTemplateEnvVariable {
	return &_PipelineTemplateEnvVariableStruct{
		handler: handler,
	}
}

type _PipelineTemplateEnvVariableStruct struct {
	handler *gorm.DB
}

func (interstruct *_PipelineTemplateEnvVariableStruct) BatchInsertEnvInfo(ctx context.Context, items []*entity.PipelineTemplateEnvVariable) (int64, error) {
	if len(items) == 0 {
		return 0, GlobalErrPipelineTemplateEnvVariable.EmptySliceErr
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Create(&items)
		if _sdb.Error != nil {
			logs.Errorf("PipelineTemplateEnvVariable.BatchInsertEnvInfo occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) UpsertEnvInfo(ctx context.Context, items *entity.PipelineTemplateEnvVariable) (int64, error) {
	if items == nil {
		return 0, GlobalErrPipelineTemplateEnvVariable.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Save(items)
		if _sdb.Error != nil {
			logs.Errorf("PipelineTemplateEnvVariable.UpsertEnvInfo occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) DeleteByID(ctx context.Context, id int64) error {
	_retErr := func() error {
		_sqlText := "UPDATE `pipeline_template_env_variable` SET `deleted` = true WHERE `id` = ?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Exec(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return _sdb.Error
		}
		return nil
	}()
	return _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) DeleteTemplateEnvInfo(ctx context.Context, ids []int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update pipeline_template_env_variable set deleted=1 where id in (?)"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Exec(_sqlText, ids)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) DeleteTemplateEnvInfoByConfigId(ctx context.Context, configId int64) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update pipeline_template_env_variable set deleted=1 where template_id = ?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Exec(_sqlText, configId)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) FindByTemplateID(ctx context.Context, templateId int64) ([]*entity.PipelineTemplateEnvVariable, error) {
	_result, _retErr := func() ([]*entity.PipelineTemplateEnvVariable, error) {
		_sqlText := "SELECT * FROM `pipeline_template_env_variable` WHERE `template_id` = ? and `deleted` = false"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, templateId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []*entity.PipelineTemplateEnvVariable
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(entity.PipelineTemplateEnvVariable)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_PipelineTemplateEnvVariableStruct) ListBriefTemplateEnvVariables(ctx context.Context, templateId int64) ([]entity.PipelineTemplateEnvVariable, error) {
	_result, _retErr := func() ([]entity.PipelineTemplateEnvVariable, error) {
		_sqlText := "select name,default_value,required from pipeline_template_env_variable where template_id=? and deleted=0 "
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, templateId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		var _rets []entity.PipelineTemplateEnvVariable
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret entity.PipelineTemplateEnvVariable
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
