package model

func (CloudBuildConfig) Key() string {
	return "cloud_build"
}

// CloudBuildConfig 获取云构建相关tcc配置
type CloudBuildConfig struct {
	// Specs 套餐列表，key 为套餐名
	Specs      map[string]SpecMap `yaml:"specs"`
	GraySpaces []uint64           `yaml:"gray_spaces"`
}

// CloudBuildCompileExtraField 要给 one-site compile解析成engine-dsl时使用的参数合集，获取tcc、db数据
type CloudBuildCompileExtraField struct {
	// tcc记录 spec、对应的requests cpu memory default_concurrency等
	CloudBuildConfigMap *CloudBuildConfig
}

type CompileExtraField struct {
	*GitCompileExtraField
	*CloudBuildCompileExtraField
}
