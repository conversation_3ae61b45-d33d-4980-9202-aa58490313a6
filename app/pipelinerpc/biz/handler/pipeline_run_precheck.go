package handler

import (
	"code.byted.org/devinfra/hagrid/internal/pipeline/perror"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"context"
	"strings"
	"sync"

	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/atom"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/pkg/leafboat"

	leafboatsdk "code.byted.org/pipeline/go-sdk"
)

func (p *PipelineRunHandler) PreCheckPipeline(ctx context.Context, req *servicepb.PreCheckPipelineRequest) (*servicepb.PreCheckPipelineResponse, error) {
	pipeline, err := infra.PipelineRepo().GetPipelineWithOrca(ctx, req.PipelineId, 0)
	if err != nil {
		return nil, errors.Errorf("failed to get pipeline %d, err: %s", req.PipelineId, err.Error())
	}
	pipelineRun, err := p.buildPrePipelineRun(ctx, req)
	if err != nil {
		return nil, errors.Errorf("failed to build pipeline run, err: %s", err.Error())
	}
	var (
		wg           sync.WaitGroup
		space        *rpcpb.SpaceDetail
		variable     []*varstorepb.VarAssignment
		preCheckJobs []*platformpb.PreCheckDetail
	)
	errChan := make(chan error, 10)

	// get space
	wg.Add(1)
	go func() {
		defer wg.Done()
		space, err = p.spaceService.GetPipelineSpace(ctx, pipeline.SpaceID)
		if err != nil {
			errChan <- errors.Errorf("failed to get space %d, err: %s", pipeline.SpaceID, err.Error())
		}
	}()

	// get variable
	wg.Add(1)
	go func() {
		defer wg.Done()
		variable, err = p.pipelineVarService.GetVariable(ctx, pipelineRun.VarAssignmentIds)
		if err != nil {
			errChan <- errors.Errorf("failed to get variable, err: %s", err.Error())
		}
	}()

	if len(req.JobId) == 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			resp, err := p.GetPreCheckJobs(ctx, &platformpb.GetPreCheckJobsRequest{
				PipelineId: req.PipelineId,
			})
			if err != nil {
				errChan <- errors.Errorf("failed to get pre check jobs, err: %s", err.Error())
				return
			}
			preCheckJobs = resp.Jobs
		}()
	}

	wg.Wait()
	close(errChan)

	for e := range errChan {
		logs.CtxError(ctx, "get pipeline run failed, error: %s", e)
		if e != nil {
			err = e
		}
	}
	if err != nil {
		return nil, err
	}

	enginePipeline, err := leafboat.CompileEnginePipeline(ctx, pipeline, req.Username, pipelineRun, space, nil, variable)
	if err != nil {
		return nil, errors.Errorf("failed to compile engine pipeline, err: %s", err.Error())
	}
	results, err := engine.Client.PreCheckPipelineOrJob(ctx, enginePipeline, req.StageId, req.JobId, pipeline.ControlPanel)
	if err != nil {
		return nil, errors.Errorf("failed to pre check pipeline, err: %s", err.Error())
	}

	jobMap := make(map[string]*dslpb.Job)
	for _, stage := range pipeline.Stages {
		for _, job := range stage.Jobs {
			jobMap[stage.Id+job.Id] = job
		}
	}
	checkDetails := gslice.Map(results, func(result leafboatsdk.PrecheckResult) *platformpb.PreCheckDetail {
		return &platformpb.PreCheckDetail{
			JobId:        result.JobUID,
			StageId:      result.StageUID,
			Name:         jobMap[result.StageUID+result.JobUID].Name,
			ErrorMessage: result.ErrorMessage,
		}
	})

	checkTotal := choose.If(len(req.JobId) == 0, len(preCheckJobs), 1)
	return &servicepb.PreCheckPipelineResponse{
		CheckPassedTotal:     int32(checkTotal - len(checkDetails)),
		SingleJobCheckResult: len(checkDetails) == 0,
		CheckFailedTotal:     int32(len(checkDetails)),
		CheckDetails:         checkDetails,
	}, nil
}

func (p *PipelineRunHandler) GetPreCheckJobs(ctx context.Context, req *platformpb.GetPreCheckJobsRequest) (*platformpb.GetPreCheckJobsResponse, error) {
	pipeline, err := infra.PipelineRepo().GetPipelineWithOrca(ctx, req.PipelineId, 0)
	if err != nil {
		return nil, errors.Errorf("failed to get pipeline %d, err: %s", req.PipelineId, err.Error())
	}

	jobs := gslice.FlatMap(pipeline.Stages, func(stage *dslpb.Stage) []*dslpb.Job {
		return stage.Jobs
	})
	jobAtomMap := make(map[string]string)
	for _, job := range jobs {
		// job_atoms/devops_shell@1.0.0
		jobName := strings.Split(job.Uses, "@")[0]
		parts := strings.Split(jobName, "/")
		if len(parts) == 2 {
			jobName = parts[1]
		} else {
			continue
		}
		jobAtomMap[job.Id] = jobName
	}

	resp, err := atom.GetClient().GetJobAtomServiceMetaByUniqueIds(ctx, &atommanagepb.GetJobAtomServiceMetaByUniqueIdsRequest{
		AtomRegion: pipeline.ControlPanel.ToAtomRegion(),
		UniqueIds:  gmap.Values(jobAtomMap),
	})
	if err != nil {
		logs.CtxError(ctx, "failed to get job atom service meta, err: %s", err.Error())
		return nil, bits_err.PIPELINE.ErrAtomGetJobAtomServiceMetaByUniqueIds.AddOrPass(ctx, perror.HandleAtomError(ctx, err))
	}

	atomsNeedCheck := gmap.Filter(resp.GetServiceAtomMetas(), func(key string, value *atommanagepb.ServiceJobAtomMeta) bool {
		return value.CheckBeforeExecute
	})
	var jobsNeedCheck []*platformpb.PreCheckDetail
	for _, job := range jobs {
		if _, exist := atomsNeedCheck[jobAtomMap[job.Id]]; exist {
			jobsNeedCheck = append(jobsNeedCheck, &platformpb.PreCheckDetail{
				JobId: job.Id,
				Name:  job.Name,
			})
		}
	}

	return &platformpb.GetPreCheckJobsResponse{
		Jobs: jobsNeedCheck,
	}, nil
}

func (p *PipelineRunHandler) buildPrePipelineRun(ctx context.Context, req *servicepb.PreCheckPipelineRequest) (*entity.PipelineRun, error) {
	runContext, err := p.pipelineRunService.GenerateRunContext(ctx, &servicepb.RunPipelineRequest{
		PipelineId:    req.PipelineId,
		TriggerType:   platformpb.TriggerType_TRIGGER_TYPE_MANUAL,
		RunParams:     req.RunParams,
		AssignmentIds: req.AssignmentIds,
		CustomVars:    req.CustomVars,
		Username:      req.Username,
	})
	if err != nil {
		return nil, errors.Errorf("failed to generate run context, err: %s", err.Error())
	}
	pipelineRun, err := p.pipelineRunService.BuildPreCheckPipelineRun(ctx, runContext)
	if err != nil {
		return nil, errors.Errorf("failed to build pre check pipeline run, err: %s", err.Error())
	}
	return pipelineRun, nil
}
