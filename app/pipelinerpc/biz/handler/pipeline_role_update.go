package handler

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/no_cancel_ctx"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pauthz"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
)

func (p *PipelineHandler) UpdatePipelineRole(ctx context.Context, req *servicepb.UpdatePipelineRoleRequest) error {
	pipeline, err := infra.PipelineRepo().GetPipeline(ctx, req.PipelineId)
	if err != nil {
		return err
	}
	grantBinding := pauthz.ToIamRoleBind(req.GetGrantBindings(), req.GetPipelineId())
	revokeBinding := pauthz.ToIamRoleBind(req.GetRevokeBindings(), req.GetPipelineId())

	err = p.permissionService.BatchMigratePrincipalsRole(ctx, pipeline.SpaceID, req.GetJwt(), grantBinding, revokeBinding)
	ctx2 := no_cancel_ctx.WithoutCancel(ctx)
	utils.SafeGoWithoutCopyCtx(ctx, func() {
		TeaEditPipelineMember(ctx2, req.GetGrantBindings(), req.GetRevokeBindings())
	})

	return err
}
