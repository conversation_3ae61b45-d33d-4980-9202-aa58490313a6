package pipeline_yaml

import (
	"context"

	json "github.com/bytedance/sonic"
	yamlJson "github.com/ghodss/yaml"
	"google.golang.org/protobuf/encoding/protojson"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/trigger"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_notification"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_var"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/translation"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pyaml"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/yamlfile/yamlv2"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/triggerpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metainfo"
)

type IPipelineYamlService interface {
	GetPipelineVersionYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion) (pipelineYaml *platformpb.PipelineYamlPreview, err error)
	GetPipelineTriggerYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion, ts []*dslpb.Trigger) (r string, err error)
	GetPipelineNotificationsYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion, ns []*dslpb.Notification) (r string, err error)
	GetVarYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion, varGroup *varstorepb.VarGroup, varPreference dslpb.VarPreference, varOption *dslpb.PipelineVarOption) (r string, err error)
}

type pipelineYamlService struct {
	translationService  translation.ITranslationService
	pipelineService     pipeline.IPipelineService
	pipelineVarService  pipeline_var.IPipelineVarService
	notificationService pipeline_notification.INotificationService
}

func NewPipelineYamlService() *pipelineYamlService {
	return &pipelineYamlService{
		translationService:  translation.GetTranslationService(),
		pipelineService:     pipeline.NewPipelineService(),
		pipelineVarService:  pipeline_var.GetPipelineVarService(),
		notificationService: pipeline_notification.NewPipelineNotificationService(),
	}
}

func (p *pipelineYamlService) GetPipelineVersionYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion) (pipelineYaml *platformpb.PipelineYamlPreview, err error) {
	pipelineYaml = &platformpb.PipelineYamlPreview{
		Stages: pipelineVersion.GetOrca().Stages,
	}

	basicInfoYaml, err := p.GetPipelineBasicYaml(ctx, pipelineVersion)
	if err != nil {
		return
	}
	basicInfoYamlBytes, err := json.Marshal(basicInfoYaml)
	if err != nil {
		return
	}
	basicInfoBytes, err := yamlJson.JSONToYAML(basicInfoYamlBytes)
	if err != nil {
		return
	}
	pipelineYaml.BasicInfoYaml = string(basicInfoBytes)

	orcaYaml, err := p.GetPipelineOrcaYaml(ctx, pipelineVersion)
	if err != nil {
		return
	}
	orcaYamlBytes, err := json.Marshal(orcaYaml)
	if err != nil {
		return
	}
	jobYamlBytes, err := yamlJson.JSONToYAML(orcaYamlBytes)
	if err != nil {
		return
	}
	pipelineYaml.OrcaYaml = string(jobYamlBytes)

	pipelineYaml.TriggerYaml, err = p.GetPipelineTriggerYaml(ctx, pipelineVersion, nil)
	if err != nil {
		return
	}

	pipelineYaml.NotificationYaml, err = p.GetPipelineNotificationsYaml(ctx, pipelineVersion, nil)
	if err != nil {
		return
	}

	pipelineYaml.VariableYaml, err = p.GetVarYaml(ctx, pipelineVersion, nil, 0, nil)
	if err != nil {
		return
	}

	return
}

func (p *pipelineYamlService) GetVarYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion,
	varGroup *varstorepb.VarGroup, varPreference dslpb.VarPreference, varOption *dslpb.PipelineVarOption) (r string, err error) {

	if pipelineVersion != nil {
		if pipelineVersion.VarGroupID != 0 {
			varGroup, err = p.pipelineVarService.GetVarGroupByGroupIDAndVersion(ctx, pipelineVersion.VarGroupID, pipelineVersion.VarGroupVersion)
			if err != nil || varGroup == nil {
				return r, err
			}
		}

		baseSettings := pipelineVersion.GetBasicSetting()
		if baseSettings != nil {
			varPreference = baseSettings.VarPreference.ToPB()
			varOption = baseSettings.VarOption
		}
	}

	varYaml := &pyaml.PipelineVarYaml{
		VarGroup:      p.GetVarGroupYamlFromVarGroupPB(ctx, varGroup),
		VarPreference: varPreference,
		VarOption:     &pyaml.VarOptionYaml{AllowCustomVarsInBuildContext: varOption.GetAllowCustomVarsInBuildContext()},
	}

	varYamlBytes, err := json.Marshal(varYaml)
	if err != nil {
		return
	}
	varBytes, err := yamlJson.JSONToYAML(varYamlBytes)
	if err != nil {
		return
	}
	r = string(varBytes)
	return
}

func (p *pipelineYamlService) GetPipelineBasicYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion) (basicYaml *pyaml.PipelineBasicYaml, err error) {
	basicYaml = &pyaml.PipelineBasicYaml{}
	if pipelineVersion == nil {
		return
	}
	basicYaml.Id = pipelineVersion.PipelineID
	basicSetting := pipelineVersion.GetBasicSetting()
	if basicSetting != nil {
		acceptLang, ok := metainfo.GetPersistentValue(ctx, constvar.ContextKey_AcceptLanguage)
		if !ok {
			acceptLang = "zh"
		}
		basicYaml.Name = utils.GetI18nPBFromAcceptLanguage(basicSetting.NameEN, basicSetting.Name, acceptLang)
		basicYaml.Desc = utils.GetI18nPBFromAcceptLanguage(basicSetting.DescriptionEN, basicSetting.Description, acceptLang)
		basicYaml.Tag = p.pipelineService.GetPipelineTagsPBWithTags(ctx, basicSetting.Tags)
		if err != nil {
			return
		}
		basicYaml.Concurrency = &dslpb.Concurrency{
			Max:         basicSetting.Concurrency,
			NewRunFirst: basicSetting.NewRunFirst,
		}
		basicYaml.Authorizations = basicSetting.Authorizations
	}
	return
}

func (p *pipelineYamlService) GetPipelineOrcaYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion) (orcaYaml *pyaml.PipelineOrcaYaml, err error) {
	orcaYaml = &pyaml.PipelineOrcaYaml{}
	if pipelineVersion == nil {
		return
	}
	orcaYaml.LockInfos = pipelineVersion.GetOrcaLockInfo()
	orcaYaml.Stages = pipelineVersion.GetOrca().Stages

	return
}

func (p *pipelineYamlService) GetVarGroupYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion) (varGroupYaml *pyaml.VarGroupYaml, err error) {
	varGroupYaml = &pyaml.VarGroupYaml{}
	if pipelineVersion == nil || pipelineVersion.VarGroupID == 0 {
		return
	}

	varGroup, err := p.pipelineVarService.GetVarGroupByGroupIDAndVersion(ctx, pipelineVersion.VarGroupID, pipelineVersion.VarGroupVersion)
	if err != nil || varGroup == nil {
		return
	}

	return p.GetVarGroupYamlFromVarGroupPB(ctx, varGroup), nil
}

func (p *pipelineYamlService) GetVarGroupYamlFromVarGroupPB(ctx context.Context, varGroup *varstorepb.VarGroup) (varGroupYaml *pyaml.VarGroupYaml) {
	varGroupYaml = &pyaml.VarGroupYaml{}
	if varGroup == nil || (varGroup.GroupId == 0 && varGroup.VarDefinitions == nil) {
		return
	}
	varGroupYaml = &pyaml.VarGroupYaml{
		// GroupId:        varGroup.GroupId,
		// Version:        varGroup.Version,
		// Description:    varGroup.Description,
		// Provider:       varGroup.Provider,
		// CustomScope:    varGroup.CustomScope,
		VarDefinitions: varGroup.VarDefinitions,
	}
	if varGroupYaml.Description != nil && varGroupYaml.Description.Value == "" {
		varGroupYaml.Description = nil
	}
	return
}

func (p *pipelineYamlService) GetPipelineTriggerYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion, ts []*dslpb.Trigger) (r string, err error) {
	// 直接传入ts 或者 从pipeline_version获取
	if pipelineVersion != nil && pipelineVersion.TriggerGroupID != 0 {
		triggerGroup, err := trigger.GetClient().GetTriggerGroup(ctx, &triggerpb.GetTriggerGroupRequest{
			GroupId: pipelineVersion.TriggerGroupID,
			Version: pipelineVersion.TriggerGroupVersion,
		})
		if err != nil || triggerGroup.GetTriggerGroup() == nil {
			return r, err
		}
		ts = triggerGroup.GetTriggerGroup().GetTriggers()
	}

	triggersYaml := &pyaml.TriggersYaml{}
	// triggersYaml.Triggers = make([]*pyaml.TriggerYaml, 0, len(triggerGroup.GetTriggerGroup().GetTriggers()))
	for _, trigger := range ts {
		var byteTrigger []byte
		switch trigger.GetTrigger().(type) {
		case *dslpb.Trigger_Mr:
			triggerMr := trigger.GetMr()
			triggerMr.Repository = "" // 废弃字段，被Repositories取代
			byteTrigger, err = protojson.MarshalOptions{
				UseProtoNames:   true,
				UseEnumNumbers:  false,
				EmitUnpopulated: true,
			}.Marshal(triggerMr)
		case *dslpb.Trigger_GitPush:
			pushMr := trigger.GetGitPush()
			pushMr.Repository = "" // 废弃字段，被Repositories取代
			byteTrigger, err = protojson.MarshalOptions{
				UseProtoNames:   true,
				UseEnumNumbers:  false,
				EmitUnpopulated: true,
			}.Marshal(pushMr)
		case *dslpb.Trigger_Cron:
			byteTrigger, err = protojson.MarshalOptions{
				UseProtoNames:   true,
				UseEnumNumbers:  false,
				EmitUnpopulated: true,
			}.Marshal(trigger.GetCron())
		}
		if err != nil {
			return r, err
		}
		yamlBytes, err := yamlJson.JSONToYAML(byteTrigger)
		if err != nil {
			return r, err
		}
		yamlStr := pyaml.ReplacePatternSyntax(string(yamlBytes))
		yamlStr = pyaml.ReplaceMrEventStr(yamlStr)
		yamlStr = pyaml.ReplacePushEventStr(yamlStr)

		triggersYaml.Triggers = append(triggersYaml.Triggers, &pyaml.TriggerYaml{
			Trigger:           yamlStr,
			VarGroup:          p.GetVarGroupYamlFromVarGroupPB(ctx, trigger.VarGroup),
			TemplateTriggerId: trigger.TemplateTriggerId,
			Disabled:          trigger.Disabled,
		})
	}

	triggersYamlBytes, err := json.Marshal(triggersYaml)
	if err != nil {
		return
	}
	triggersBytes, err := yamlJson.JSONToYAML(triggersYamlBytes)
	if err != nil {
		return
	}
	r = string(triggersBytes)
	return
}

func (p *pipelineYamlService) GetPipelineNotificationsYaml(ctx context.Context, pipelineVersion *entity.PipelineVersion, ns []*dslpb.Notification) (r string, err error) {
	// ns直接传入 或者 基于pipelineVersion获取
	if pipelineVersion != nil && pipelineVersion.NotificationGroupID != 0 {
		notificationGroup, err := p.notificationService.GetGroup(ctx, pipelineVersion.NotificationGroupID, pipelineVersion.NotificationGroupVersion)
		if err != nil || notificationGroup == nil || notificationGroup.GetNotifications() == nil || (notificationGroup.GetGroupId() == 0 && len(notificationGroup.GetNotifications()) == 0) {
			return r, err
		}
		ns = notificationGroup.GetNotifications()
	}

	notificationYaml, err := yamlv2.ParseNotificationsPBToYaml(ns)
	if err != nil {
		logs.CtxNotice(ctx, "failed to get notifications yaml, error: %s", err.Error())
		return
	}
	notificationsYamlBytes, err := json.Marshal(&pyaml.NotificationsYaml{
		Notifications: notificationYaml,
	})
	if err != nil {
		logs.CtxNotice(ctx, "failed to get notifications yaml, error: %s", err.Error())
		return
	}
	notificationsBytes, err := yamlJson.JSONToYAML(notificationsYamlBytes)
	if err != nil {
		logs.CtxNotice(ctx, "failed to get notifications yaml, error: %s", err.Error())
		return
	}
	r = string(notificationsBytes)
	return
}
