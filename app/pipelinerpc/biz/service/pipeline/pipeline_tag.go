package pipeline

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/service_utils"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
)

func (p *pipelineService) GetPipelineTagsPB(ctx context.Context, pipelineID uint64) (*dslpb.Tag, error) {
	pipelineTags, err := infra.PipelineRepo().GetPipelineTagsByPipelineIDs(ctx, []uint64{pipelineID})
	if err != nil {
		return nil, err
	}
	return p.GetPipelineTagsPBWithTags(ctx, pipelineTags), nil
}

func (p *pipelineService) GetPipelineTagsPBWithTags(ctx context.Context, pipelineTags []*entity.PipelineTag) *dslpb.Tag {
	return service_utils.GetPipelineTagsPBWithTags(ctx, pipelineTags)
}
