package pipeline_notification

import (
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/testfactory"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
)

func Test_notificationService_isSameSnapshot(t *testing.T) {
	type args struct {
		old *entity.NotificationSnapshot
		new *entity.NotificationSnapshot
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "nil",
			args: args{
				old: nil,
				new: nil,
			},
			want: true,
		},
		{
			name: "empty",
			args: args{
				old: &entity.NotificationSnapshot{},
				new: &entity.NotificationSnapshot{},
			},
			want: true,
		},
		{
			name: "same but unordered",
			args: args{
				old: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							NotificationID: 1,
							Name:           "a",
						},
						{
							NotificationID: 2,
							Name:           "b",
						},
					},
				},
				new: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							NotificationID: 2,
							Name:           "b",
						},
						{
							NotificationID: 1,
							Name:           "a",
						},
					},
				},
			},
			want: true,
		},
		{
			name: "not same",
			args: args{
				old: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							ID: 1,
						},
					},
				},
				new: &entity.NotificationSnapshot{
					WebhookNotifications: []*entity.WebhookNotification{
						{
							ID: 1,
						},
					},
				},
			},
			want: false,
		},
		{
			name: "ignore time field",
			args: args{
				old: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							ID:        1,
							UpdatedAt: time.Now(),
						},
					},
				},
				new: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							ID:        1,
							UpdatedAt: time.Now().Add(time.Hour),
						},
					},
				},
			},
			want: true,
		},
		{
			name: "not same",
			args: args{
				old: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							ID: 1,
						},
						{
							ID: 2,
						},
					},
				},
				new: &entity.NotificationSnapshot{
					LarkNotifications: []*entity.LarkNotification{
						{
							ID: 1,
						},
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.args.old.Equals(tt.args.new), "isSameSnapshot(%v, %v)", tt.args.old, tt.args.new)
		})
	}
}

func Test_saveGroupBySnapShot(t *testing.T) {
	ctx := context.Background()
	operator := "test_operator"
	svc := &notificationService{}
	testfactory.InitMemMysql()
	mysql.DB.Create(&entity.NotificationGroup{
		GroupID:  4,
		Version:  1,
		Snapshot: []byte("{}"),
	})
	t.Run("Test empty notification group", func(t *testing.T) {
		groupID := uint64(0)
		snapshot := &entity.NotificationSnapshot{}
		res, err := svc.saveGroupBySnapShot(ctx, groupID, snapshot, operator)
		assert.Error(t, err)
		assert.Nil(t, res)
	})
	t.Run("Test save group duplicate key", func(t *testing.T) {
		groupID := uint64(4)
		snapshot := &entity.NotificationSnapshot{
			WebhookNotifications: []*entity.WebhookNotification{
				{
					ID: 1,
				},
				{
					ID: 2,
				},
			},
		}
		go func() {
			res, err := svc.saveGroupBySnapShot(context.Background(), groupID, snapshot, operator)
			assert.Error(t, err)
			assert.Nil(t, res)
		}()
		res, err := svc.saveGroupBySnapShot(context.Background(), groupID, snapshot, operator)
		assert.Error(t, err)
		assert.Nil(t, res)

	})
}
