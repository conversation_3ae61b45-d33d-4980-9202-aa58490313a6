package testfactory

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/mysql/entity"
)

func GuiPipelineRun(ctx context.Context) *entity.PipelineRun {
	r := &entity.PipelineRun{
		Id:               1,
		RunId:            410429697,
		RunSeq:           1,
		PipelineId:       410429697,
		PipelineVersion:  1,
		TriggerType:      1,
		RunParams:        nil,
		VarAssignmentIds: nil,
		Status:           0,
		EngineRunId:      0,
		Note:             "",
		IdempotentToken:  "",
		CreatedBy:        "",
	}
	return r
}
