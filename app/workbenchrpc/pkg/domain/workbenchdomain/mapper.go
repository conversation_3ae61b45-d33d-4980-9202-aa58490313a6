package workbenchdomain

import (
	"context"
	"strings"

	dal_tcc "code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/tenancies"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infrapb"
	spacepb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/workbenchpb"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bytedance_bits_meta/kitex_gen/bytedance/bits/meta"
)

func TransformToQuickEntryGroups(inputs []*dal_tcc.Group) []*workbenchpb.QuickEntryGroup {
	return gslice.Map(inputs, TransformToQuickEntryGroup)
}

func TransformToQuickEntryGroup(input *dal_tcc.Group) *workbenchpb.QuickEntryGroup {
	return &workbenchpb.QuickEntryGroup{
		Name:    input.Name,
		NameEn:  input.NameEn,
		Entries: TransformToQuickEntries(input.Entries),
	}
}

func TransformToQuickEntries(inputs []*dal_tcc.Entry) []*workbenchpb.QuickEntry {
	return gslice.Map(inputs, TransformToQuickEntry)
}

func TransformToQuickEntry(input *dal_tcc.Entry) *workbenchpb.QuickEntry {
	return &workbenchpb.QuickEntry{
		Name:          input.Name,
		NameEn:        input.NameEn,
		Icon:          input.Icon,
		Link:          input.Link,
		HasSpace:      input.HasSpace,
		Description:   input.Description,
		DescriptionEn: input.DescriptionEn,
		SubGroups:     TransformToQuickEntryGroups(input.SubGroups),
		SpaceType:     TransformToSpaceType(input.SpaceType),
	}
}

type QuickEntryGroupV2Mapper struct {
	groups  []*dal_tcc.GroupV2
	recents []string
	locale  string
}

func NewQuickEntryGroupV2Mapper(groups []*dal_tcc.GroupV2, recents []string, locale string) *QuickEntryGroupV2Mapper {
	return &QuickEntryGroupV2Mapper{groups: groups, recents: recents, locale: locale}
}

func (mapper *QuickEntryGroupV2Mapper) IntoProtobuf(ctx context.Context) []*workbenchpb.QuickEntryGroupV2 {
	return gslice.Map(mapper.groups, func(input *dal_tcc.GroupV2) *workbenchpb.QuickEntryGroupV2 {
		return mapper.TransformToQuickEntryGroupV2(ctx, input)
	})
}

func (mapper *QuickEntryGroupV2Mapper) TransformToQuickEntryGroupV2(ctx context.Context, input *dal_tcc.GroupV2) *workbenchpb.QuickEntryGroupV2 {
	return &workbenchpb.QuickEntryGroupV2{
		Name:    input.Name,
		NameEn:  input.NameEn,
		Entries: mapper.TransformToQuickEntryV2s(ctx, input.Entries),
	}
}

func (mapper *QuickEntryGroupV2Mapper) TransformToQuickEntryV2s(ctx context.Context, inputs []*dal_tcc.EntryV2) []*workbenchpb.QuickEntryV2 {
	return gslice.Map(inputs, func(input *dal_tcc.EntryV2) *workbenchpb.QuickEntryV2 {
		return mapper.TransformToQuickEntryV2(ctx, input)
	})
}

func (mapper *QuickEntryGroupV2Mapper) TransformToQuickEntryV2(ctx context.Context, input *dal_tcc.EntryV2) *workbenchpb.QuickEntryV2 {
	dropdowns := mapper.TransformToDropdowns(ctx, input.Dropdowns)

	switch tenancy := mtctx.GetTenancy(ctx); tenancy {
	case tenancies.TenancyDcarlife:
		input.Link = strings.Replace(input.Link, "meego.feishu.cn", "project.feishu.cn", 1)
	default:
	}

	return &workbenchpb.QuickEntryV2{
		Name:          input.Name,
		NameEn:        input.NameEn,
		Description:   input.Description,
		DescriptionEn: input.DescriptionEn,
		Icon:          input.Icon,
		Link:          input.Link,
		HasSpace:      input.HasSpace,
		SpaceType:     TransformToSpaceType(input.SpaceType),
		Recent:        gslice.ContainsAny(mapper.recents, input.Name) || gslice.Any(dropdowns, func(v *workbenchpb.QuickEntryV2) bool { return v.Recent }),
		Dropdowns:     dropdowns,
	}
}

func (mapper *QuickEntryGroupV2Mapper) TransformToDropdowns(ctx context.Context, inputs []*dal_tcc.EntryV2) []*workbenchpb.QuickEntryV2 {
	return gslice.Map(inputs, func(input *dal_tcc.EntryV2) *workbenchpb.QuickEntryV2 {
		return mapper.TransformToDropdown(ctx, input)
	})
}

func (mapper *QuickEntryGroupV2Mapper) TransformToDropdown(ctx context.Context, input *dal_tcc.EntryV2) *workbenchpb.QuickEntryV2 {
	switch tenancy := mtctx.GetTenancy(ctx); tenancy {
	case tenancies.TenancyDcarlife:
		input.Link = strings.Replace(input.Link, "meego.feishu.cn", "project.feishu.cn", 1)
	default:
	}

	return &workbenchpb.QuickEntryV2{
		Name:          input.Name,
		NameEn:        input.NameEn,
		Description:   input.Description,
		DescriptionEn: input.DescriptionEn,
		Icon:          input.Icon,
		Link:          input.Link,
		HasSpace:      input.HasSpace,
		SpaceType:     TransformToSpaceType(input.SpaceType),
		Recent:        gslice.ContainsAny(mapper.recents, input.Name),
		Dropdowns:     nil,
	}
}

func TransformToSimpleSpace(input *spacepb.SpaceDetail, stack meta.TechnologyStack) *workbenchpb.SimpleSpace {
	if input == nil {
		return nil
	}

	return &workbenchpb.SimpleSpace{
		Id: input.Id,
		Name: &infrapb.I18NString{
			Value: input.Name,
			Lang:  "zh",
		},
		Logo:  input.Avatar,
		Type:  TransformToSpaceType(input.Type),
		Stack: TransformToSpaceTechnologyStack(stack),
	}
}

func TransformToSpaceType(input string) workbenchpb.SpaceType {
	switch input {
	case "general":
		return workbenchpb.SpaceType_SPACE_TYPE_GENERAL
	case "backend_frontend":
		return workbenchpb.SpaceType_SPACE_TYPE_BACKEND_FRONTEND
	case "client":
		return workbenchpb.SpaceType_SPACE_TYPE_CLIENT
	default:
		return workbenchpb.SpaceType_SPACE_TYPE_UNSPECIFIED
	}
}

func TransformToSpaceTechnologyStack(input meta.TechnologyStack) workbenchpb.SpaceTechnologyStack {
	switch input {
	case meta.TechnologyStack_Android:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_ANDROID
	case meta.TechnologyStack_iOS:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_IOS
	case meta.TechnologyStack_Web:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_WEB
	case meta.TechnologyStack_Flutter:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_HYBRID_FLUTTER
	case meta.TechnologyStack_FlutterApp:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_FLUTTER_APP
	case meta.TechnologyStack_Mixed:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_MIXED
	case meta.TechnologyStack_MacOS:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_MACOS
	case meta.TechnologyStack_Windows:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_WINDOWS

	default:
		return workbenchpb.SpaceTechnologyStack_SPACE_TECHNOLOGY_STACK_UNSPECIFIED
	}
}

func TransformToGetQuickEntryActionPromptResponseBlockCategory(input string) workbenchpb.GetQuickEntryActionPromptResponse_BlockCategory {
	switch input {
	case "alert":
		return workbenchpb.GetQuickEntryActionPromptResponse_BLOCK_CATEGORY_ALTER
	case "force":
		return workbenchpb.GetQuickEntryActionPromptResponse_BLOCK_CATEGORY_FORCE
	default:
		return workbenchpb.GetQuickEntryActionPromptResponse_BLOCK_CATEGORY_FORCE
	}
}
