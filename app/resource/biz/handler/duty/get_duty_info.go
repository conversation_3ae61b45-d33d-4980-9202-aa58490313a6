package duty

import (
	"code.byted.org/devinfra/hagrid/app/resource/pkg/protocol"
	"code.byted.org/devinfra/hagrid/app/resource/pkg/runtime"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/resource/duty"
	"code.byted.org/devinfra/hagrid/pkg/resource/endpoint"
	"context"
	"fmt"
	"github.com/cloudwego/hertz/pkg/app"
	"net/http"
)

func GetDutyInfo(ctx context.Context, c *app.RequestContext, r *protocol.GenericRequest[duty.GetDutyInfoReq]) (*duty.GetDutyInfoResp, error) {
	req := r.Payload
	domain, ok := domainMap[req.TargetDomain]
	if !ok {
		return nil, bits_err.RESOURCE.ErrInvalidDomain.AddError(fmt.Errorf("can't get domain in %s", runtime.GetCurrentRegion()))
	}
	httpCli, err := endpoint.GetHttpCli()
	if err != nil {
		return nil, bits_err.RESOURCE.ErrHttpClientCreation.AddError(err)
	}
	httpReq := httpCli.R()
	httpReq.SetContext(ctx)
	httpReq.Method = http.MethodGet
	httpReq.SetUrl(fmt.Sprintf("%s/duty/oapi/v3/schedule/%s", domain, req.Name))
	httpReq.SetHeaders(r.ResourceHeaders)

	resp := &duty.GetDutyInfoResp{}
	httpReq.SetResult(resp)
	_, err = httpCli.Execute(ctx, httpReq)
	if err != nil {
		return nil, bits_err.RESOURCE.ErrHttpRequestExecution.AddError(err)
	}
	if resp.Code != 0 {
		return nil, bits_err.RESOURCE.ErrRemoteServiceCall.AddError(fmt.Errorf("call duty code: %d error: %s", resp.Code, resp.Msg))
	}
	return &duty.GetDutyInfoResp{
		DutyResponse: resp.DutyResponse,
	}, nil
}
