package tce

import (
	tce_provider "code.byted.org/canal/provider/tce"
	"code.byted.org/devinfra/hagrid/app/resource/pkg/protocol"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/resource/tce"
	"context"
	"github.com/cloudwego/hertz/pkg/app"
)

func GetServiceInfoByPSMOfIDCList(ctx context.Context, c *app.RequestContext,
	req *protocol.GenericRequest[tce.GetServiceInfoByPSMOfIDCListReq]) (*tce.GetServiceInfoByPSMOfIDCListResp, error) {
	resp, err := tce_provider.GetServiceInfoByPSMOfIDCList(ctx, req.Payload.Psm, req.Payload.AppEnv,
		req.Payload.IdcList, req.Payload.TceEnv, req.Payload.Username)
	if err != nil {
		return nil, bits_err.RESOURCE.ErrTCEGetServiceInfo.AddError(err)
	}
	return &tce.GetServiceInfoByPSMOfIDCListResp{
		ServiceInfos: resp,
	}, nil
}
