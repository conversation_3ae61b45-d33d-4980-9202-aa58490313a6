package merge_request

import "context"

const MERGE_LOCK_ATOMIC = "merge_lock_atomic"
const MERGE_LOCK_SUPERIOR = "merge_lock_superior"
const MERGE_LOCK_EXTERN = "merge_lock_extern"

// 外部锁
const MERGE_LOCK_EXTERN_NAME_RC = "merge_lock_extern_rc"
const MERGE_LOCK_EXTERN_NAME_OTHER = "merge_lock_extern_other"

type lockInfoByProjectIDAndTargetBranch struct{}

func GetLockNamsByProjectIDAndTargetBranch(ctx context.Context, projectID int64, iID int64) []string {
	//projects = []
	//if project_id:
	//projects.append(str(project_id))
	//else:
	//projects.extend(list(RedisCache.smembers(__redis_lock_key_name(type))))
	//
	//names = []
	//
	//def add_name(name):
	//if RedisCache.exists(name) or not exist:
	//names.append(name)
	//
	//for project in projects:
	//if target_branch:
	//add_name(KEY_SPLIT.join([type, project, target_branch]))
	//else:
	//branches = list(RedisCache.smembers(__redis_lock_key_name(type, project)))
	//for branch in branches:
	//add_name(KEY_SPLIT.join([type, project, branch]))
	//
	//return names
	return nil
}
func GetLockInfoByProjectIDAndTargetBranch(ctx context.Context, types string, projectID int64, targetBranch string) []*lockInfoByProjectIDAndTargetBranch {
	//lockList := make([]*lockInfoByProjectIDAndTargetBranch, 0)
	//
	//if len(types) >0 {
	//  names = names_for_lock(type, project_id, target_branch)
	//}
	//
	//
	//for __key in names:
	//___value = RedisCache.smembers(__key)
	//lock_list.extend(list(___value))
	//
	//#
	//if type:
	//__get_lock_for_type(type)
	//else:
	//for type in [MERGE_LOCK_EXTERN, MERGE_LOCK_SUPERIOR, MERGE_LOCK_ATOMIC]:
	//__get_lock_for_type(type)
	//  type=None if not ignore_lock else MERGE_LOCK_ATOMIC,
	//    project_id=project_id,
	//    target_branch=target_branch)
	//  if not lock_list:
	//  return []
	//
	//  return __get_lock_for_merge_request(iid, lock_list)
	return nil
}

type mergeRequestLockInfo struct {
}

func GetLockInfoByProjectIDAndIIDAndTargetBranch(ctx context.Context, projectID int64, iID int64, targetBranch string) error {
	//  super_mr_info = []
	//  if mr.state == MergeRequestModel.STATE_OPEN:
	//  super_mr_info = MergeLock.get_lock_for_merge_request(mr.project_id, mr.iid, mr.target_branch)

	//  lock_list = get_lock_for_branch(

	//  for mr_info in super_mr_info:
	//  mr_info["lock_type"] = mr_info.get("type", "")
	//  if mr_info["lock_type"] == MergeLock.MERGE_LOCK_EXTERN:
	//  mr_info["project_name"] = ConfigCache.name_for_project(mr_info.get("project_id", ""))
	//  else:
	//  mr_data = MergeRequestModel.get_item_with_iid(mr_info.get("project_id", ""), mr_info.get("iid", 0), using_read=True)
	//  if mr_data:
	//  mr_info["mr_title"] = mr_data.title
	//  mr_info["submitter_name"] = mr_data.author_name
	//  mr_info["mr_id"] = mr_data.id
	//  return super_mr_info
	return nil
}
