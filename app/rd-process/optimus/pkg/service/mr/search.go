package mr

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	elastic "code.byted.org/bytees/olivere_elastic/v7"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/code_review_model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/elasticsearch"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/tcc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/facility/math"
	"code.byted.org/gopkg/lang/sets"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bytedance_bits_qa_review/kitex_gen/bytedance/bits/qa_review"
	"code.byted.org/overpass/bytedance_bits_qa_review/rpc/bytedance_bits_qa_review"
	json "github.com/bytedance/sonic"
)

type Search struct {
	error    consts.ErrorMsg
	Searched bool
	adaptor
}

type prefixFun func(ctx context.Context, text string, projectIDs []string, username string, lastID int64) gresult.R[[]int64]

type prefixFunMap struct {
	Prefix string
	Fun    prefixFun
}

var CommonSearchWhiteList []prefixFunMap = []prefixFunMap{{
	Prefix: "commit:",
	Fun:    queryCommitIDOrMessage,
}, {
	Prefix: "commit：",
	Fun:    queryCommitIDOrMessage,
}, {
	Prefix: "pod:",
	Fun:    queryPod,
}, {
	Prefix: "pod：",
	Fun:    queryPod,
}, {
	Prefix: "path:",
	Fun:    queryFilePath,
}, {
	Prefix: "path：",
	Fun:    queryFilePath,
}}

func queryPod(ctx context.Context, text string, projectIDs []string, username string, lastID int64) gresult.R[[]int64] {
	var name, version string
	if arr := strings.Split(text, ":"); len(arr) == 2 {
		name = arr[0]
		version = arr[1]
	} else {
		return gresult.Err[[]int64](bits_err.OPTIMUS.ErrPodSearchFormatInvalid)
	}
	// 兼容两种写法
	baseMatchQueries := elastic.NewBoolQuery()

	if len(text) != 0 {
		condition := make([]elastic.Query, 0)
		condition = append(condition, elastic.NewTermQuery("pod_name.keyword", name))
		condition = append(condition, elastic.NewTermQuery("pod_version.keyword", version))
		baseMatchQueries.Must(condition...)
	}
	fetchContent := elastic.NewFetchSourceContext(true).Include("bits_mr_id")

	client := elasticsearch.MRPodSearchClient.GetSearchConnection().
		FetchSourceContext(fetchContent).
		Size(1000).
		Query(baseMatchQueries)

	res, err := client.Do(ctx)

	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrEsSearchFail.Error(), err.Error())
		return gresult.Err[[]int64](bits_err.OPTIMUS.ErrEsSearchFail)
	}

	bitsMRIDs := make([]int64, 0)
	for _, item := range res.Hits.Hits {
		v := &MRGeneralSearchModel{}
		str, _ := item.Source.MarshalJSON()
		_ = json.Unmarshal(str, v)
		if v.BitsMRID > 0 {
			bitsMRIDs = append(bitsMRIDs, v.BitsMRID)
		}
	}
	logs.CtxInfo(ctx, "pod search result: %+v", bitsMRIDs)
	return gresult.OK(bitsMRIDs)
}

func queryFilePath(ctx context.Context, text string, projectIDs []string, username string, lastID int64) gresult.R[[]int64] {
	// 兼容两种写法
	baseMatchQueries := elastic.NewBoolQuery()
	text = strings.ReplaceAll(text, "/", "_")
	text = strings.ReplaceAll(text, "-", "_")
	baseMatchQueries.Must(elastic.NewWildcardQuery("path.keyword", fmt.Sprintf("%s*", text)))

	filterQuery := make([]elastic.Query, 0)
	q := make([]interface{}, 0)
	gslice.ForEach[string](projectIDs, func(pId string) {
		q = append(q, pId)
	})
	filterQuery = append(filterQuery, elastic.NewTermsQuery("project_id", q...))

	if len(strings.Trim(username, " ")) != 0 {
		filterQuery = append(filterQuery, elastic.NewTermQuery("author", username))
	}

	if lastID != 0 {
		filterQuery = append(filterQuery, elastic.NewRangeQuery("bits_mr_id").Lt(lastID))
	}
	baseMatchQueries.Filter(filterQuery...)

	client := elasticsearch.MRFileSearchClient.GetSearchConnection().
		Aggregation("bits_mr_id", elastic.NewTermsAggregation().
			Size(1000).
			Field("bits_mr_id")).
		Query(baseMatchQueries).
		Sort("bits_mr_id", false).
		Size(0)

	res, err := client.Do(ctx)

	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrEsSearchFail.Error(), err.Error())
		return gresult.Err[[]int64](bits_err.OPTIMUS.ErrEsSearchFail)
	}

	bitsMRIDs := make([]int64, 0)
	bucket, _ := res.Aggregations.Terms("bits_mr_id")

	for _, item := range bucket.Buckets {
		v := &MRGeneralSearchModel{}
		mrIdStr := fmt.Sprintf("%v", item.KeyNumber)
		v.BitsMRID, _ = strconv.ParseInt(mrIdStr, 10, 64)
		if v.BitsMRID > 0 {
			bitsMRIDs = append(bitsMRIDs, v.BitsMRID)
		}
	}
	gslice.Sort(bitsMRIDs)
	gslice.Reverse(bitsMRIDs)
	logs.CtxInfo(ctx, "file_path_search_result: %v", bitsMRIDs)
	return gresult.OK(bitsMRIDs)
}

// QueryCommitIDOrMessage
// @Title Search by commit id and message
// @Description Filter the corresponding bits_mr based on the commit information submitted by the user
func queryCommitIDOrMessage(ctx context.Context, text string, projectIDs []string, username string, lastID int64) gresult.R[[]int64] {
	// 兼容两种写法
	baseMatchQueries := elastic.NewBoolQuery()
	filterQuery := make([]elastic.Query, 0)

	q := make([]interface{}, 0)
	gslice.ForEach[string](projectIDs, func(pId string) {
		q = append(q, pId)
	})
	filterQuery = append(filterQuery, elastic.NewTermsQuery("project_id", q...))
	baseMatchQueries.Filter(filterQuery...)

	if len(text) != 0 {
		condition := make([]elastic.Query, 0)
		condition = append(condition, elastic.NewTermQuery("commit_id.keyword", text))
		condition = append(condition, elastic.NewMatchPhraseQuery("commit_message", text).Slop(1).Boost(10.0))
		baseMatchQueries.Should(condition...).MinimumNumberShouldMatch(1)
	}
	fetchContent := elastic.NewFetchSourceContext(true).Include("bits_mr_id", "project_id", "commit_id", "commit_message")

	client := elasticsearch.MRCommitSearchClient.GetSearchConnection().
		FetchSourceContext(fetchContent).
		Size(1000).
		Query(baseMatchQueries)

	res, err := client.Do(ctx)

	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrEsSearchFail.Error(), err.Error())
		return gresult.Err[[]int64](bits_err.OPTIMUS.ErrEsSearchFail)
	}

	bitsMRIDs := make([]int64, 0)
	for _, item := range res.Hits.Hits {
		v := &MRGeneralSearchModel{}
		str, _ := item.Source.MarshalJSON()
		_ = json.Unmarshal(str, v)
		if v.BitsMRID > 0 {
			bitsMRIDs = append(bitsMRIDs, v.BitsMRID)
		}
	}
	logs.CtxInfo(ctx, "commit_message_search_result: %v", bitsMRIDs)
	return gresult.OK(bitsMRIDs)
}

func (s *Search) commonSearchPrefixExistCheck(text string) bool {
	for _, p := range CommonSearchWhiteList {
		if strings.HasPrefix(text, p.Prefix) {
			return true
		}
	}
	return false
}

func (s *Search) QueryPrefixWithFunResult(ctx context.Context, text string, projectIDs []string, username string, lastID int64) gresult.R[[]int64] {
	text = strings.Trim(text, " ")
	var controller prefixFun
	for _, t := range CommonSearchWhiteList {
		if arr := strings.Split(text, t.Prefix); len(arr) > 1 {
			text = arr[1]
			controller = t.Fun
			break
		}
	}
	return controller(ctx, text, projectIDs, username, lastID)
}

func (s *Search) getMRTags(ctx context.Context, text string, groupName string, useBuiltin bool) ([]int64, error) {
	t := &tag{}
	answer := make([]int64, 0)

	// 先通过 ES 找到对应的 tag
	tags := t.Search(ctx, text, groupName, useBuiltin)
	if tags.IsErr() || len(tags.Must()) == 0 {
		return answer, nil
	}
	tagsId := make([]int64, 0)
	for _, item := range tags.Must() {
		tagsId = append(tagsId, item.Id)
	}

	// 在查数据库查找 tag 绑定的 mr
	mrsId := make([]int64, 0)
	if len(groupName) == 0 {
		mrsId, _ = data.GetMrIDByTagsId(ctx, tagsId)
	} else {
		mrsId, _ = data.BatchGetMrIDByTagsIDAndGroupName(ctx, tagsId, groupName, 500, 4)
	}
	if mrsId != nil && len(mrsId) != 0 {
		for _, val := range mrsId {
			answer = append(answer, val)
		}
	}
	return answer, nil
}

func (s *Search) filterMRIDsByMRTagsAndMrIDs(ctx context.Context, text string, mrIDs []int64, groupName string, useBuiltin bool) ([]int64, error) {
	t := &tag{}
	answer := make([]int64, 0)

	// 先通过 ES 找到对应的 tag
	tags := t.Search(ctx, text, groupName, useBuiltin)
	if tags.IsErr() || len(tags.Must()) == 0 {
		return answer, nil
	}
	tagsId := make([]int64, 0)
	for _, item := range tags.Must() {
		tagsId = append(tagsId, item.Id)
	}

	// 在查数据库查找 tag 绑定的 mr
	mrsId := make([]int64, 0)
	if len(groupName) == 0 {
		mrsId, _ = data.FilterMRIDsByTagIDs(ctx, tagsId, mrIDs)
	} else {
		mrsId, _ = data.BatchFilterMRIDsByTagIDsAndGroupName(ctx, tagsId, mrIDs, groupName, 500, 4)
	}
	if mrsId != nil && len(mrsId) != 0 {
		for _, val := range mrsId {
			answer = append(answer, val)
		}
	}
	return answer, nil
}

func (s *Search) getGroupAllProjectsID(ctx context.Context, groupName string) gresult.R[[]string] {
	projects := make([]string, 0)
	group := data.GetConfigGroupInfoByGroupName(ctx, groupName, true)
	// 找到后判断有多少个绑定仓库
	if group.IsErr() {
		return gresult.Err[[]string](bits_err.OPTIMUS.ErrRecordNotFound)
	}
	projects = append(projects, group.Must().ProjectID)
	if arr, _ := data.GetConfigProjectsInfoByMainGroupID(ctx, group.Must().ID); arr != nil {
		for _, item := range arr {
			projects = append(projects, item.ProjectID)
		}
	}
	return gresult.OK(projects)
}

// Limit the amount of mrs to avoid query failed with ES (length limit)
func (s *Search) getReviewerLegalGitlabMRID(ctx context.Context, req *SearchMRListConditionQuery) ([]int64, error) {
	sameIDs := make([]int64, 0)
	if len(req.ReviewerName) == 0 {
		return sameIDs, nil
	}
	states := []code_review_model.ReviewStatus{
		code_review_model.ReviewStatus_UNKNOWN,
		code_review_model.ReviewStatus_PENDING,
		code_review_model.ReviewStatus_FETCHING,
		code_review_model.ReviewStatus_RUNNING,
		code_review_model.ReviewStatus_APPROVED,
		code_review_model.ReviewStatus_REJECTED,
		code_review_model.ReviewStatus_ALWAYS_APPROVED,
	}
	var qaStates []qa_review.QaStatus
	if req.ReviewState == "all" {
		qaStates = append(qaStates, qa_review.QaStatus_testing, qa_review.QaStatus_approved, qa_review.QaStatus_rejected)
	} else if req.ReviewState == "approved" {
		qaStates = append(qaStates, qa_review.QaStatus_approved)
	} else if req.ReviewState == "assigned" {
		qaStates = append(qaStates, qa_review.QaStatus_testing)
	} else if req.ReviewState == "in_progress" {
		qaStates = append(qaStates, qa_review.QaStatus_testing)
	}
	lastId := req.LastID
	if req.Sort == "mr_merged_time" {
		lastId = math.LimitsMaxInt64
	}
	cq := &code_review_model.GetMrIDListFilterByNameAndStateReq{
		Username:  req.ReviewerName,
		State:     code_review_model.ReviewStatus_APPROVED,
		LastId:    lastId,
		Limit:     200,
		StateList: states,
	}
	reviewers, err := rpc.CodeReviewModelClient.GetMrIDListFilterByNameAndStateV2(ctx, cq)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	if reviewers != nil && len(reviewers.MrIds) != 0 {
		sameIDs = append(sameIDs, reviewers.MrIds...)
	}

	qaReviewers, err := bytedance_bits_qa_review.GetMrIDListFilterByNameAndState(ctx, req.ReviewerName, qaStates, 200, lastId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	if qaReviewers != nil && len(qaReviewers.GetMrIds()) != 0 {
		sameIDs = append(sameIDs, qaReviewers.GetMrIds()...)
	}
	//
	if reviewers != nil && len(reviewers.MrIds) != 0 {
		sameIDs = append(sameIDs, reviewers.MrIds...)
	}
	return sameIDs, err
}

func (s *Search) getTagLegalGitlabMRID(ctx context.Context, req *SearchMRListConditionQuery, useBuiltin bool) ([]int64, error) {
	sameIDs := make([]int64, 0)
	if len(req.Tags) == 0 {
		return sameIDs, nil
	}
	res, err := s.getMRTags(ctx, req.Tags, req.GroupName, useBuiltin)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	if res != nil {
		sameIDs = append(sameIDs, res...)
		return sameIDs, nil
	}

	return sameIDs, nil
}

func (s *Search) getTagLegalGitlabMRIDWithMRIDs(ctx context.Context, req *SearchMRListConditionQuery, mrIDs []int64, useBuiltin bool) ([]int64, error) {
	sameIDs := make([]int64, 0)
	if len(req.Tags) == 0 {
		return sameIDs, nil
	}
	res, err := s.filterMRIDsByMRTagsAndMrIDs(ctx, req.Tags, mrIDs, req.GroupName, useBuiltin)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	if res != nil {
		sameIDs = append(sameIDs, res...)
		return sameIDs, nil
	}

	return sameIDs, nil
}

func (s *Search) getKeywordLegalGitlabMRID(ctx context.Context, req *SearchMRListConditionQuery) gresult.R[[]int64] {
	sameIDs := make([]int64, 0)
	if len(req.KeyWord) == 0 {
		return gresult.OK(sameIDs)
	}
	return s.QueryPrefixWithFunResult(ctx, strings.Trim(req.KeyWord, " "), req.ProjectsID, req.AuthorName, req.LastID)
}

// QueryMRCommonAnalyze
// @Title MR 列表搜索
func (s *Search) QueryMRCommonAnalyze(ctx context.Context, query *SearchMRListConditionQuery) ([]int64, error) {
	//
	sameIDs := make([]int64, 0)

	// 1. 根据 group_name 获取所有的 project id 信息
	if projects := s.getGroupAllProjectsID(ctx, query.GroupName); projects.IsOK() {
		query.ProjectsID = projects.Must()
	}

	// 2.
	if query.LastID == 0 {
		query.LastID = math.LimitsMaxInt64
	}

	// 3. 如果用户指定了 reviewer_name 就先请求 CodeReview 服务
	//    找到 reviewer_name 相关的 mr id 数组后
	//    再去主索引中过滤
	if len(query.ReviewerName) != 0 {
		s.Searched = true
		if res, err := s.getReviewerLegalGitlabMRID(ctx, query); err == nil {
			if len(res) == 0 {
				return sameIDs, nil
			}
			sameIDs = append(sameIDs, res...)
			sameIDs = gslice.Uniq(sameIDs)
		} else if err != nil {
			logs.CtxError(ctx, err.Error())
			return nil, err
		} else {
			return sameIDs, nil
		}
	}

	// 4. 如果用户搜索了 tag 就先查数据库
	//    找到 tag 对应的 mr id 数组后
	//    再去主索引中过滤
	//    注意: 此处为了性能和服务稳定性, 最多返回 2000 个 mr id
	largeTags := tcc.GetLargeMRTagList(ctx, query.GroupName)
	matchLargeTags := false
	largeTagSearching := false
	if len(query.Tags) != 0 && len(largeTags) > 0 {
		tagsArr := strings.Split(query.Tags, ",")
		matchLargeTags = sets.NewStringSetFromSlice(largeTags).ContainsAll(tagsArr)
	}

	productVersionMRIDs := make([]int64, 0)
	if matchLargeTags && len(query.ProductVersion) > 0 && len(query.ProjectsID) > 0 {
		logs.CtxInfo(ctx, "handling large tags and product version (%v) (%v)", query.ProductVersion, query.ProjectsID)
		totalMRs, err := data.BatchGetMergeRequestStatusByProjectIDAndVersion(ctx, query.ProjectsID, query.ProductVersion, 500, 4)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			return nil, err
		}
		productVersionMRIDs = gslice.Map(totalMRs, func(item *model.OptimusGitlabMergeRequest) int64 {
			return item.ID
		})
		largeTagSearching = true
	}

	if len(query.Tags) != 0 && largeTagSearching {
		s.Searched = true
		if res, err := s.getTagLegalGitlabMRIDWithMRIDs(ctx, query, productVersionMRIDs, true); err == nil {
			if len(res) == 0 {
				return sameIDs, nil
			}
			sameIDs = append(sameIDs, res...)
			sameIDs = gslice.Uniq(sameIDs)
		} else if err != nil {
			logs.CtxError(ctx, err.Error())
			return nil, err
		} else {
			return sameIDs, nil
		}
	}

	if len(query.Tags) != 0 && !largeTagSearching {
		s.Searched = true
		if res, err := s.getTagLegalGitlabMRID(ctx, query, true); err == nil {
			if len(res) == 0 {
				return sameIDs, nil
			}
			sameIDs = append(sameIDs, res...)
			sameIDs = gslice.Uniq(sameIDs)
		} else if err != nil {
			logs.CtxError(ctx, err.Error())
			return nil, err
		} else {
			return sameIDs, nil
		}
	}

	// 5. 如果用户指定了 commit, pod, path 等相关的内容, 先搜索 ES
	//    找到对应的 mr id 数组后
	//    再去主索引中过滤
	//    注意: 此处为了性能和服务稳定性, 最多返回 1000 个 mr id
	if s.commonSearchPrefixExistCheck(query.KeyWord) {
		s.Searched = true
		query.Sort = "id" // 排序默认按照 id 进行排序
		res := s.getKeywordLegalGitlabMRID(ctx, query)
		if res.IsOK() {
			if len(res.Must()) == 0 {
				return sameIDs, nil
			}
			sameIDs = append(sameIDs, res.Must()...)
			sameIDs = gslice.Uniq(sameIDs)
		}
		if res.IsErr() {
			return nil, res.Err()
		}
	}

	logs.CtxInfo(ctx, "QueryMRCommonAnalyze same id: %+v", sameIDs)
	return sameIDs, nil
}

func (s *Search) DoCommonSearch(ctx context.Context, query *SearchMRListConditionQuery, ids []int64) ([]int64, error) {

	// 如果传了空间，但是空间内部没有仓库就直接返回空
	if len(query.ProjectsID) == 0 && len(query.GroupName) != 0 {
		return []int64{}, nil
	}

	// 非强一致性要求的 search 可以放到 es 中做
	sameId := make([]int64, 0)
	if esInfo, err := s.QueryMR(ctx, query, ids); len(esInfo) > 0 {
		for _, item := range esInfo {
			sameId = append(sameId, item.ID)
		}
	} else if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	return sameId, nil
}

func (s *Search) BatchDoCommonSearch(ctx context.Context, query *SearchMRListConditionQuery, ids []int64, batchSize int) ([]int64, error) {

	// 如果传了空间，但是空间内部没有仓库就直接返回空
	if len(query.ProjectsID) == 0 && len(query.GroupName) != 0 {
		return []int64{}, nil
	}

	// 非强一致性要求的 search 可以放到 es 中做
	sameId := make([]int64, 0)
	chunks := gslice.Chunk(ids, batchSize)
	for _, chunk := range chunks {
		logs.CtxInfo(ctx, "do chunk search for chunk len(%d)", len(chunk))
		esInfo, err := s.QueryMR(ctx, query, chunk)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			return nil, err
		}
		sameId = append(sameId, gslice.Map(esInfo, func(r *MRSearchResult) int64 {
			return r.ID
		})...)
	}
	return sameId, nil
}

func (s *Search) getTimeRangeQuery(req *SearchMRListConditionQuery) []elastic.Query {
	filterQuery := make([]elastic.Query, 0)
	if req.CreatedTime != nil {
		gte := time.Unix(req.CreatedTime.Gte, 0)
		lte := time.Unix(req.CreatedTime.Lte, 0)
		filterQuery = append(filterQuery, elastic.NewRangeQuery("mr_create_time").Gte(s.increase8Hours(&gte)).Lte(s.increase8Hours(&lte)))
	}
	if req.MergedTime != nil {
		gte := time.Unix(req.MergedTime.Gte, 0)
		lte := time.Unix(req.MergedTime.Lte, 0)
		filterQuery = append(filterQuery, elastic.NewRangeQuery("mr_merged_time").Gte(s.increase8Hours(&gte)).Lte(s.increase8Hours(&lte)))
	}
	return filterQuery
}

func (s *Search) getFeatureQuery(req *SearchMRListConditionQuery) []elastic.Query {
	filterQuery := make([]elastic.Query, 0)
	if len(req.FeatureType) == 0 {
		return filterQuery
	}
	if len(req.FeatureSearchText) != 0 {
		filterQuery = append(filterQuery, elastic.NewQueryStringQuery(fmt.Sprintf("mr_feature_info:\"%s\"", req.FeatureSearchText)))
	}
	switch req.FeatureType {
	case "meego":
		filterQuery = append(filterQuery, elastic.NewMatchQuery("mr_feature_info", "meego.bytedance.net"))
	case "slardar":
		filterQuery = append(filterQuery, elastic.NewMatchQuery("mr_feature_info", "slardar.bytedance.net"))
	case "rocket":
		filterQuery = append(filterQuery, elastic.NewMatchQuery("mr_feature_info", "rocket.bytedance.net"))
	case "jira":
		filterQuery = append(filterQuery, elastic.NewMatchQuery("mr_feature_info", "jira.bytedance.com"))
	}
	return filterQuery
}

func (s *Search) getIDQuery(IDs []int64) []elastic.Query {
	query := make([]elastic.Query, 0)
	if len(IDs) == 0 {
		return query
	}

	qi := make([]interface{}, 0)
	for _, item := range IDs {
		qi = append(qi, item)
		str := strconv.FormatInt(item, 10)
		query = append(query, elastic.NewMatchQuery("mr_child_mr", str))
	}
	query = append(query, elastic.NewTermsQuery("id", qi...))
	return query
}

func (s *Search) getConflictQuery(req *SearchMRListConditionQuery) elastic.Query {
	var query elastic.Query
	if req.Conflicted == -1 {
		return query
	}
	// 0 未检查, 1 冲突， 2不冲突，3检查中， 4 检查失败
	if req.Conflicted == 0 {
		query = elastic.NewTermsQuery("mr_conflicted", 0, 2, 3)
	} else if req.Conflicted == 1 {
		query = elastic.NewTermsQuery("mr_conflicted", 1)
	}
	return query
}

func (s *Search) getSourceBranchQuery(req *SearchMRListConditionQuery) []elastic.Query {
	query := make([]elastic.Query, 0)
	if len(req.SourceBranch) == 0 {
		return query
	}
	query = append(query, elastic.NewTermQuery("mr_source_branch.keyword", req.SourceBranch))
	query = append(query, elastic.NewQueryStringQuery(fmt.Sprintf("mr_child_source_branch:\"%s\"", req.SourceBranch)))
	return query
}

func (s *Search) getTargetBranchQuery(req *SearchMRListConditionQuery) elastic.Query {
	if len(req.TargetBranch) == 0 {
		return nil
	}
	targets := strings.Split(req.TargetBranch, ",")
	t := make([]interface{}, 0)
	for _, item := range targets {
		t = append(t, item)
	}

	return elastic.NewTermsQuery("mr_target_branch.keyword", t...)
}

func (s *Search) getChildTargetBranchQuery(req *SearchMRListConditionQuery) []elastic.Query {
	if len(req.ChildTargetBranch) == 0 {
		return nil
	}
	targets := strings.Split(req.ChildTargetBranch, ",")
	t := make([]interface{}, 0)
	ct := make([]elastic.Query, 0)
	for _, item := range targets {
		t = append(t, item)
		ct = append(ct, elastic.NewQueryStringQuery(fmt.Sprintf("mr_child_target_branch:\"%s\"", item)))
	}

	query := make([]elastic.Query, 0)

	query = append(query, elastic.NewTermsQuery("mr_target_branch.keyword", t...))
	query = append(query, ct...)

	return query
}

func (s *Search) getVersionQuery(req *SearchMRListConditionQuery) []elastic.Query {
	query := make([]elastic.Query, 0)
	if len(req.ProductVersion) == 0 && len(req.TargetVersion) == 0 {
		return query
	}
	//baseMatchQueries.Must(elastic.NewTermQuery("product_version.keyword", req.ProductVersion))
	for _, item := range []string{req.ProductVersion, req.TargetVersion} {
		if len(item) != 0 {
			query = append(query, elastic.NewBoolQuery().Should(elastic.NewMatchQuery("mr_child_publishversion", item), elastic.NewTermQuery("product_version.keyword", item)).MinimumNumberShouldMatch(1))
		}
	}
	return query
}

func (s *Search) getMRTypeQuery(req *SearchMRListConditionQuery) []elastic.Query {
	query := make([]elastic.Query, 0)
	if len(req.MrType) == 0 {
		return query
	}
	mrTypes := strings.Split(req.MrType, ",")
	for _, item := range mrTypes {
		query = append(query, elastic.NewTermQuery("mr_type.keyword", item))
	}
	return query
}

func (s *Search) getKeywordQuery(req *SearchMRListConditionQuery) []elastic.Query {
	query := make([]elastic.Query, 0)
	if s.commonSearchPrefixExistCheck(req.KeyWord) || len(req.KeyWord) == 0 {
		return query
	}
	keyWord := strings.TrimSuffix(strings.TrimPrefix(req.KeyWord, " "), " ")
	query = append(query, elastic.NewMatchPhraseQuery("mr_title", keyWord))
	return query
}

func (s *Search) getProjectsIDQuery(req *SearchMRListConditionQuery) []elastic.Query {
	query := make([]elastic.Query, 0)
	if req.ProjectsID == nil {
		return query
	}
	qi := make([]interface{}, 0)
	for _, item := range req.ProjectsID {
		qi = append(qi, item)
		// 处理多宿主场景, 只要子仓中包含了直接依赖的仓库就算命中
		query = append(query, elastic.NewBoolQuery().Must(elastic.NewMatchQuery("mr_child_project_id", item), elastic.NewTermQuery("mr_multiple_mr_type.keyword", "multiple_master_mr")))
	}
	query = append(query, elastic.NewTermsQuery("mr_project_gitlab_id", qi...))
	return query
}

func (s Search) getQueryLimit(req *SearchMRListConditionQuery) int {
	limit := 10
	if req.Limit != 0 {
		limit = int(req.Limit)
	}
	return limit
}

func (s *Search) getSortAndOrder(req *SearchMRListConditionQuery) (string, bool) {
	sort := "id"
	order := false
	if len(req.Sort) != 0 {
		sort = req.Sort
	}
	if req.Order == "asc" {
		order = true
	}
	return sort, order
}

func (s *Search) getSorters(req *SearchMRListConditionQuery) []elastic.Sorter {
	sorters := make([]elastic.Sorter, 0)
	if len(req.Sort) != 0 {
		newFieldSorter := elastic.NewFieldSort(req.Sort)
		if req.Order != "asc" {
			newFieldSorter.Desc()
		}
		sorters = append(sorters, newFieldSorter)
	} else {
		sorters = append(sorters, elastic.NewFieldSort("id").Desc())
	}
	if len(req.KeyWord) != 0 && !s.commonSearchPrefixExistCheck(req.KeyWord) {
		sorters = append(sorters, elastic.NewScoreSort())
	}
	return sorters
}

func (s *Search) getMustQuery(req *SearchMRListConditionQuery, IDs []int64) *elastic.BoolQuery {
	baseMatchQueries := elastic.NewBoolQuery()

	// id
	if res := s.getIDQuery(IDs); len(res) != 0 {
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	// 我的 mr, 待我 review 不需要限制仓库范围
	// 其他 tab 下需要限制仓库范围
	if req.Source != "mine" && req.Source != "review" {
		if res := s.getProjectsIDQuery(req); len(res) != 0 {
			baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
			req.hasSearched = true
		}
	}

	// project_id
	if req.TargetProjectID != 0 {
		str := strconv.FormatInt(req.TargetProjectID, 10)
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("mr_child_project_id", str), elastic.NewTermQuery("mr_project_gitlab_id", req.TargetProjectID)).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	// mr author_name
	if len(req.AuthorName) != 0 {
		baseMatchQueries.Must(elastic.NewTermQuery("mr_author_name.keyword", req.AuthorName))
		req.hasSearched = true
	}

	// source branch
	if res := s.getSourceBranchQuery(req); len(res) != 0 {
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	// child target branch
	if res := s.getChildTargetBranchQuery(req); res != nil {
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	// version
	if res := s.getVersionQuery(req); len(res) != 0 {
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	// keyword
	if res := s.getKeywordQuery(req); len(res) > 0 {
		baseMatchQueries.Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
		req.hasSearched = true
	}

	return baseMatchQueries
}

func (s *Search) increase8Hours(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Add(time.Hour * 8).Format(time.RFC3339)
}

func (s *Search) getLastIDQuery(ctx context.Context, req *SearchMRListConditionQuery) []elastic.Query {
	filterQuery := make([]elastic.Query, 0)
	if req.LastID == 0 {
		return filterQuery
	}
	info, err := data.GetMergeRequestInfoByMrID(ctx, req.LastID, true)
	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrMRNotExist.Msg())
	}
	if req.Sort == "mr_merged_time" && info != nil {
		filterQuery = append(filterQuery, elastic.NewRangeQuery("mr_merged_time").Lt(s.increase8Hours(info.MergedAt)))
	}
	if req.Sort == "mr_create_time" && info != nil {
		filterQuery = append(filterQuery, elastic.NewRangeQuery("mr_create_time").Lt(s.increase8Hours(info.CreateTime)))
	}
	if req.Sort == "id" || req.Sort == "" {
		filterQuery = append(filterQuery, elastic.NewRangeQuery("id").Lt(req.LastID))
	}
	return filterQuery
}

func (s *Search) getBusinessTypeQuery(req *SearchMRListConditionQuery) elastic.Query {
	var query elastic.Query
	if req.BusinessType < 0 && len(req.BusinessTypes) == 0 {
		return query
	}
	if len(req.BusinessTypes) > 0 {
		query = elastic.NewTermsQuery("business_type", gslice.Map(req.BusinessTypes, func(b int32) interface{} {
			return b
		})...)
	} else {
		query = elastic.NewTermQuery("business_type", req.BusinessType)
	}
	return query
}

func (s *Search) getFilterQuery(ctx context.Context, req *SearchMRListConditionQuery) []elastic.Query {
	//
	filterQuery := make([]elastic.Query, 0)
	filterQuery = append(filterQuery, elastic.NewTermQuery("mr_has_parent", 0))
	filterQuery = append(filterQuery, s.getTimeRangeQuery(req)...)

	// feature
	filterQuery = append(filterQuery, s.getFeatureQuery(req)...)
	if len(req.State) != 0 && req.State != "all" {
		filterQuery = append(filterQuery, elastic.NewTermQuery("mr_state.keyword", req.State))
		req.hasSearched = true
	}

	// wip
	if req.Wip != -1 {
		filterQuery = append(filterQuery, elastic.NewTermQuery("mr_work_in_progress", req.Wip))
		req.hasSearched = true
	}

	// conflict
	if res := s.getConflictQuery(req); res != nil {
		filterQuery = append(filterQuery, res)
		req.hasSearched = true
	}

	// mr_type
	if res := s.getMRTypeQuery(req); len(res) != 0 {
		filterQuery = append(filterQuery, res...)
		req.hasSearched = true
	}

	// target branch
	if res := s.getTargetBranchQuery(req); res != nil {
		filterQuery = append(filterQuery, res)
		req.hasSearched = true
	}

	// 聚合
	if res := s.getLastIDQuery(ctx, req); len(res) > 0 {
		filterQuery = append(filterQuery, res...)
		req.hasSearched = true
	}

	// business type, default only get mr with 0 business_type
	if res := s.getBusinessTypeQuery(req); res != nil {
		filterQuery = append(filterQuery, res)
		req.hasSearched = true
	}

	return filterQuery
}

func (s *Search) changeInvalidParams(req *SearchMRListConditionQuery) {
	if req.Sort == "mr_merged_time" {
		req.State = "merged"
	}
}

func (s *Search) QueryMR(ctx context.Context, req *SearchMRListConditionQuery, IDs []int64) ([]*MRSearchResult, error) {

	// 修正错误参数
	s.changeInvalidParams(req)

	// must query
	baseMatchQueries := s.getMustQuery(req, IDs)

	// filter
	filterQuery := s.getFilterQuery(ctx, req)
	if len(filterQuery) != 0 {
		req.hasSearched = true
		baseMatchQueries.Filter(filterQuery...)
	}

	// fetch content
	fetchContent := elastic.NewFetchSourceContext(true).Include("mr_id", "id", "mr_project_gitlab_id", "mr_child_mr")

	// get sorts
	sorts := s.getSorters(req)
	logs.CtxInfo(ctx, "es baseMatchQueries: %v", utils.ToJson(baseMatchQueries))
	logs.CtxInfo(ctx, "es sorts: %v", utils.ToJson(sorts))

	// build ES client
	client := elasticsearch.MergeRequestSearchClient.GetSearchConnection()
	client.SortBy(sorts...).
		Size(s.getQueryLimit(req)).
		FetchSourceContext(fetchContent).
		Query(baseMatchQueries)

	if req.From != 0 {
		client.From(int(req.From))
	}

	// ES 搜索
	res, err := client.Do(ctx)

	//
	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrEsSearch.Msg(), err.Error())
		return nil, bits_err.OPTIMUS.ErrEsSearch
	}

	// ES 搜索结果的转换与去重
	ret := s.adaptor.GetMRSearchResult(ctx, res)
	logs.CtxInfo(ctx, "es results: %v", utils.ToJson(ret))

	return ret, nil
}

func (s *Search) CountMrAmount(ctx context.Context, req *SearchMRListConditionQuery, IDs []int64) (int64, error) {
	// 搜索总数，不需要分页生效
	s.changeInvalidParams(req)
	req.LastID = 0
	req.Limit = math.LimitsMaxInt64

	baseMatchQueries := s.getMustQuery(req, IDs)
	filterQuery := s.getFilterQuery(ctx, req)
	if len(filterQuery) != 0 {
		req.hasSearched = true
		baseMatchQueries.Filter(filterQuery...)
	}
	client := elasticsearch.MergeRequestSearchClient.GetCountConnection().Query(baseMatchQueries)

	res, err := client.Do(ctx)

	if err != nil {
		logs.CtxError(ctx, bits_err.OPTIMUS.ErrEsSearch.Msg(), err.Error())
		return 0, bits_err.OPTIMUS.ErrEsSearch
	}
	return res, nil
}
