load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "version",
    srcs = ["version.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/version",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/optimus/service/elasticsearch",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_toutiao_elastic_v7//:elastic",
    ],
)
