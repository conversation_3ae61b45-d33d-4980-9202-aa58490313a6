package model

import (
	"time"
)

const OptimusPackageArtifactTableName = "optimus_package_artifact"

type OptimusPackageArtifact struct {
	ID           int64      `bson:"id" json:"id"`
	CreateTime   *time.Time `bson:"create_time" json:"create_time"`
	UpdateTime   *time.Time `bson:"update_time" json:"update_time"`
	URL          string     `bson:"url" json:"url"`
	ErrorMessage string     `bson:"err_msg" json:"err_msg"`
	Name         string     `bson:"name" json:"name"`
	PackageID    int64      `bson:"package_id" json:"package_id"`
	Type         string     `bson:"type" json:"type"`
}
