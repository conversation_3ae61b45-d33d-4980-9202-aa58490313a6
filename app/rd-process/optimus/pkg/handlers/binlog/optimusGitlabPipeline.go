package binlog

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/graph"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/mr"
	"context"

	"code.byted.org/devinfra/hagrid/libs/bits_err"

	"code.byted.org/gopkg/dbus"
	"code.byted.org/gopkg/logs"
)

const optimusGitlabPipeline = "optimus_gitlab_pipeline"

func handleUpdatePipeline(ctx context.Context, beforeColumns, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	var before, after *model.OptimusGitlabPipeline
	err := UnmarshalColumns(ctx, beforeColumns, &before)
	if err != nil {
		logs.CtxWarn(ctx, "failed to unmarshal columns to OptimusGitlabComponentVersion:%+v,error:%s", beforeColumns, err.Error())
		return nil
	}
	err = UnmarshalColumns(ctx, afterColumns, &after)
	if err != nil {
		logs.CtxWarn(ctx, "failed to unmarshal columns to OptimusGitlabComponentVersion:%+v,error:%s", afterColumns, err.Error())
		return nil
	}
	if before.Status != after.Status {
		mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, after.MergeRequestID, false)
		if err != nil {
			return bits_err.OPTIMUS.ErrGetMrFail
		}
		if mrInfo == nil || mrInfo.LatestInternalID != after.ID {
			return nil
		}
		if mrInfo.ID > 0 {
			m := mr.MergeRequest{}
			err = m.FillUniqueID(ctx, nil, nil, &mrInfo.ID)
			if err != nil {
				logs.CtxError(ctx, "failed to fill mr id:%s", err.Error())
				return nil
			}
			return graph.PushUpdate(ctx, &m, optimusGitlabPipeline)
		}
	}
	return nil
}

func handleInsertPipeline(ctx context.Context, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	var after *model.OptimusGitlabPipeline
	err := UnmarshalColumns(ctx, afterColumns, &after)
	if err != nil {
		logs.CtxWarn(ctx, "failed to unmarshal columns to OptimusGitlabComponentVersion:%+v,error:%s", afterColumns, err.Error())
		return nil
	}
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, after.MergeRequestID, false)
	if err != nil {
		return bits_err.OPTIMUS.ErrGetMrFail
	}
	if mrInfo == nil || mrInfo.LatestInternalID != after.ID {
		return nil
	}
	if mrInfo.ID > 0 {
		m := mr.MergeRequest{}
		err = m.FillUniqueID(ctx, nil, nil, &mrInfo.ID)
		if err != nil {
			logs.CtxError(ctx, "failed to fill mr id:%s", err.Error())
			return nil
		}
		return graph.PushUpdate(ctx, &m, optimusGitlabPipeline)
	}
	return nil
}
