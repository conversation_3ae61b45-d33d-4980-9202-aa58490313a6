package reviewer

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"

	utils2 "code.byted.org/bytedtrace/bytedtrace-client-go/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/basic"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/merge_request"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/mr"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/reviewer"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/tcc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	commonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/facility/ternary"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_optimus_infra/kitex_gen/bits/optimus/infra"
	"code.byted.org/overpass/bits_optimus_infra/rpc/bits_optimus_infra"
	"code.byted.org/overpass/bytedance_bits_code_review/rpc/bytedance_bits_code_review"
	"code.byted.org/overpass/bytedance_bits_config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/overpass/bytedance_bits_qa_review/kitex_gen/bytedance/bits/qa_review"
	"code.byted.org/overpass/bytedance_bits_qa_review/rpc/bytedance_bits_qa_review"
	json "github.com/bytedance/sonic"
	"golang.org/x/sync/errgroup"
)

func getState(state string) string {
	switch state {
	case "APPROVED", "approved":
		return consts.OPTIMUS_REVIEW_APPROVE
	case "ALWAYS_APPROVED":
		return consts.OPTIMUS_REVIEW_ALWAYS_APPROVE
	case "REJECTED":
		return consts.OPTIMUS_REVIEW_REJECT
	default:
		return consts.OPTIMUS_REVIEW_ASSIGNED
	}
}
func getMRIDInfo(mrUniqueID *model.MergeRequestUniqueID) *optimus.MrIDInfo {
	return &optimus.MrIDInfo{
		MrID:      mrUniqueID.ID,
		IID:       mrUniqueID.IID,
		ProjectID: mrUniqueID.ProjectID,
	}
}

func getRDReviewByMR(ctx context.Context, mrUniqueID *model.MergeRequestUniqueID) ([]*optimus.Reviewer, error) {
	users := make([]*optimus.Reviewer, 0)
	cr, err := bytedance_bits_code_review.GetReviewerListWithMrID(ctx, mrUniqueID.ID)
	if err != nil {
		return nil, err
	}
	if cr == nil {
		return users, nil
	}

	for _, user := range cr.ReviewerList {
		info, _ := data.GetUserByEnName(ctx, user.Username, true)
		if info == nil || info.ID == 0 {
			continue
		}
		v := &optimus.Reviewer{
			Username:   user.Username,
			ReviewRole: user.Role.String(),
			Status:     getState(user.Status.String()),
			Removable:  &user.CanRemove,
			ZhName:     &user.ZhName,
			MrIDInfo:   getMRIDInfo(mrUniqueID),
		}
		users = append(users, v)
	}
	return users, nil
}
func getQAByMR(ctx context.Context, mrUniqueID *model.MergeRequestUniqueID) ([]*optimus.Reviewer, error) {
	users := make([]*optimus.Reviewer, 0)
	cr, err := bytedance_bits_qa_review.RawCall.GetAllQaTesters(ctx, &qa_review.GetAllQaTestersRequest{
		DevId: 0,
		MrId:  &mrUniqueID.ID,
	})
	if err != nil {
		return nil, err
	}
	if cr == nil {
		return users, nil
	}

	qaSet := set.NewStringSet()
	for _, singleMR := range cr.Hosts {

		logs.CtxInfo(ctx, "call qa review, %d, %d", singleMR.MrId, mrUniqueID.ID)
		if singleMR.MrId != mrUniqueID.ID {
			continue
		}
		for _, user := range singleMR.Testers {
			getUsername := strings.Split(user.Email, "@")
			if len(getUsername) <= 1 {
				return nil, bits_err.OPTIMUS.ErrParamsFail
			}
			username := getUsername[0]
			info, err := data.GetUserByEnName(ctx, username, true)
			if err != nil {
				return nil, err
			}
			if info == nil || info.ID == 0 {
				return nil, bits_err.OPTIMUS.ErrUserNotExist
			}
			// 去重
			if qaSet.Exist(username) {
				continue
			}
			v := &optimus.Reviewer{
				Username:   username,
				ReviewRole: "QA",
				Status:     getState(user.Status),
				ZhName:     &info.Name,
				MrIDInfo:   getMRIDInfo(mrUniqueID),
			}
			users = append(users, v)
			qaSet.Add(username)
		}
	}
	return users, nil
}

/*
只需保证关注迁移 OneSite 之前的权限结果，前端会在新平台上使用 IAM 组件鉴权; 新版直接展示所有按钮
*/
func hasAppTaskAdvancedPermission(ctx context.Context, groupConfigs []*model.OptimusConfigGroup, username string) bool {
	if commonUtils.ParseBitsApiFromOneSite(ctx) {
		return true
	}

	var authorized = false
	for _, cfg := range groupConfigs {
		if gslice.Contains(cfg.GetMaintainers(), username) || rpc.IsAdmin(ctx, cfg.Name, username) {
			authorized = true
			break
		}
	}

	return authorized
}

func GetInfosByMrID(ctx context.Context, req *optimus.GetMrReviewersQuery) (*optimus.GetMrReviewersResponse, error) {
	mrObj := &mr.MergeRequest{}
	err := mrObj.FillUniqueID(ctx, req.ProjectID, req.IID, &req.MrID)
	if err != nil {
		return nil, err
	}
	mrs, err := mrObj.GetBitsRelation(ctx)
	if err != nil {
		return nil, err
	}
	mrsID := make([]int64, 0)
	for _, v := range mrs {
		mrsID = append(mrsID, v.ID)
	}
	mrsInfo, err := data.GetMRsInfoInID(ctx, mrsID, false)
	if err != nil {
		return nil, err
	}
	// 获取 reviewer 所归属的 mr
	answer := make([]*optimus.Reviewer, 0)

	g := new(errgroup.Group)
	lock := sync.Mutex{}
	for _, info := range mrsInfo {
		pID, err := strconv.ParseInt(info.ProjectID, 10, 64)
		mrUniqueID := &model.MergeRequestUniqueID{
			ProjectID: pID,
			IID:       info.IID,
			ID:        info.ID,
		}
		ctx := ctx
		g.Go(func() error {
			defer utils.DeferLogFatal(ctx)
			if err != nil {
				return err
			}
			// get reviewer from reviewer service
			rdUsers, err := getRDReviewByMR(ctx, mrUniqueID)
			if err != nil {
				return err
			}
			// get reviewer from qa reviewer service
			logs.CtxInfo(ctx, "call qa review")
			qaUsers, err := getQAByMR(ctx, mrUniqueID)
			if err != nil {
				return err
			}
			lock.Lock()
			answer = append(answer, rdUsers...)
			answer = append(answer, qaUsers...)
			lock.Unlock()
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	return &optimus.GetMrReviewersResponse{
		List: answer,
	}, nil
}

/**
  {
      "branch_force_merge_config": [{
          "branch": "^.*$",
          "force_merge_admins": ["zhangchunsheng"]
      }]
  }
*/

type commonConfig struct {
	BranchForceMergeConfig []*branchForceMergeConfig `json:"branch_force_merge_config"`
}

type branchForceMergeConfig struct {
	Branch           string   `json:"branch"`
	ForceMergeAdmins []string `json:"force_merge_admins"`
}

func GetPermissionsInfo(ctx context.Context, req *optimus.GetReviewPermissionsQuery) (*optimus.GetReviewPermissionsResponse, error) {
	dev, err := bits_optimus_infra.GetDevDetailForDep(ctx, req.MrID, infra.DependencyType_MR)
	if err != nil {
		logs.CtxError(ctx, "GetDevBranchInfo GetDevDetailForDep failed mrID=%d  error=%s", req.MrID, err.Error())
		return nil, err
	}
	if dev == nil || dev.Dev == nil || len(dev.Dev.Hosts) == 0 {
		return nil, bits_err.OPTIMUS.ErrGetDevInfoFail
	}
	//get mr ids
	mrIDs := make([]int64, 0)
	hostMrIDs := make([]int64, 0)
	projectsID := make([]int64, 0)
	for _, hostDep := range dev.Dev.Hosts {
		hostMrIDs = append(hostMrIDs, hostDep.Host.Id)
		for _, dep := range hostDep.Dependencies {
			mrIDs = append(mrIDs, dep.Id)
		}
	}
	mrIDs = append(mrIDs, hostMrIDs...)
	for _, pubDep := range dev.Dev.PublicDependencies {
		mrIDs = append(mrIDs, pubDep.PublicDependency.Id)
	}

	mainMrInfo, err := data.GetMergeRequestInfoByMrID(ctx, hostMrIDs[0], false)
	if err != nil {
		return nil, err
	}
	mrsInfo, err := data.GetMRsInfoInID(ctx, mrIDs, false)
	if err != nil {
		logs.CtxError(ctx, "GetMRsInfoInID failed error:%s", err.Error())
		return nil, err
	}
	hostMrsInfo, err := data.GetMRsInfoInID(ctx, hostMrIDs, false)
	if err != nil {
		logs.CtxError(ctx, "GetMRsInfoInID failed error:%s", err.Error())
		return nil, err
	}
	for _, mrInfo := range hostMrsInfo {
		projectsID = append(projectsID, mrInfo.GetProjectIDNumber(ctx))
	}
	// 查找所有的仓库配置
	projectsInfo, err := data.GetConfigProjectInfoInProjectID(ctx, projectsID)
	if err != nil {
		logs.CtxError(ctx, "GetConfigProjectInfoInProjectID failed error:%s", err.Error())
		return nil, err
	}
	mainGroupsID := make([]int64, 0)
	for _, project := range projectsInfo {
		mainGroupsID = append(mainGroupsID, project.MainGroupConfigID)
	}
	if len(mainGroupsID) == 0 {
		logs.CtxError(ctx, "group not found")
		return nil, errors.New("group not found")
	}
	groupConfigsInfo, err := data.GetConfigGroupInfoInID(ctx, mainGroupsID)
	if err != nil {
		logs.CtxError(ctx, "GetConfigGroupInfoInID failed error:%s", err.Error())
		return nil, err
	}

	canFixConflict := false
	CanSkipReview := false
	answer := &optimus.GetReviewPermissionsInfo{
		CanRetry:      true,
		ShowOk:        false,
		ShowCancelOk:  false,
		Status:        "success",
		CanSkipReview: &CanSkipReview,
	}
	// 是否可以更新
	// 1. open的，有锁的不能更新
	canBeUpdated := merge_request.CanBeUpdated(ctx, req.MrID)
	logs.Info("can_be_updated_%d_final_state_%v", req.MrID, canBeUpdated)
	devState, err := basic.GetDevStateByHostMrInfoList(ctx, mrsInfo)
	if err != nil {
		logs.CtxError(ctx, "GetDevStateByHostMrInfoList failed error:%s", err.Error())
		return nil, err
	}
	extra := mainMrInfo.GetExtra()
	if extra != nil {
		if hasAppTaskAdvancedPermission(ctx, groupConfigsInfo, req.GetActionUsername()) {
			if devState == model.MR_STATE_OPENED {
				answer.CanClose = true
				answer.CanMergeBase = canBeUpdated
				answer.CanForceMerge = canBeUpdated
			}
			if canBeUpdated {
				if extra.Superior {
					answer.CanDowngradeMr = true
					answer.CanUpgradeMr = false
				} else {
					answer.CanDowngradeMr = false
					answer.CanUpgradeMr = true
				}
			}
		}
	}
	// WIP状态的 mr 隐藏 force merge
	if mainMrInfo.WorkInProgress > 0 {
		answer.CanForceMerge = false
	}
	// can skip review
	mainGroupConfig, _ := data.GetConfigGroupInfoByProjectIDRaw(ctx, mainMrInfo.ProjectID, true)
	if mainGroupConfig != nil && mainGroupConfig.SkipReviewEnable {
		answer.CanSkipReview = &answer.CanForceMerge
	}
	// 判断主仓的author_name是不是自己
	if mainMrInfo.AuthorName == req.ActionUsername {
		if devState == model.MR_STATE_OPENED {
			answer.CanClose = true
		}
		answer.CanMergeBase = canBeUpdated
	}
	// TODO 只有抖音android有这个逻辑, 抖音ios主仓有冲突,且是超级MR
	if (mainMrInfo.ProjectID == "114467" || mainMrInfo.ProjectID == "1142") && mainMrInfo.Conflicted == consts.OPTIMUS_CONFLICTED_CONFLICTED {
		if extra != nil && extra.Superior {
			canFixConflict = true
		}
	}
	// 如果是ios && 有冲突 && 超级MR
	pID, _ := strconv.ParseInt(mainMrInfo.ProjectID, 10, 64)
	groupConfig, _ := data.GetConfigGroupInfoByProjectID(ctx, pID, true)
	// 如果是iOS
	if groupConfig != nil && groupConfig.AppID == 1 {
		// 查找所有的子仓是否有冲突
		hasConflict := false
		for _, mrInfo := range mrsInfo {
			if mrInfo != nil && mrInfo.Conflicted == consts.OPTIMUS_CONFLICTED_CONFLICTED {
				hasConflict = true
			}
		}
		if hasConflict {
			canFixConflict = true
		}
	}
	projectConfig, err := data.GetConfigProjectInfoByProjectID(ctx, pID, true)
	if err != nil {
		logs.CtxError(ctx, "GetPermissionsInfo GetConfigProjectInfoByProjectID failed projectID:%d error:%s", pID, err.Error())
	}
	//Android 单主仓 merge 类型的mr，主仓或者子仓有版本冲突，可以用zhangxiao提供的脚本解决,仅限mr创建者触发
	//当前暂时在主仓有冲突时才显示
	if projectConfig != nil && projectConfig.AppType == int16(consts.ProjectApp_Android) {
		if mainMrInfo.OptimusMrID == 0 && mainMrInfo.MrType == model.MR_TYPE_MERGE && mainMrInfo.AuthorName == req.ActionUsername {
			if mainMrInfo.Conflicted == consts.OPTIMUS_CONFLICTED_CONFLICTED {
				canFixConflict = true
			}
		}
	}
	//tcc强制关闭功能
	disabledMap, err := tcc.GetMrDetailOperationDisabledMap(ctx)
	if err == nil {
		if disabledMap["disable_auto_resolve_conflict"] == true {
			canFixConflict = false
		}
	}
	//白名单用户开启功能
	userList, _ := tcc.GetMrDetailOperationUserWhiteList(ctx)
	if userList != nil && len(userList) > 0 {
		if utils2.Contain(userList, req.ActionUsername) {
			canFixConflict = true
		}
	}
	answer.CanFixConflict = &canFixConflict
	answer.Status = "success"

	extraForceMergeAdmins := make([]string, 0)
	if groupConfig != nil {
		groupCommonConfig, _ := rpc.ConfigClient.GetGroupCommonConfig(ctx, &config_service.GetGroupCommonConfigRequest{
			GroupName: groupConfig.Name,
		})
		if groupCommonConfig != nil {
			config := &commonConfig{}
			_ = json.Unmarshal([]byte(groupCommonConfig.Config), config)
			if config != nil {
				for _, cfg := range config.BranchForceMergeConfig {
					matched, _ := regexp.MatchString(cfg.Branch, mainMrInfo.TargetBranch)
					if matched {
						extraForceMergeAdmins = append(extraForceMergeAdmins, cfg.ForceMergeAdmins...)
					}
				}
			}
		}
	}

	answer.ExtraForceMergeAdmins = extraForceMergeAdmins
	return &optimus.GetReviewPermissionsResponse{
		Info:     answer,
		BaseResp: nil,
	}, nil
}

// 获取一个 review 人是不是能删除及其原因
func GetCommonReason(ctx context.Context, req *optimus.GetMrReviewerCommonReasonsQuery) (*optimus.GetMrReviewerCommonReasonsResponse, error) {

	// 获取 mr 与 reviewer 及其 reason
	user, _ := data.GetReviewerByID(ctx, req.ReviewId)
	if user == nil {
		return &optimus.GetMrReviewerCommonReasonsResponse{
			Reasons:             nil,
			IrrRemovableReasons: nil,
			BaseResp:            nil,
		}, nil
	}

	mr, _ := data.GetMergeRequestInfoByMrID(ctx, user.MergeRequestID, true)
	reasons := user.GetReasons()

	// 通用 reason 信息
	pID, _ := strconv.ParseInt(mr.ProjectID, 10, 64)
	project, _ := data.GetConfigProjectInfoByProjectID(ctx, pID, true)

	if project == nil {
		return nil, errors.New("project not exist")
	}
	projectName := utils.GetProjectNameByGitRepoURL(project.GitRepoAddr)
	projectGroupName := utils.GetGroupByGitRepoURL(project.GitRepoAddr)
	projectFullName := fmt.Sprintf("%s/%s", projectGroupName, projectName)
	group, _ := data.GetConfigGroupInfoByID(ctx, project.MainGroupConfigID, true)

	isGroupMaintainer, _ := reviewer.GetAllMaintainerBySingleMrID(ctx, req.ActionUsername, mr.ID)
	canRemovable, reasonKey := reviewer.IsRemovable(ctx, user, mr.ID, isGroupMaintainer)

	// 被添加的原因
	commonReasonList := make([]*optimus.ReviewerReasonsContent, 0)
	for _, reason := range reasons {
		reasonContent := &optimus.ReviewerReasonsContent{Key: reason.ReasonKey}
		reasonContent.ProjectName = projectName
		reasonContent.ProjectFullName = projectFullName
		commonReasonList = append(commonReasonList, reasonContent)
		if reason.ReasonKey == consts.REVIEW_REASON_KEY_USER_ADD_RD ||
			reason.ReasonKey == consts.REVIEW_REASON_KEY_USER_ADD_QA {
			operators := make([]string, 0)
			for _, detail := range reason.DetailReason {
				operators = append(operators, detail)
			}
			reasonContent.Operator = operators
		}

		if len(reason.CustomReason) > 0 {
			customReason := set.NewStringSet()
			for _, val := range reason.CustomReason {
				customReason.AddAll(val...)
			}
			reasonContent.CustomizedReason = customReason.ToList()
		}

		if reason.ReasonKey == consts.REVIEW_REASON_KEY_BRANCH_REVIEWER {
			reasonContent.Branch = reason.DetailReason
		}
		if reason.ReasonKey == consts.REVIEW_REASON_KEY_FILE_REVIEWER {
			reasonContent.Files = reason.DetailReason
		}
	}

	// 不可删除的原因
	removableReasonList := make([]*optimus.ReviewerReasonsContent, 0)
	if !canRemovable {
		reasonContent := &optimus.ReviewerReasonsContent{Key: reasonKey}
		removableReasonList = append(removableReasonList, reasonContent)
		reasonContent.ProjectName = projectName
		reasonContent.ProjectFullName = projectFullName
		reasonContent.ReviewerNum = project.ReviewerNumber
		reasonContent.ReviewerNumUnderAdmin = ternary.TernaryInt64(project.ReviewerNumber > 1, 1, project.ReviewerNumber)

		rdbmRequiredNum := int64(0)
		if group.GetBmReviewConfig() != nil && group.GetBmReviewConfig().RdBm != nil {
			rdbmRequiredNum = group.GetBmReviewConfig().RdBm.MinNum
		}
		qabmRequiredNum := int64(0)
		if group.GetBmReviewConfig() != nil && group.GetBmReviewConfig().QaBm != nil {
			qabmRequiredNum = group.GetBmReviewConfig().QaBm.MinNum
		}
		reasonContent.RdBmNum = &rdbmRequiredNum
		reasonContent.QaBmNum = &qabmRequiredNum
		if len(user.ReasonObj) > 0 {
			for _, item := range user.ReasonObj {

				if set.NewStringSet(
					consts.REVIEW_REASON_KEY_USER_ADD_RD,
					consts.REVIEW_REASON_KEY_USER_ADD_QA,
					consts.REVIEW_REASON_KEY_USER_ADD_RD_BM,
					consts.REVIEW_REASON_KEY_USER_ADD_QA_BM,
				).Contains(item.ReasonKey) {
					for _, operator := range item.DetailReason {
						reasonContent.Operator = append(reasonContent.Operator, operator)
					}
				}
			}
		}
	}

	// 返回值
	return &optimus.GetMrReviewerCommonReasonsResponse{
		Reasons:             commonReasonList,
		IrrRemovableReasons: removableReasonList,
		CanRemove:           canRemovable,
		BaseResp:            nil,
	}, nil
}
