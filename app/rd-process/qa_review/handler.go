package main

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler/comment"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler/compatibility"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler/external"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler/qaer"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/handler/test"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/service/business"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/biz/service/statics"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/qa_review/kitex_gen/bytedance/bits/qa_review"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs"
)

type QaReviewServiceImpl struct {
}

func (s *QaReviewServiceImpl) SyncRule(ctx context.Context, req *qa_review.SyncRuleRequest) (r *base.EmptyResponse, err error) {
	r = base.NewEmptyResponse()
	err = handler.SyncRule(ctx, req)
	return
}

func (s *QaReviewServiceImpl) CollectStatics(ctx context.Context, req *base.EmptyRequest) (r *base.EmptyResponse, err error) {
	r = base.NewEmptyResponse()
	statics.CollectStatics(ctx)
	return
}

func (s *QaReviewServiceImpl) GetMrIDListFilterByNameAndState(ctx context.Context, req *qa_review.GetMrIDListFilterByNameAndStateReq) (r *qa_review.GetMrIDListFilterByNameAndStateResp, err error) {
	return external.GetMRListWithNameAndStatus(ctx, req)
}

// 永远返回 true
func (s *QaReviewServiceImpl) CheckEnable(ctx context.Context, req *qa_review.CheckEnableRequest) (r *qa_review.CheckEnableResponse, err error) {
	return handler.CheckEnable(ctx, req)
}

func (s *QaReviewServiceImpl) AddQaReviewerFromCR(ctx context.Context, req *qa_review.AddQaReviewerFromCrRequest) (r *base.EmptyResponse, err error) {
	logs.CtxInfo(ctx, "AddQaReviewerFromCR req: %s", utils.ToJson(req))
	r = base.NewEmptyResponse()
	err = compatibility.AddQaReviewer(ctx, req)
	return
}

func (s *QaReviewServiceImpl) GetQaStatus(ctx context.Context, req *qa_review.GetQaStatusRequest) (r *qa_review.GetQaStatusResponse, err error) {
	return compatibility.GetQaStatus(ctx, req)
}

func (s *QaReviewServiceImpl) BatchGetQaStatus(ctx context.Context, req *qa_review.BatchGetQaStatusRequest) (r *qa_review.BatchGetQaStatusResponse, err error) {
	return compatibility.BatchGetQaStatus(ctx, req)
}

func (s *QaReviewServiceImpl) StartQaProcess(ctx context.Context, req *qa_review.StartQaProcessRequest) (r *base.EmptyResponse, err error) {
	r = base.NewEmptyResponse()
	err = test.StartQa(ctx, req)
	return
}

func (s *QaReviewServiceImpl) GetMatchedTestRule(ctx context.Context, req *qa_review.GetMatchedTestRuleRequest) (resp *qa_review.GetMatchedTestRuleResponse, err error) {
	return business.MatchRuleWithReq(ctx, req)
}

func (s *QaReviewServiceImpl) PreCheck(ctx context.Context, req *qa_review.PreCheckRequest) (r *qa_review.PreCheckResponse, err error) {
	return handler.Check(ctx, req)
}

func (s *QaReviewServiceImpl) QaComment(ctx context.Context, req *qa_review.QaCommentRequest) (r *base.EmptyResponse, err error) {
	r = base.NewEmptyResponse()
	err = comment.QaComment(ctx, req)
	return
}

// 添加 QA Review 人
// AddQaTester implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) AddQaTester(ctx context.Context, req *qa_review.AddQaTesterRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = qaer.AddQaTester(ctx, req)
	return
}

// 删除 QA Review 人
// RemoveQaTester implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) RemoveQaTester(ctx context.Context, req *qa_review.RemoveQaTesterRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = qaer.RemoveQaTester(ctx, req)
	return
}

// ReplaceQaTester implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) ReplaceQaTester(ctx context.Context, req *qa_review.ReplaceQaTesterRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = qaer.ReplaceQaTester(ctx, req)
	return
}

// GetAllQaTesters implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) GetAllQaTesters(ctx context.Context, req *qa_review.GetAllQaTestersRequest) (resp *qa_review.GetAllQaTestersResponse, err error) {
	return qaer.GetQaTesters(ctx, req)
}

// NotifyQaTesters implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) NotifyQaTesters(ctx context.Context, req *qa_review.NotifyQaTestersRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = qaer.Notify(ctx, req)
	return
}

// UpdateHostStatus implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) UpdateHostStatus(ctx context.Context, req *qa_review.UpdateHostStatusRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.UpdateHostStatus(ctx, req)
	return
}

// 手动在 mr 详情页面点击发起 QA Review
// ApplyTest implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) ApplyTest(ctx context.Context, req *qa_review.ApplyTestRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.ApplyTest(ctx, req)
	return
}

// SubmitSkipApproval implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) SubmitSkipApproval(ctx context.Context, req *qa_review.SubmitSkipApprovalRequest) (resp *qa_review.SubmitSkipApprovalResponse, err error) {
	return test.SubmitApproval(ctx, req)
}

// DoSkipPrecheckApproval implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) DoSkipPrecheckApproval(ctx context.Context, req *qa_review.DoSkipPrecheckApprovalRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.DoApprove(ctx, req)
	return
}

// Approve implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) Approve(ctx context.Context, req *qa_review.ApproveRequest) (resp *base.EmptyResponse, err error) {
	logs.CtxInfo(ctx, "Approve req: %s", utils.ToJson(req))
	resp = base.NewEmptyResponse()
	err = test.Approve(ctx, req)
	return
}

// Reject implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) Reject(ctx context.Context, req *qa_review.RejectRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.Reject(ctx, req)
	return
}

// Cancel implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) Cancel(ctx context.Context, req *qa_review.CancelRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.Cancel(ctx, req)
	return
}

// douyin.tech.optimus_server_engine 状态机获取 QA Review 是否通过
// GetStatus implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) GetStatus(ctx context.Context, req *qa_review.GetStatusRequest) (resp *qa_review.GetStatusResponse, err error) {
	logs.CtxInfo(ctx, "GetStatus req: %s", utils.ToJson(req))
	startTime := time.Now().Unix()
	resp, err = test.GetStatus(ctx, req)
	if err != nil {
		errStr := err.Error()
		resp.SetMessage(&errStr)
	}
	resp.SetStartTime(startTime)
	resp.SetEndTime(time.Now().Unix())
	return
}

// GetHostTestInfo implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) GetHostTestInfo(ctx context.Context, req *qa_review.GetHostTestInfoRequest) (resp *qa_review.GetHostTestInfoResponse, err error) {
	return test.GetHostTestInfo(ctx, req)
}

// EditTestingPackage implements the EditTestingPackage interface.
func (s *QaReviewServiceImpl) EditTestingPackage(ctx context.Context, req *qa_review.EditTestingPackageRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = test.EditTestingPackage(ctx, req)
	return
}

// RemindDevQaTesters implements the QaReviewServiceImpl interface.
func (s *QaReviewServiceImpl) RemindDevQaTesters(ctx context.Context, req *qa_review.RemindDevQaTestersRequest) (resp *base.EmptyResponse, err error) {
	resp = base.NewEmptyResponse()
	err = qaer.RemindDevQaTesters(ctx, req)
	return
}
