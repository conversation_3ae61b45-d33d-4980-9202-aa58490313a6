package model

type CustomDeployTaskConfig struct {
	Enabled      bool                 `json:"enabled"`
	TaskName     string               `json:"task_name"`
	StartURL     string               `json:"start_url"`
	StartHeaders map[string]string    `json:"start_headers"`
	StartMesh    CustomDeployTaskMesh `json:"start_mesh"`
	PingURL      string               `json:"ping_url"`
	PingHeaders  map[string]string    `json:"ping_headers"`
	PingMesh     CustomDeployTaskMesh `json:"ping_mesh"`
	ResetURL     string               `json:"reset_url"`
	ResetHeaders map[string]string    `json:"reset_headers"`
	ResetMesh    CustomDeployTaskMesh `json:"reset_mesh"`
	CloseURL     string               `json:"close_url"`
	CloseHeaders map[string]string    `json:"close_headers"`
	CloseMesh    CustomDeployTaskMesh `json:"close_mesh"`
}

type CustomDeployTaskMesh struct {
	Enabled  bool   `json:"enabled"`
	IDC      string `json:"idc"`
	Protocol string `json:"protocol"`
	Domain   string `json:"domain"`
	URL      string `json:"url"`
}
