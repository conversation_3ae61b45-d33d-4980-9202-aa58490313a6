package model

var (
	PipelineTypeGitlab  int64 = 0
	PipelineTypeJenkins int64 = 1
	PipelineTypeViper   int64 = 2
	PipelineTypeBits    int64 = 3
)

const (
	PIPELINE_BRANCH_TYPE_SHADOW_BRANCH                    = "shadow_branch"                    // 主仓在普通影子分支上跑pipeline
	PIPELINE_BRANCH_TYPE_SHADOW_BRANCH_AFTER_MERGE_TARGET = "shadow_branch_after_merge_target" // 主仓在merge了target分支之后的影子分支上跑pipeline
	PIPELINE_BRANCH_TYPE_SOURCE_BRANCH                    = "source_branch"                    // 主仓直接在source 分支上跑pipeline
)
