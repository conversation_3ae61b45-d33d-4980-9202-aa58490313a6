// Code generated from bits_ddl.sql by cbt. DO NOT EDIT.

package db

import (
	"time"
)

func (PackageBuildTask) TableName() string {
	return "package_build_task"
}

type PackageBuildTask struct {
	AppId         int64     `gorm:"column:app_id" json:"app_id"`
	ConfigType    int64     `gorm:"column:config_type" json:"config_type"`       // 打包配置类型
	Id            int64     `gorm:"column:id;primary_key" json:"id"`             // 主键id
	JobId         int64     `gorm:"column:job_id" json:"job_id"`                 // 实际执行的任务id
	PipelineId    int64     `gorm:"column:pipeline_id" json:"pipeline_id"`       // ppl触发的pipelineID
	ProjectId     int64     `gorm:"column:project_id" json:"project_id"`         // 仓库id
	CallbackUrl   string    `gorm:"column:callback_url" json:"callback_url"`     // 自定义回调URL
	Changelog     string    `gorm:"column:changelog" json:"changelog"`           // 更新日志
	ConfigName    string    `gorm:"column:config_name" json:"config_name"`       // 编译配置名字
	ErrMsg        string    `gorm:"column:err_msg" json:"err_msg"`               // 错误信息
	JobInfo       string    `gorm:"column:job_info" json:"job_info"`             // 打包元信息
	JobUrl        string    `gorm:"column:job_url" json:"job_url"`               // 打包地址
	Params        string    `gorm:"column:params" json:"params"`                 // 打包参数
	ProjectBranch string    `gorm:"column:project_branch" json:"project_branch"` // 打包分支
	ProjectCommit string    `gorm:"column:project_commit" json:"project_commit"` // 打包commit
	ProjectName   string    `gorm:"column:project_name" json:"project_name"`     // 仓库名
	ProjectUrl    string    `gorm:"column:project_url" json:"project_url"`       // 仓库地址
	Status        string    `gorm:"column:status" json:"status"`                 // 任务状态
	Username      string    `gorm:"column:username" json:"username"`             // 用户名
	Ctime         time.Time `gorm:"column:ctime;autoCreateTime" json:"ctime"`    // 创建时间 tags:{"gorm":"column:ctime;autoCreateTime"}
	Mtime         time.Time `gorm:"column:mtime;autoUpdateTime" json:"mtime"`    // 更新时间 tags:{"gorm":"column:mtime;autoUpdateTime"}
}

func NewPackageBuildTask() *PackageBuildTask {
	return &PackageBuildTask{}
}

func (PackageBuildArtifact) TableName() string {
	return "package_build_artifact"
}

type PackageBuildArtifact struct {
	Id          int64     `gorm:"column:id;primary_key" json:"id"`          // 自增id
	TaskId      int64     `gorm:"column:task_id" json:"task_id"`            // 打包任务id
	ExtraInfo   string    `gorm:"column:extra_info" json:"extra_info"`      // 额外信息
	InstallUrl  string    `gorm:"column:install_url" json:"install_url"`    // 包安装地址
	PackageName string    `gorm:"column:package_name" json:"package_name"`  // 包名
	PackageUrl  string    `gorm:"column:package_url" json:"package_url"`    // 包地址
	Ctime       time.Time `gorm:"column:ctime;autoCreateTime" json:"ctime"` // 创建时间 tags:{"gorm":"column:ctime;autoCreateTime"}
	Mtime       time.Time `gorm:"column:mtime;autoUpdateTime" json:"mtime"` // 更新时间 tags:{"gorm":"column:mtime;autoUpdateTime"}
}

func NewPackageBuildArtifact() *PackageBuildArtifact {
	return &PackageBuildArtifact{}
}

func (PackageBuildExtraArtifact) TableName() string {
	return "package_build_extra_artifact"
}

type PackageBuildExtraArtifact struct {
	Id        int64     `gorm:"column:id;primary_key" json:"id"`          // id
	PackageId int64     `gorm:"column:package_id" json:"package_id"`      // 包产物id
	ExtraInfo string    `gorm:"column:extra_info" json:"extra_info"`      // 额外信息
	Name      string    `gorm:"column:name" json:"name"`                  // 产物名
	Type      string    `gorm:"column:type" json:"type"`                  // 类型
	Url       string    `gorm:"column:url" json:"url"`                    // 产物地址
	Ctime     time.Time `gorm:"column:ctime;autoCreateTime" json:"ctime"` // 创建时间 tags:{"gorm":"column:ctime;autoCreateTime"}
	Mtime     time.Time `gorm:"column:mtime;autoUpdateTime" json:"mtime"` // 更新时间 tags:{"gorm":"column:mtime;autoUpdateTime"}
}

func NewPackageBuildExtraArtifact() *PackageBuildExtraArtifact {
	return &PackageBuildExtraArtifact{}
}

func (PackagePlistInfo) TableName() string {
	return "package_plist_info"
}

type PackagePlistInfo struct {
	Id            int64     `gorm:"column:id;primary_key" json:"id"`               // 主键id
	BundleId      string    `gorm:"column:bundle_id" json:"bundle_id"`             // bundle id
	PackageName   string    `gorm:"column:package_name" json:"package_name"`       // 包名
	PackageUrl    string    `gorm:"column:package_url" json:"package_url"`         // 包地址
	PackageUrlKey string    `gorm:"column:package_url_key" json:"package_url_key"` // 包地址md5 key
	Ctime         time.Time `gorm:"column:ctime" json:"ctime"`                     // 创建时间
}

func NewPackagePlistInfo() *PackagePlistInfo {
	return &PackagePlistInfo{}
}
