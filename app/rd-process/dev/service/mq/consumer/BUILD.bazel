load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "consumer",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/consumer",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/pkg/config",
        "//app/rd-process/dev/pkg/dal/mysql/data",
        "//app/rd-process/dev/pkg/handler/mq",
        "//app/rd-process/dev/pkg/handler/mq/devtask",
        "//app/rd-process/dev/pkg/handler/small_mr_v2",
        "//libs/common_lib/utils",
        "//libs/events",
        "@org_byted_code_eventbus_client_go//:client-go",
        "@org_byted_code_eventbus_client_go//legacy",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/config",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/consumer",
    ],
)
