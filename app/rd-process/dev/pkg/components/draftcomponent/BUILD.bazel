load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "draftcomponent",
    srcs = ["component.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/draftcomponent",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/kitex_gen/bits/integration/multi",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/pkg/common/consts",
        "//app/rd-process/dev/pkg/common/relations",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/dal/mysql/repository",
        "//app/rd-process/dev/pkg/deps/configcenter",
        "//app/rd-process/dev/pkg/deps/integrationsvr",
        "//app/rd-process/dev/pkg/deps/releaseticket",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/codebase:codebase_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
    ],
)
