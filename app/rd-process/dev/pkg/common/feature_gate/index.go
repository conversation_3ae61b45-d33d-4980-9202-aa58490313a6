package feature_gate

import (
	"context"
	"strconv"

	"code.byted.org/devinfra/bytegate_sdk/models"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/third_part"
	"code.byted.org/gopkg/logs/v2/log"
)

func IfUseNewConflictDetect(ctx context.Context, spaceID int64) bool {
	answer, err := third_part.DevopsFg.Get(ctx, FeatureKeyIfSpaceUseNewSmrConflictDetect, &models.ByteGateConfig{
		Custom: map[string]string{
			"space_id": strconv.FormatInt(spaceID, 10),
		},
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}

func IfUseWIPOptimusMR(ctx context.Context, spaceID int64) bool {
	answer, err := third_part.DevopsClientFg.Get(ctx, FeatureKeyIfSpaceUseWIPOptimusMR, &models.ByteGateConfig{
		Custom: map[string]string{
			"spaceId": strconv.FormatInt(spaceID, 10),
		},
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}

func IfUseDevTaskCodeFrozen(ctx context.Context, spaceID int64) bool {
	answer, err := third_part.DevopsClientFg.Get(ctx, FeatureKeyIfSpaceUseDevTaskCodeFrozen, &models.ByteGateConfig{
		Custom: map[string]string{
			"spaceId": strconv.FormatInt(spaceID, 10),
		},
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}

func IfSpaceUseMergeMRWhenComplete(ctx context.Context, spaceID int64) bool {
	answer, err := third_part.DevopsClientFg.Get(ctx, FeatureKeyIfSpaceUseMergeMRWhenComplete, &models.ByteGateConfig{
		Custom: map[string]string{
			"spaceId": strconv.FormatInt(spaceID, 10),
		},
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}

func IfDevTaskOverviewEnabled(ctx context.Context, spaceID int64) bool {
	answer, err := third_part.DevopsFg.Get(ctx, FeatureKeyIfDevTaskOverviewEnabled, &models.ByteGateConfig{
		Custom: map[string]string{
			"space_id": strconv.FormatInt(spaceID, 10),
		},
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}

func IfUseDevTaskNewStage(ctx context.Context, spaceID int64, username string) bool {
	answer, err := third_part.DevopsClientFg.Get(ctx, FeatureKeyIfSpaceUseNewStage, &models.ByteGateConfig{
		Custom: map[string]string{
			"spaceId": strconv.FormatInt(spaceID, 10),
		},
		User: username,
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return false
	}
	return answer
}
