package devtaskrelated

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
)

func BindTagOnDevTasks(ctx context.Context, devBasicIds []int64, tagId int64) error {
	// 查询 devId
	infos, err := data.OptimusDB.Slave.BatchGetDevBasicInfoByIds(ctx, devBasicIds).Get()
	if err != nil {
		return err
	}
	if len(infos) == 0 {
		return errors.New("dev task not found")
	}
	devIds := gslice.Uniq(gslice.Map(infos, func(f *model.DevBasicInfo) int64 { return f.DevId }))

	// 查询已有 Tag | 避免重复创建
	tagRepository := data.NewDevTagsRepository(data.OptimusDB.Master.GetDB(ctx))
	existTags, err := tagRepository.ListByDevIds(ctx, devIds).Get()
	if err != nil {
		return err
	}
	var devIdTagIdM = make(map[int64][]int64)
	for _, tag := range existTags {
		val := gmap.Load(devIdTagIdM, tag.DevId).ValueOr([]int64{})
		devIdTagIdM[tag.DevId] = append(val, tag.TagId)
	}

	// 构造需要插入的记录
	var toCreate = make([]*model.DevTags, 0)
	for _, devId := range devIds {
		val, ok := devIdTagIdM[devId]
		if ok && gslice.Contains(val, tagId) {
			continue
		}
		toCreate = append(toCreate, &model.DevTags{
			DevId: devId,
			TagId: tagId,
		})
	}

	// 插入记录
	return tagRepository.BatchCreate(ctx, toCreate, 100)
}
