load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "devchange",
    srcs = [
        "biz_config.go",
        "code.go",
        "factory.go",
        "service.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/devtask/devchange",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/kitex_gen/bits/integration/multi",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/config_service",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/feature",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/git_server",
        "//app/rd-process/dev/pkg/common/derr",
        "//app/rd-process/dev/pkg/common/functools",
        "//app/rd-process/dev/pkg/dal/mysql/data",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/deps/configcenter",
        "//app/rd-process/dev/pkg/deps/gitsvr",
        "//app/rd-process/dev/pkg/service/devtask/adaptor",
        "//app/rd-process/dev/pkg/service/devtask/devtaskmodel",
        "//libs/bits_err",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_lang_gg//gvalue",
        "@org_byted_code_lang_gg//stdwrap/syncwrap",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "devchange_test",
    srcs = ["code_test.go"],
    embed = [":devchange"],
    deps = [
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/git_server",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/deps/gitsvr",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
    ],
)
