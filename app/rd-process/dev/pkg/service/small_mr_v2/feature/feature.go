package feature

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/feature"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"context"
)

func SyncDevFeatureToOptimusMr(ctx context.Context, DevID, optimusMrID int64) error {
	devFeatureResp, err := rpc.FeatureClient.GetDevFeatureRecord(ctx, &feature.GetDevFeatureRecordQuery{DevID: DevID})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return err
	}
	if len(devFeatureResp.List) > 0 {
		for _, record := range devFeatureResp.List {
			_, err := rpc.FeatureClient.FeatureBindMr(ctx, &feature.FeatureBindMrQuery{
				TaskID:          record.TaskID,
				Platform:        record.Platform,
				TaskType:        record.TaskType,
				MrID:            optimusMrID,
				Username:        record.Username,
				GroupName:       record.GroupName,
				TaskTitle:       record.TaskTitle,
				FeatureConfigID: record.FeatureConfigID,
				Config:          record.Config,
				CustomContent:   record.CustomContent,
				DevID:           DevID,
			})
			if err != nil {
				logs.CtxError(ctx, err.Error())
			}
		}
	}
	return nil
}
