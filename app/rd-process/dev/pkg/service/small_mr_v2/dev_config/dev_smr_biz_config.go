package dev_config

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gresult"
	"context"
	"github.com/bytedance/sonic"
)

type BizConfigHandler struct {
	BizConfig string
	NeedSave  bool
}

func (h *BizConfigHandler) ConfigStr() string {
	return h.BizConfig
}

func (h *BizConfigHandler) GetConfigMap(ctx context.Context) gresult.R[map[string]interface{}] {
	answer := make(map[string]interface{}, 0)
	if err := sonic.UnmarshalString(h.BizConfig, &answer); err != nil {
		logs.CtxError(ctx, "get dev smr biz config failed error = %s", err.Error())
		return gresult.Err[map[string]interface{}](err)
	}
	return gresult.OK(answer)
}

type DevBizConfig struct {
	CustomPipelineEnv      map[string]interface{} `json:"custom_pipeline_env"`
	MergeTaskOptimusMrId   int64                  `json:"merge_task_optimus_mr_id"`
	MergeOption            string                 `json:"merge_option"`
	MergeTaskMode          string                 `json:"merge_task_mode"`
	MergeTaskTargetBranch  string                 `json:"merge_task_target_branch"`
	LarkGroupID            string                 `json:"lark_group_id"`
	RemoveSourceBranch     bool                   `json:"remove_source_branch"`
	PartialMergeEnabled    bool                   `json:"partial_merge_enabled"`
	StartReviewManually    bool                   `json:"start_review_manually"`
	PartialMergeTaskType   string                 `json:"partial_merge_task_type"`
	FeatureTrunkReviewMode int64                  `json:"feature_trunk_review_mode"`
	ChangeAutoMergeEnabled bool                   `json:"change_auto_merge_enabled"`
	FeatureGateIDs         []int64                `json:"feature_gate_ids"`
	WIPMax                 int64                  `json:"wip_max"`
	ChangeCreateDefaultWIP bool                   `json:"change_create_default_wip"`
}

type DevBizConfigHandler struct {
	BizConfigHandler
}

func NewDevBizConfigHandler(str string) *DevBizConfigHandler {
	return &DevBizConfigHandler{
		BizConfigHandler: BizConfigHandler{
			BizConfig: str,
		},
	}
}

func (h *DevBizConfigHandler) GetConfig(ctx context.Context) gresult.R[*DevBizConfig] {
	answer := &DevBizConfig{}
	if len(h.BizConfig) > 0 && h.BizConfig != "null" {
		if err := sonic.UnmarshalString(h.BizConfig, answer); err != nil {
			logs.CtxError(ctx, "get dev smr biz config failed error = %s", err.Error())
			return gresult.Err[*DevBizConfig](err)
		}
	}
	return gresult.OK(answer)
}
