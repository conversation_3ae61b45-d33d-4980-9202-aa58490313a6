package gitsvr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"context"
	"errors"
)

func GetFileContent(ctx context.Context, projectID int64, ref string, filePath string) gresult.R[[]byte] {
	response, err := rpc.GitServerClient.GetFileContent(ctx, &git_server.GetFileContentRequest{
		ProjectId: gptr.Of(int32(projectID)),
		FilePath:  filePath,
		Ref:       ref,
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return gresult.Err[[]byte](err)
	}
	if baseResp := response.GetBaseResp(); baseResp != nil && baseResp.StatusCode != 0 {
		log.V2.Error().With(ctx).Str(baseResp.StatusMessage).Emit()
		return gresult.Err[[]byte](errors.New(baseResp.StatusMessage))
	}
	return gresult.OK(response.GetBody())
}
