package changedomain

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/track"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/rdtasks"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/mapper"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/developmenttask/devoperation"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/developmenttask/query"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/devtask/devchange"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"gorm.io/gorm"
)

type Domain struct {
	operationService *devoperation.Service
	queryService     *query.Service
	infoRepository   *repository.DevBasicInfoRepository
}

func NewDomain(db *gorm.DB, rdb *goredis.Client) *Domain {
	return &Domain{
		operationService: devoperation.NewService(db, rdb),
		queryService:     query.NewService(db),
		infoRepository:   repository.NewDevBasicInfoRepository(db),
	}
}

func (d Domain) ReopenCodeChanges(ctx context.Context, req *dev.ReopenCodeChangesRequest) gresult.R[*dev.ReopenCodeChangesResponse] {
	if err := verifyReopenCodeChangesRequest(req); err != nil {
		return gresult.Err[*dev.ReopenCodeChangesResponse](err)
	}

	// reopen mr
	params := DevReopenCodeChangesRequest2GitSvrReopenMrParams(req)
	err := devchange.ReopenCodeChanges(ctx, params)
	if err != nil {
		return gresult.Err[*dev.ReopenCodeChangesResponse](err)
	}

	// 通知 tasks | 保证合入列表的状态及时更新
	changeIds := gslice.Map(req.GetItems(), func(f *dev.ReopenCodeChangeItem) int64 { return f.GetChangeId() })
	err = rdtasks.UpdateCodeChangeStatus(ctx, req.GetDevBasicId(), changeIds)
	if err != nil {
		return gresult.Err[*dev.ReopenCodeChangesResponse](err)
	}

	return gresult.OK(dev.NewReopenCodeChangesResponse())
}

func verifyReopenCodeChangesRequest(req *dev.ReopenCodeChangesRequest) error {
	if req.GetDevBasicId() == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId should be greater than zero")
	}
	if len(req.GetUsername()) == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("username is required")
	}
	return nil
}

func (d Domain) MarkChangesMerged(ctx context.Context, req *dev.MarkChangesMergedRequest) gresult.R[*dev.MarkChangesMergedResponse] {
	if err := verifyMarkChangesMergedRequest(req); err != nil {
		return gresult.Err[*dev.MarkChangesMergedResponse](err)
	}

	err := d.operationService.MarkChangesMerged(ctx, req.GetDevBasicId(), req.GetChangeIds())
	if err != nil {
		return gresult.Err[*dev.MarkChangesMergedResponse](err)
	}

	return gresult.OK(dev.NewMarkChangesMergedResponse())
}

func verifyMarkChangesMergedRequest(req *dev.MarkChangesMergedRequest) error {
	if req.GetDevBasicId() == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId should be greater than zero")
	}
	if len(req.GetChangeIds()) == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("changeIds is required")
	}
	return nil
}

func (d Domain) RemoveDevChange(ctx context.Context, req *dev.RemoveDevChangeRequest) gresult.R[*dev.RemoveDevChangeResponse] {
	if err := verifyRemoveDevChangeRequest(req); err != nil {
		return gresult.Err[*dev.RemoveDevChangeResponse](err)
	}

	if err := d.operationService.RemoveDevChange(ctx, req.GetChangeId()); err != nil {
		return gresult.Err[*dev.RemoveDevChangeResponse](err)
	}

	return gresult.OK(&dev.RemoveDevChangeResponse{})
}

func verifyRemoveDevChangeRequest(req *dev.RemoveDevChangeRequest) error {
	if req.GetChangeId() <= 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId should be greater than zero")
	}
	return nil
}

func (d Domain) GetDevChangeList(ctx context.Context, req *dev.GetDevChangeListRequest) gresult.R[*dev.GetDevChangeListResponse] {
	if err := verifyGetDevChangeListRequest(req); err != nil {
		return gresult.Err[*dev.GetDevChangeListResponse](err)
	}

	info, err := d.infoRepository.FindById(ctx, req.GetDevBasicId()).Get()
	if err != nil {
		return gresult.Err[*dev.GetDevChangeListResponse](err)
	}

	changeAndRelations, err := d.queryService.GetChangeAndRelatedInfos(ctx, req.GetDevBasicId(), false).Get()
	if err != nil {
		return gresult.Err[*dev.GetDevChangeListResponse](err)
	}

	changeInfos, err := d.queryService.GetChangeInfos(ctx, changeAndRelations.BasicChanges, req.GetUsername()).Get()
	if err != nil {
		return gresult.Err[*dev.GetDevChangeListResponse](err)
	}
	changeInfoM := gslice.ToMap(changeInfos, func(f *query.ChangeInfo) (int64, *query.ChangeInfo) {
		return f.Change.Id, f
	})

	var changes = make([]*dev.DevChange, 0)
	switch info.WorkflowType {
	case enums.UniqueType_DEV_TASK_ADVANCED.String():
		// incremental
		changes = mapper.ModelBasicChangeAndDeployConfigOnChange2DevChanges(changeAndRelations.BasicChanges, changeAndRelations.ChangeProjects, changeAndRelations.Projects, changeAndRelations.Relations)

	default:
		// normal
		changes = mapper.ModelBasicChangeAndDeployConfig2DevChanges(changeAndRelations.BasicChanges, changeAndRelations.Projects)
	}

	resp := &dev.GetDevChangeListResponse{
		ChangeList: gslice.Map(changes, func(f *dev.DevChange) *dev.DevChangeListItem {
			result := &dev.DevChangeListItem{
				Change:       f,
				DiffCount:    nil,
				CommentCount: 0,
				CheckInfo:    nil,
				ReviewInfo:   nil,
			}
			gmap.Load(changeInfoM, f.Id).IfOK(func(t *query.ChangeInfo) {
				result.DiffCount = t.DiffCount
				result.CommentCount = t.CommentCount
				result.CheckInfo = &dev.ChangeCheckInfo{Status: t.CheckStatus}
				result.ReviewInfo = t.ReviewInfo
			})
			return result
		}),
	}
	return gresult.OK(resp)
}

func verifyGetDevChangeListRequest(req *dev.GetDevChangeListRequest) error {
	if req.GetDevBasicId() == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("dev_basic_id is required")
	}
	return nil
}

func (d Domain) GetDevTaskCodeReviewInfo(ctx context.Context, req *dev.GetDevTaskCodeReviewInfoRequest) gresult.R[*dev.GetDevTaskCodeReviewInfoResponse] {
	if err := verifyGetDevTaskCodeReviewInfoRequest(req); err != nil {
		return gresult.Err[*dev.GetDevTaskCodeReviewInfoResponse](err)
	}

	reviewInfos, err := d.queryService.GetCodeReviewInfoByDevBasicId(ctx, req.GetDevBasicId()).Get()
	if err != nil {
		logs.V2.Error().With(ctx).KV(track.BusinessType_DevTask, req.DevBasicId).KV("error", bits_err.DEVTASK.ErrGetDevTaskCodeReviewInfo).Error(err).Emit()
		return gresult.Err[*dev.GetDevTaskCodeReviewInfoResponse](err)
	}
	resp := &dev.GetDevTaskCodeReviewInfoResponse{
		ReviewInfoList: gslice.Map(reviewInfos, queryChangeCodeReviewInfoToDevTaskCodeReviewInfoItem),
	}
	return gresult.OK(resp)
}

func verifyGetDevTaskCodeReviewInfoRequest(req *dev.GetDevTaskCodeReviewInfoRequest) error {
	if req.GetDevBasicId() == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId is required")
	}
	return nil
}

func (d Domain) RemindDevTaskToReview(ctx context.Context, req *dev.RemindDevTaskToReviewRequest) gresult.R[*dev.RemindDevTaskToReviewResponse] {
	if err := verifyRemindDevTaskToReview(req); err != nil {
		return gresult.Err[*dev.RemindDevTaskToReviewResponse](err)
	}

	chatId, err := d.operationService.RemindDevTaskToReview(ctx, req.GetDevBasicID(), req.GetUnsetChangesCodebaseDraft(), req.GetUsername())
	if err != nil {
		logs.V2.Error().With(ctx).KV(track.BusinessType_DevTask, req.GetDevBasicID()).KV("error", bits_err.DEVTASK.ErrRemindDevTaskToReview).Error(err).Emit()
		return gresult.Err[*dev.RemindDevTaskToReviewResponse](err)
	}
	resp := &dev.RemindDevTaskToReviewResponse{
		LarkGroupId: chatId,
	}
	return gresult.OK(resp)
}

func verifyRemindDevTaskToReview(req *dev.RemindDevTaskToReviewRequest) error {
	if req.GetDevBasicID() <= 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId should be greater than zero")
	}
	return nil
}

func (d Domain) GetDevTaskReviewBasicInfo(ctx context.Context, req *dev.GetDevTaskReviewBasicInfoRequest) gresult.R[*dev.GetDevTaskReviewBasicInfoResponse] {
	if err := verifyGetDevTaskReviewBasicInfo(req); err != nil {
		return gresult.Err[*dev.GetDevTaskReviewBasicInfoResponse](err)
	}

	info, err := d.queryService.GetDevTaskReviewBasicInfo(ctx, req.GetDevBasicId(), req.Reviewer).Get()
	if err != nil {
		logs.V2.Error().With(ctx).KV(track.BusinessType_DevTask, req.GetDevBasicId()).KV("error", bits_err.DEVTASK.ErrGetDevTaskReviewBasicInfo).Error(err).Emit()
		return gresult.Err[*dev.GetDevTaskReviewBasicInfoResponse](err)
	}
	resp := &dev.GetDevTaskReviewBasicInfoResponse{
		ReviewBasicInfo: info,
	}
	return gresult.OK(resp)
}

func verifyGetDevTaskReviewBasicInfo(req *dev.GetDevTaskReviewBasicInfoRequest) error {
	if req.GetDevBasicId() <= 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId should be greater than zero")
	}
	return nil
}
