load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mq",
    srcs = [
        "base.go",
        "init.go",
        "producer.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_review_model/biz/service/mq",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_review_model/biz/self_common",
        "//app/rd-process/code_review_model/biz/service/rpc",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/config",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/producer",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/types",
    ],
)
