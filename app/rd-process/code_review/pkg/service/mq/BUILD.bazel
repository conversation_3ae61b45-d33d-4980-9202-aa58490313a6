load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mq",
    srcs = [
        "code_review.go",
        "init.go",
        "produce.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_review/pkg/service/mq",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_review/pkg/config",
        "//libs/common_lib/utils",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_eventbus_client_go//:client-go",
        "@org_byted_code_eventbus_client_go//legacy",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_overpass_bytedance_bits_optimus//rpc/bytedance_bits_optimus",
    ],
)
