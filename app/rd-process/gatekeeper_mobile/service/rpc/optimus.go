package rpc

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/gatekeeper_mobile/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/lang/gg/gresult"
	"context"
)

/**
conflicted 的含义
    CONFLICTED_UNCHECKED = 0 还没开始检查
    CONFLICTED_CONFLICTED = 1 冲突
    CONFLICTED_NOT_CONFLICTED = 2 不冲突
    CONFLICTED_CHECKING = 3 正在检查中
    CONFLICTED_CHECK_FAILED = 4 云构建 job 失败了

func checkItemStatusToGatekeeperCheckStatus(status string) gatekeeper.CheckStatus {
	switch status {
	case "queueing":
		return gatekeeper.CheckStatus_CheckStatusQueuing
	case "running":
		return gatekeeper.CheckStatus_CheckStatusRunning
	case "success":
		return gatekeeper.CheckStatus_CheckStatusSuccess
	case "failed":
		return gatekeeper.CheckStatus_CheckStatusFailed
	case "skipped":
		return gatekeeper.CheckStatus_CheckStatusSkipped
	case "warning":
		return gatekeeper.CheckStatus_CheckStatusWarning
	case "exception":
		return gatekeeper.CheckStatus_CheckStatusException
	default:
		return gatekeeper.CheckStatus_CheckStatusException
	}
}
*/

type ConflictStatus int64

const (
	ConflictStatusQueueing ConflictStatus = iota
	ConflictStatusFailed
	ConflictStatusSuccess
	ConflictStatusRunning
	ConflictStatusException
)

func (c ConflictStatus) String() string {
	switch c {
	case ConflictStatusQueueing:
		return "queueing"
	case ConflictStatusFailed:
		return "failed"
	case ConflictStatusSuccess:
		return "success"
	case ConflictStatusRunning:
		return "running"
	case ConflictStatusException:
		return "exception"
	default:
		return "queueing"
	}
}

func newConflictStatusToConflictStatus(status int64) ConflictStatus {
	switch status {
	case 0:
		return ConflictStatusQueueing
	case 1:
		return ConflictStatusFailed
	case 2:
		return ConflictStatusSuccess
	case 3:
		return ConflictStatusRunning
	case 4:
		return ConflictStatusException
	default:
		return ConflictStatusQueueing
	}
}

func GetMRConflictStatus(ctx context.Context, mrID int64) gresult.R[ConflictStatus] {
	conflict, err := optimusClient.GetMrConflictStatus(ctx, &optimus.GetMrConflictStatusQuery{
		MrID: mrID,
	})
	if err != nil {
		return gresult.Err[ConflictStatus](err)
	}
	ret := newConflictStatusToConflictStatus(conflict.GetConflicted())
	return gresult.OK(ret)
}
