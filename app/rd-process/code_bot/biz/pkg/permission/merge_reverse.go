package permission

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/branchsync"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/version"
)

func CanVersionMerge(ctx context.Context, group string, source, branch string, username string) bool {
	if !version.IsVitalBranches(ctx, group, branch) {
		return true
	}

	// 是不是临时用户
	if IsTmpUser(group, username) {
		return true
	}

	// 是不是admin
	if IsAdmin(ctx, group, username) {
		return true
	}

	// 是不是常驻BM
	if IsStableBM(ctx, group, username) {
		return true
	}

	// 是不是版本BM
	versionCode, ok := version.GetVersionByBranch(ctx, group, branch)
	if !ok {
		if version.IsDevelopBranch(ctx, group, branch) {
			versionCode = branchsync.CurrentReleaseVersion(ctx, group)
		}
	}

	if IsVersionBM(ctx, versionCode, group, username) {
		return true
	}

	// source 版本BM也可以
	versionCode, ok = version.GetVersionByBranch(ctx, group, source)
	if ok {
		return IsVersionBM(ctx, versionCode, group, username)
	}
	return false
}

func CanBatchVersionMerge(ctx context.Context, group string, branch string, username string) bool {
	if version.IsVersionCode(branch) {
		return IsVersionBM(ctx, branch, group, username)
	}

	if version.IsDevelopBranch(ctx, group, branch) {
		return IsStableBM(ctx, group, username)
	}

	return false
}
