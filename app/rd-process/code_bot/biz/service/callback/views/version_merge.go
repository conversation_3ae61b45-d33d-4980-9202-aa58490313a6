package views

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/observers"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/permission"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/version"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/branchsync"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/callback/command"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/callback/pkg"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/util/tools"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"context"
	"code.byted.org/devinfra/hagrid/libs/lark-kit/bot"
	"strconv"
	"strings"
)

type VersionMergeHandler struct {
	pkg.BaseHandler
}

func (v *VersionMergeHandler) match(ackCtx *pkg.Context) bool {
	lowerText := strings.ToLower(ackCtx.Text)
	if tools.GetQuestionType(lowerText) != tools.QuestionTypeNone {
		return false
	}

	if tools.IsContainsAll(lowerText, []string{"merge", "into"}) || gslice.Contains([]string{"合入", "合到", "合进", "合并"}, lowerText) {
		return true
	}
	return false
}

func (v *VersionMergeHandler) Do(ctx context.Context, ackCtx *pkg.Context) pkg.ChainStatus {
	if !v.match(ackCtx) {
		return pkg.ChainStatusContinue
	}

	v.process(ctx, ackCtx)
	return pkg.ChainStatusStopped
}

func (v *VersionMergeHandler) process(ctx context.Context, ackCtx *pkg.Context) {
	text := ackCtx.LowerText
	// 去掉@人的字符串，避免误触发下面的逻辑 --- "\u003cat user_id=\"6482554950831309069\" open_id=\"ou_e3cb5a420f0eb17193d237aa855a1613\"\u003e@张宇\u003c/at\u003e 等这个MR合进去之后的下一个MR"
	command.ExtractUsersFromText(&text)
	source, escapeSource := tools.ExtractVersionOrBranch(&text)
	target, escapeTarget := tools.ExtractVersionOrBranch(&text)
	normal := strings.HasSuffix(strings.TrimSpace(text), "normally")
	if len(source) == 0 || len(target) == 0 {
		ackCtx.Response = pkg.NewResponse(category, "version.merge.hint.lack").Reply()
		return
	}

	if !ackCtx.HasGroupName() {
		ackCtx.Response = pkg.NewResponse(category, "version.merge.hint").Reply()
		return
	}

	if !escapeSource {
		source = version.ConvertToRealBranch(ctx, source, ackCtx.GroupName)
	}
	if !escapeTarget {
		target = version.ConvertToRealBranch(ctx, target, ackCtx.GroupName)
	}
	if !permission.CanVersionMerge(ctx, ackCtx.GroupName, source, target, ackCtx.Username) {
		ackCtx.Response = pkg.NewResponse(category, "version.merge.hint.permission").Reply()
		return
	}

	req := doVersionMergeRequest{
		GroupName:    ackCtx.GroupName,
		UserName:     ackCtx.Username,
		Source:       source,
		Target:       target,
		ChatID:       ackCtx.OpenChatId,
		SourceEscape: escapeSource,
		TargetEscape: escapeTarget,
		Normal:       normal,
		TriggerBy:    branchsync.TriggerByManual,
	}

	btnConfirm := pkg.NewButtonBuilder(ackCtx, observers.BtnVersionMergeConfirm).SetValidUsers(ackCtx.Username).SetReq(req).Build()
	btnCancel := pkg.NewButtonBuilder(ackCtx, observers.BtnCommonCancel).SetValidUsers(ackCtx.Username).Build()
	ackCtx.Response = pkg.NewResponse(category, "version.merge.confirm").
		SetParam("group", ackCtx.GroupName).
		SetParam("source", source).
		SetParam("target", target).
		SetBtnVal("confirm_val", btnConfirm).
		SetBtnVal("cancel_val", btnCancel)
	return
}

func doVersionMerge(ctx context.Context, parser *bot.ValueParser) {
	var req = new(doVersionMergeRequest)
	err := pkg.GetCustomReqFromParser(parser, req)
	if err != nil {
		logs.CtxError(ctx, "get doVersionMergeRequest err, %v", err)
		return
	}

	cb, err := parser.GetBot(ctx)
	if err != nil {
		return
	}
	versionMerge(ctx, req, cb)
}

func versionMerge(ctx context.Context, req *doVersionMergeRequest, cb *bot.ChatBot) {
	pkg.NewResponse(category, "version.merge.wait").
		Reply().
		SetOpenChatID(req.ChatID).
		SendWithChatBot(ctx, cb)

	triggerConfig := branchsync.TriggerConfig{
		GroupName:    req.GroupName,
		ConfigName:   req.ConfigName,
		Source:       req.Source,
		Target:       req.Target,
		TriggerBy:    req.TriggerBy,
		Author:       req.UserName,
		SourceEscape: req.SourceEscape,
		TargetEscape: req.TargetEscape,
		NormalMr:     req.Normal,
		ChatId:       req.ChatID,
	}
	resp, err := branchsync.NewTaskHelper(triggerConfig).Execute(ctx)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		pkg.NewResponse(category, "version.merge.error").Reply().
			SetParam("source", req.Source).
			SetParam("target", req.Target).
			SetParam("error", err.Error()).
			SetOpenChatID(req.ChatID).
			SendWithChatBot(ctx, cb)
		return
	}

	if !resp.Success {
		pkg.NewResponse(category, "version.merge.error").Reply().
			SetParam("source", req.Source).
			SetParam("target", req.Target).
			SetParam("error", resp.Msg).
			SetOpenChatID(req.ChatID).
			SendWithChatBot(ctx, cb)
		return
	}

	var messageID string
	if len(resp.Msg) > 0 {
		messageID = pkg.NewResponse(category, "version.merge.done.notice").Reply().
			SetParam("url", resp.MrURL).
			SetParam("msg", resp.Msg).
			SetOpenChatID(req.ChatID).
			SendWithChatBot(ctx, cb).Must()
	} else {
		messageID = pkg.NewResponse(category, "version.merge.done.notice").Reply().
			SetParam("url", resp.MrURL).
			SetOpenChatID(req.ChatID).
			SendWithChatBot(ctx, cb).Must()
	}

	mrID, err := strconv.Atoi(tools.ExtractMrID(resp.MrURL))
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}

	var detail mysql.BranchSyncJob
	err = mysql.QualityReadDB.Where("mr_id=?", mrID).Find(&detail).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}

	detail.FirstMessageId = messageID
	err = mysql.QualityWriteDB.
		Context(ctx).
		Save(&detail).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
}

type doVersionMergeRequest struct {
	GroupName    string `json:"group_name"`
	UserName     string `json:"user_name"`
	Source       string `json:"source"`
	Target       string `json:"target"`
	ChatID       string `json:"chat_id"`
	SourceEscape bool   `json:"source_escape"`
	TargetEscape bool   `json:"target_escape"`
	Normal       bool   `json:"normal"`
	ConfigName   string `json:"config_name"`
	TriggerBy    string `json:"trigger_by"`
}
