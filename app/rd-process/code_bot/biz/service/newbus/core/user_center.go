package core

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/model/busmodel"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/newbus/msg"
	"context"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"

	"code.byted.org/gopkg/facility/set"
)

type UserCenter interface {
	GetAllUsers(ctx context.Context) []string
	GetBMs(ctx context.Context) []string
	GetByStanders(ctx context.Context) []string
	GetAllOpenID(ctx context.Context) []string
}

type userCenter struct {
	busmodel.VersionInfo
}

func NewUserCenter(info busmodel.VersionInfo) UserCenter {
	return &userCenter{info}
}

func (f *userCenter) GetAllUsers(ctx context.Context) []string {
	var users []string
	// bystander
	users = f.GetByStanders(ctx)
	users = append(users, f.GetBMs(ctx)...)

	userSet := set.NewStringSet()
	for _, user := range users {
		userSet.Add(emails.WithSuffix(user))
	}

	return userSet.ToList()
}

func (f *userCenter) GetBMs(ctx context.Context) (bms []string) {
	bms = append(bms, rpc.GetVersionQaBMs(ctx, f.GroupName, f.VersionCode)...)
	bms = append(bms, rpc.GetVersionRdBMs(ctx, f.GroupName, f.VersionCode)...)
	return
}

func (f *userCenter) GetByStanders(ctx context.Context) []string {
	return []string{}
}

func (f *userCenter) GetAllOpenID(ctx context.Context) []string {
	users := f.GetAllUsers(ctx)
	var openIds = make([]string, 0)
	for _, item := range users {
		id, err := msg.Bits.GetOpenIDByEmailPrefix(ctx, emails.TrimSuffix(item))
		if err != nil {
			continue
		}
		openIds = append(openIds, id)
	}
	return openIds
}
