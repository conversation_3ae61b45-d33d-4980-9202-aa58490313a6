package views

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/model/mqmodel"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/messageBuilder"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/notification"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/permission"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/util/tools"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/kitex_gen/bytedance/bits/code_frozen"
	"code.byted.org/gopkg/facility/slice"
	"code.byted.org/gopkg/logs"
	"context"
	"fmt"
)

func newFixVersion(ctx context.Context, setting *tools.ChatEnvSetting) {
	if len(setting.GroupName) == 0 {
		notification.DouDou.SendTextToChat(ctx, setting.ChatID, "group_name is required")
		return
	}

	// extra base version and new version
	text := setting.Text
	newVersion := tools.CutVersion(&text)
	baseVersion := tools.CutVersion(&text)
	if len(baseVersion) == 0 || len(newVersion) == 0 {
		notification.DouDou.SendTextToChat(ctx, setting.ChatID, "version number just like 1.0.0")
		return
	}

	helper := newFixHelper(setting.GroupName, baseVersion, newVersion)
	if helper.IsRunning(ctx) {
		notification.DouDou.SendTextToChat(ctx, setting.ChatID, "there is a running fix version event.")
		return
	}

	if !permission.IsVersionBM(ctx, baseVersion, setting.GroupName, setting.Username) && !permission.IsStableBM(ctx, setting.GroupName, setting.Username) {
		notification.DouDou.SendTextToChat(ctx, setting.ChatID, "you are not BM.")
		return
	}

	// trigger
	resp, err := rpc.CodeFrozenClient.CreateHotfixVersion(ctx, &code_frozen.CreateHotfixVersionRequest{
		GroupName:    setting.GroupName,
		BigVersion:   baseVersion,
		SmallVersion: newVersion,
		Username:     &setting.Username,
	})
	if err != nil {
		logs.CtxError(ctx, "CreateHotfixVersion error: %v", err)
		return
	}

	if !slice.Int32Contains([]int32{200, 0}, resp.GetBaseResp().GetStatusCode()) {
		msg, _ := messageBuilder.NewBuilder(messageBuilder.Category.Conversation, "fix_version.error").Build(ctx, map[string]string{
			"error": resp.GetBaseResp().GetStatusMessage(),
		})
		_, _ = setting.Bot.SendRichTextToChat(ctx, setting.ChatID, msg)
		return
	}

	helper.Record(ctx, setting.ChatID)
	msgContent, _ := messageBuilder.NewBuilder(messageBuilder.Category.Conversation, "fix_version.start").GetMessage(ctx)
	_, _ = setting.Bot.SendRichTextToChat(ctx, setting.ChatID, msgContent)
	return
}

func HookFixVersionEnd(ctx context.Context, input interface{}) {
	event, ok := input.(mqmodel.FixVersionEvent)
	if !ok {
		logs.CtxError(ctx, "fix version end assert failed - %v", input)
		return
	}

	helper := newFixHelper(event.GroupName, event.BaseVersion, event.NewVersion)
	chatId := helper.GetChatId(ctx)
	if len(chatId) == 0 {
		return
	}
	notification.DouDou.SendTextToChat(ctx, chatId, fmt.Sprintf("fix version (%s) is created successfully.", event.NewVersion))
}

type fixVersionHelper struct {
	Group       string
	BaseVersion string
	NewVersion  string
}

func newFixHelper(group, base, new string) fixVersionHelper {
	return fixVersionHelper{
		Group:       group,
		BaseVersion: base,
		NewVersion:  new,
	}
}

func (h *fixVersionHelper) key() string {
	return fmt.Sprintf("code_bot_fix_version_end_%s_%s_%s", h.Group, h.BaseVersion, h.NewVersion)
}

func (h *fixVersionHelper) Record(ctx context.Context, chatId string) {
	ok, err := redis.Tool.SetNX(h.key(), chatId, 0).Result()
	if err != nil || !ok {
		logs.CtxInfo(ctx, "record fix version err - %v", h.key())
		return
	}
}

func (h *fixVersionHelper) IsRunning(ctx context.Context) bool {
	val, err := redis.Tool.Get(h.key()).Result()
	if err != nil {
		logs.CtxInfo(ctx, "query fix version err - %v", err)
		return false
	}
	return len(val) > 0
}

func (h *fixVersionHelper) GetChatId(ctx context.Context) string {
	val, err := redis.Tool.Get(h.key()).Result()
	if err != nil {
		logs.CtxInfo(ctx, "query fix version err - %v", err)
		return ""
	}
	return val
}
