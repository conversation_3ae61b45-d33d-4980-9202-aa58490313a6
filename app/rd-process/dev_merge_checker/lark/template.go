package lark

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev_merge_checker/database/model"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"context"
	"fmt"
	"github.com/go-resty/resty/v2"
)

const header = `#red 检测到合入异常的开发任务
以下开发任务存在异常：\n`
const boeHeader = `#red [BOE] 检测到合入异常的开发任务
以下开发任务存在异常：\n`

const devHeader = `**DevBasic ID: %d**\n`
const devBody = `- %s [%s](%s)(%s)\n`
const devFooter = `---\n`
const footer = `--note--
存在开发任务合入异常，请立刻处理
--note-end--`

func generateJson(ctx context.Context, wrongDev map[int64][]model.MrInfo) (*larkRequestBody, error) {
	jingshu := generateJinshu(wrongDev)
	resp := struct {
		Code    int64           `json:"code"`
		Content larkRequestBody `json:"content"`
	}{}
	res, err := resty.New().R().SetContext(ctx).SetBody(map[string]string{"content": jingshu}).SetResult(&resp).Post("https://bot.apps.bytedance.net/jinshu/msg")
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get card body, err:%s", err.Error())
		return nil, err
	}
	if res.StatusCode() != 200 {
		logs.V1.CtxError(ctx, "failed to get card body, status code:%d", res.StatusCode())
		return nil, fmt.Errorf("failed to get card body, status code:%d", res.StatusCode())
	}
	return &resp.Content, err
}

func statusToEmoji(status string) string {
	switch status {
	case "opened":
		return "⚠️"
	case "merged":
		return "✅"
	case "closed":
		return "❌"
	default:
		return ""
	}
}
func generateJinshu(wrongDev map[int64][]model.MrInfo) string {
	str := header
	if env.IsBoe() {
		str = boeHeader
	}
	for devId, mrList := range wrongDev {
		str += fmt.Sprintf(devHeader, devId)
		for _, mr := range mrList {
			str += fmt.Sprintf(devBody, statusToEmoji(mr.Status), mr.Title, mr.Url, mr.Message)
		}
		str += devFooter
	}
	str += footer
	return str
}
