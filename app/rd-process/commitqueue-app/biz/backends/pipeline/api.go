/**
 * @Date: 2022/8/31
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package pipeline

import (
	"context"

	"code.byted.org/lang/gg/gresult"
)

type Api interface {
	TriggerPipeline(ctx context.Context, operator string, projectId int64, gitUrl string, branch string, commit string, env map[string]string, mrIid int64) gresult.R[int64]

	GetPipelineStatus(ctx context.Context, pipelineId int64) gresult.R[string]

	CancelPipeline(ctx context.Context, pipelineId int64, reason string)
}
