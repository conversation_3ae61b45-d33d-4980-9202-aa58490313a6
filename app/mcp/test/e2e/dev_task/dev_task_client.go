package main

import (
	"context"
	"log"

	mcpgolang "code.byted.org/devinfra/mcp"
	"code.byted.org/devinfra/mcp/transport/http"
	json "github.com/bytedance/sonic"
	"github.com/davecgh/go-spew/spew"
)

const (
	DevTaskURI         = "devTask://760446"
	SendMRNotification = "SendMRNotification"
)

func main() {
	// Create an HTTP transport that connects to the server
	transport := http.NewHTTPClientTransport("/v1/mcp/mcp/dev_task")
	transport.WithBaseURL("http://localhost:6789")

	// Create a new client with the transport
	client := mcpgolang.NewClient(transport)

	// Initialize the client
	if resp, err := client.Initialize(context.Background()); err != nil {
		log.Fatalf("Failed to initialize client: %v", err)
	} else {
		log.Printf("Initialized client: %v", spew.Sdump(resp))
	}

	// List available tools
	tools, err := client.ListTools(context.Background(), nil)
	if err != nil {
		log.Fatalf("Failed to list tools: %v", err)
	}

	log.Println("Available Tools:")
	jsonStr, err := json.MarshalIndent(tools, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal tools: %v", err)
	} else {
		log.Println(string(jsonStr))
	}

	// Read resource
	devTask, err := client.ReadResource(context.Background(), DevTaskURI)
	if err != nil {
		panic(err)
	}
	bs, err := json.Marshal(devTask)
	if err != nil {
		log.Fatalf("Failed to marshal devTask: %v", err)
	} else {

		log.Printf("%v", string(bs))
	}

	// List resources
	resources, err := client.ListResources(context.Background(), nil, map[string]string{"integrationId": "429919163184681"})
	if err != nil {
		panic(err)
	}
	bs, err = json.Marshal(resources)
	if err != nil {
		log.Fatalf("Failed to marshal resources: %v", err)
	} else {
		log.Printf("%v", string(bs))
	}

	// Call tool
	response, err := client.CallTool(context.Background(), SendMRNotification,
		map[string]any{
			"integrationId":   429919163184681,
			"releaseTicketId": 171776403712,
			"receiverType":    "Individual--",
		},
	)
	if err != nil {
		panic(err)
	}
	bs, err = json.Marshal(response)
	if err != nil {
		log.Fatalf("Failed to marshal response: %v", err)
	} else {
		log.Printf("%v", string(bs))
	}

	// Call tool
	//response, err = client.CallTool(context.Background(), "MoveDevTask",
	//	map[string]any{"sourceReleaseTicketId": 171776403712, "targetReleaseTicketId": 171932895744, "devTaskId": 760483})
	//if err != nil {
	//	panic(err)
	//}
	//bs, _ = json.Marshal(response)
	//log.Printf("MoveDevTask succeed, %v", string(bs))
}
