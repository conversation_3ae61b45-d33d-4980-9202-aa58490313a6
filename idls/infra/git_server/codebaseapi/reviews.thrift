include "../codebase.thrift"
include "../../../base.thrift"

namespace go bytedance.bits.git_server

struct GetReviewStatusRequest {
    1: required i64 repo_id
    2: required i64 change_id
    3: optional string username
    100: optional bool try_use_cache
    255: optional base.Base Base
}

struct GetReviewStatusResponse {
    1: optional ReviewSummary summary
    255: optional base.BaseResp BaseResp
}

struct ReviewSummary {
    1: string status
    2: list<codebase.CodebaseUser> disapprovers
    3: list<ReviewRuleApprovalInfo> approvals
}

enum ReviewSummaryStatus {
    Passed = 1
    Pending = 2
    Disapproved = 3
}

struct ReviewRuleApprovalInfo {
    1: list<codebase.CodebaseUser> approvers
    2: optional ReviewRule2 review_rule
}

struct ReviewRule2 {
    1: i64 id
    2: i64 parent_id
    3: bool default
    4: ReviewRuleType type
    5: i64 app_id
    6: string group_name
    7: string name
    8: i16 approvals_required
    9: list<codebase.CodebaseUser> users
    10: list<UserGroup> groups
    11: map<i64, string> user_reasons
    12: map<i64, i64> user_operators
}

enum ReviewRuleType {
    Regular = 1
    Fallback = 2
    CodeOwner = 3
}

struct UserGroup {
    1: i64 id
    2: string name
    3: list<codebase.CodebaseUser> users
}
