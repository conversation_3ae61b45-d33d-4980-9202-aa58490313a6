include "request_v2.thrift"
include "response_v2.thrift"
include "../../../base.thrift"

namespace go bytedance.bits.git_server_v2

// GitServiceV2 应对与 Code Change 相关的接口
// 注意: ccid 为 code change id 的简称
// 注意，接口不会返回 error，通过 response 的 BaseResp 的 Status 字段判断是否异常，0 表示正常返回，非 0 的话， StatusMessage 字段是错误说明
service GitServiceV2 {
    // 通过 code change id 获得 diff 文件名字, request 中 sha 和 base_sha 一般不需要你填写
    response_v2.GetDiffFilesOnCodeChangeResponse GetDiffFilesOnCodeChange(1: request_v2.GetDiffFilesOnCodeChangeRequest req)

    // 通过 code change id 获得 merge-base 节点
    response_v2.GetMergeBaseOnCodeChangeResponse GetMergeBaseOnCodeChange(1: request_v2.GetMergeBaseOnCodeChangeRequest req)

    // 搜索 code change ids
    response_v2.FindCodeChangeIdsByRepoIdAndSourceBranchResponse FindCodeChangeIdsByRepoIdAndSourceBranch(1: request_v2.FindCodeChangeIdsByRepoIdAndSourceBranchRequest req)

    response_v2.SubmitChangeOnCodeChangeResponse SubmitChangeOnCodeChange(1: request_v2.SubmitChangeOnCodeChangeRequest req)
}
