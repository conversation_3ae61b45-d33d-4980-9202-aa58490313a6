include "../../base.thrift"
include "enums.thrift"

namespace go bits.workflow.execution_engine

service EngineFaultRecoveryService {
    // 这个接口的作用: 流程引擎的 ResetExecution 接口,会先停止状态机,然后在启动起来,这个过程不是原子的
    // 所以就会出现,停下之后重新启动失败的问题,所以加上这个接口,重新启动失败,再启动一次
    base.EmptyResponse TryRecoveryExecution(1: TryRecoveryExecutionRequest req)
}

struct TryRecoveryExecutionRequest {
    2: required i64 unique_id // 你的业务的唯一 id
    3: required enums.UniqueType unique_type
    4: required string task_name
}
