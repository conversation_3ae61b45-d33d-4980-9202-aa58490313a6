include "../../base.thrift"
include "request.thrift"
include "response.thrift"
include "meta_common.thrift"
include "onesite.thrift"
include "employee.thrift"
include "lark.thrift"
include "error_config.thrift"
include "member.thrift"

namespace go bytedance.bits.meta

service S1 extends lark.LarkService {}
service S2 extends employee.EmployeeService {}
service S3 extends member.AppMemberService {}

service MetaService {
    employee.SearchEmployeeResponse SearchEmployee(1: employee.SearchEmployeeRequest req) // 搜索用户
    employee.QueryEmployeeResponse QueryEmployee(1: employee.QueryEmployeeRequest req) // 根据用户名前缀查询用户

    response.GetAppBasicInfosResponse GetAppBasicInfos(1: request.GetAppBasicInfosRequest req) // 为前端提供的查询所有空间的接口
    response.QueryAppSimpleInfoByIdResponse QueryAppSimpleInfoById(1: request.QueryAppSimpleInfoByIdRequest req)
    response.QueryAppSimpleInfoByEnglishNameResponse QueryAppSimpleInfoByEnglishName(1: request.QueryAppSimpleInfoByEnglishNameRequest req)
    response.CreateAppResponse CreateApp(1: request.CreateAppRequest req) // 创建app
    response.UpdateAppResponse UpdateApp(1: request.UpdateAppRequest req) //更新app
    response.QueryAppInfoResponse QueryAppInfo(1: request.QueryAppInfoRequest req) // 查询app信息
    response.QueryAppInfoResponse QueryAppInfoById(1: request.QueryAppInfoByIdRequest req) // 根据内部 ID 查询 app 信息
    response.QueryAppInfoResponse QueryAppInfoByEnglishName(1: request.QueryAppInfoByEnglishNameRequest req)
    response.QueryAppBaseInfoResponse QueryAppBaseInfoByIds(1: request.QueryAppBaseInfoByIdsRequest req) // 根据内部 ID列表 查询 app 基础信息
    response.QueryAppInfoByAppNameResponse QueryAppInfoByAppName(1: request.QueryAppInfoByAppNameRequest req) // 根据appName查询appInfo
    response.QueryAppInfosByEnglishNameResponse QueryAppInfosByEnglishName(1: request.QueryAppInfosByEnglishNameRequest req) // 根据英文名数组，查询 App 列表
    response.QueryAppSimpleInfosByRepoResponse QueryAppSimpleInfosByRepo(1: request.QueryAppSimpleInfosByRepoRequest req) // 根据仓库信息查询 App 列表
    response.QueryAppInfoResponse QueryAppInfoByNameNoLike(1: request.QueryAppInfoByNameNoLikeQuery req) // 根据app_name查询app_id，无模糊查询
    response.QueryAppInfoByAppCloudIdResponse QueryAppInfoByAppCloudId(1: request.QueryAppInfoByAppCloudIdRequest req) // 根据应用云id 查询 App 信息
    response.QueryAppIdResponse QueryAppId(1: request.QueryAppIdRequest req) // 查询app id
    response.QueryAppIdResponse QueryAppIdV2(1: request.QueryAppIdRequest req) // 查询app id
    response.QueryAllAppSimpleInfosResponse QueryAllAppSimpleInfos(1: request.QueryAllAppSimpleInfosRequest req) // 简化版数据的全量查询
    response.QueryAppSimpleInfosPaginateResponse QueryAppSimpleInfosPaginate(1: request.QueryAppSimpleInfosPaginateRequest req) // 分页查询 app 列表
    response.QueryAllAppInfostResponse QueryAllAppInfos(1: request.QueryAllAppInfosRequest req) // 全量app列表查询
    response.QueryAppSimpleInfoByIdsResponse QueryAppSimpleInfoByIds(1: request.QueryAppSimpleInfoByIdsRequest req) // 根据内部 ID 列表查询 app 的简单信息
    response.CountAppResponse CountApp(1: request.CountAppRequest req) // App 访问计数
    response.CheckAppAdminRoleByEnglishNameResponse CheckAppAdminRoleByEnglishName(1: request.CheckAppAdminRoleByEnglishNameRequest req) // 校验应用的管理员权限
    response.GetAppBySpaceIdResponse GetAppBySpaceId(1: request.GetAppBySpaceIdRequest req) // 通过 onesite space id 查询 app info

    response.QueryPlatformRolesResponse QueryPlatformRoles(1: request.QueryPlatformRolesRequest req) //根据appId和userName查询该用户在平台的角色(自己管理的权限)

    // kani resource
    response.QueryUserHasPermissionResponse QueryUserHasPermission(1: request.QueryUserHasPermissionRequest req) // 查询用户是否拥有 指定资源 指定行为 的权限
    response.QueryUserAppPermissionResponse QueryUserAppPermission(1: request.QueryUserAppPermissionRequest req) // 查询用户在 App 下的权限
    response.GetUserEmployeeIdResponse GetUserEmployeeId(1: request.GetUserEmployeeIdRequest req) // 查询用户的工号

    response.QueryLarkUserResponse QueryLarkUser(1: request.QueryLarkUserRequest req) // 根据lark open id 查询用户
    response.QueryLarkIdByEmailResponse QueryLarkIdByEmail(1: request.QueryLarkIdByEmailRequest req) // 根据 email 查询用户的 open id, employee id
    response.IsUserResignedResponse IsUserResigned(1: request.IsUserResignedRequest req) // 根据 email 查询用户是否离职

    base.EmptyResponse RefreshUser(1: request.RefreshUserRequest req) // 刷新用户相关token
    base.EmptyResponse RefreshUserToken(1: request.RefreshUserTokenRequest req) // 刷新用户相关token
    response.GetUserTokenInfoResponse GetUserTokenInfo(1: request.GetUserTokenInfoRequest req) // 查询用户相关token
    response.GetUserLarkInfoResponse GetUserLarkInfo(1: request.GetUserLarkInfoRequest req) // 查询用户的Lark信息
    response.RemoveUserFromLarkChatResponse RemoveUserFromLarkChat(1: request.RemoveUserFromLarkChatQuery req) // 把用户移除指定群聊
    response.GetUsersInLarkGroupResponse GetUsersInLarkGroup(1: request.GetUsersInLarkGroupQuery req) // 获取 lark 群里的用户信息
    response.CreateLarkGroupResponse CreateLarkGroup(1: request.CreateLarkGroupQuery req) // 创建 lark 群聊
    response.UpdateLarkGroupResponse UpdateLarkGroup(1: request.UpdateLarkGroupQuery req) // 更新 lark 群聊
    response.InviteUserJoinLarkGroupResponse InviteUserJoinLarkGroup(1: request.InviteUserJoinLarkGroupQuery req) // 拉指定用户进指定群
    response.GetMiniAppUserInfoResponse GetMiniAppUserInfo(1: request.GetMiniAppUserInfoQuery req) // 获取小程序的用户信息
    response.CreateTeamGroupInfoResponse CreateTeamGroupInfo(1: request.CreateTeamGroupInfoRequest req) // 添加团队
    response.DeleteTeamGroupInfoResponse DeleteTeamGroupInfo(1: request.DeleteTeamGroupInfoRequest req) // 删除团队
    response.UpdateTeamGroupInfoResponse UpdateTeamGroupInfo(1: request.UpdateTeamGroupInfoRequest req) // 修改团队
    response.GetTeamGroupInfoResponse GetTeamGroupInfo(1: request.GetTeamGroupInfoRequest req) // 查询团队
    response.GetAllTeamGroupsInfoByAppIdResponse GetAllTeamGroupsInfoByAppId(1: request.GetAllTeamGroupsInfoByAppIdRequest req) // 查询App下所有团队
    response.GetAppTeamRepoRelationsByTeamIDResponse GetAppTeamRepoRelationsByTeamID(1: request.GetAppTeamRepoRelationsByTeamIDRequest req) // 查询团队下所有关联组件
    response.CreateAppMemberInfoResponse CreateAppMemberInfo(1: request.CreateAppMemberInfoRequest req) // 增加人员
    response.DeleteAppMemberInfoResponse DeleteAppMemberInfo(1: request.DeleteAppMemberInfoRequest req) // 删除人员
    response.UpdateAppMemberInfoResponse UpdateAppMemberInfo(1: request.UpdateAppMemberInfoRequest req) // 更新人员
    response.GetSingleAppMemberInfoResponse GetSingleAppMemberInfo(1: request.GetSingleAppMemberInfoRequest req) // 查询单个人员
    response.GetAllAppMembersInfoResponse GetAllAppMembersInfo(1: request.GetAllAppMembersInfoRequest req) // 查询所有人员（支持筛选+分页）
    response.GetAppMemberAllAdminAppsResponse GetAppMemberAllAdminApps(1: request.GetAppMemberAllAdminAppsRequest req) // 查询某个人有哪些App管理员的权限

    // ======================IAM 服务 ==============================
    response.IAMServiceTreeNode CreateIAMGroup(1: request.IAMCreateGroupReq req) // 创建主账号
    response.GetIAMAccountListResp GetIAMGroupList(1: meta_common.VoidStruct req) // 获取主账号列表
    response.CloudApiResp BindIAMRole(1: request.IAMBindRoleQuery req ) //授予用户角色
    response.IAMPermissionCheckRes CheckIAMPermission(1: request.IAMPermissionCheckQuery req) //检查权限
    response.GenerateIamTokenResp GenerateIamToken(1: request.GenerateIamTokenQuery req) // 生成Token
    response.VerifyIAMTokenResp VerifyIAMToken(1: request.VerifyIAMTokenQuery req) // 校验Token
    response.VerifyIAMTokenResp VerifyServiceToken(1: request.VerifyIAMTokenQuery req) // 校验服务账号Token
    response.GetIAMTokenResp GetIAMToken(1: request.GetIAMTokenQuery req) // 获取Token

    response.QueryCodebaseTokenResponse QueryCodebaseToken(1: request.QueryCodebaseTokenRequest req) // 查询codebase token

    // OpenAPI
    response.QueryAppInfoListResponse SearchApp(1: request.SearchAppInfoQuery req) // 模糊查询AppInfo,走ES
    response.SearchAppInfoResp ListApp(1: request.SearchAppInfoQuery req) // App列表，带置顶
    response.CheckAppPermissionResp CheckAppPermission(1: request.CheckAppPermissionQuery req) //检查APP权限
    response.CreateIAMTIcketResp CreateIAMTicket(1: request.CreateIAMTIcketQuery req) //创建工单
    meta_common.VoidStruct CancelTicket(1: request.CancelTicketQuery req) //取消工单
    response.QueryAppInfoListResponse GetAuthorizedApps(1: request.GetAuthorizedAppsQuery req)

    // Onesite
    response.OneSitePingResp OneSitePing(1: request.OneSitePingReq req) // ping
    response.OneSiteCreateSpaceResp OneSiteCreate(1: request.OneSiteCreateSpaceReq req)
    response.OneSiteUpdateResp OneSiteUpdate(1: request.OneSiteUpdateSpaceReq req)
    base.EmptyResponse OneSiteInitialize(1: request.OneSiteInitializeSpaceReq req)
    response.CreateOneSiteResourceResponse CreateOneSiteResource(1: request.CreateOneSiteResourceRequest req)
    base.EmptyResponse DeleteOneSiteResource(1: request.DeleteOneSiteResourceRequest req)
    response.UpdateOneSiteResourceResponse UpdateOneSiteResource(1: request.UpdateOneSiteResourceRequest req)
    response.BatchCheckOneSitePermissionResponse BatchCheckOneSitePermission(1: request.BatchCheckOneSitePermissionRequest req)
    base.EmptyResponse GrantOneSitePermission(1: request.GrantOneSitePermissionRequest req)
    base.EmptyResponse RevokeOneSitePermission(1: request.RevokeOneSitePermissionRequest req)
    base.EmptyResponse UpdateOneSitePermission(1: request.UpdateOneSitePermissionRequest req)
    onesite.IdTransferResponse IdTransfer(1: onesite.IdTransferRequest req)
    onesite.CheckSpaceUsableResponse OneSiteCheckSpaceUsable(1: onesite.CheckSpaceUsableRequest req)
    response.BatchGrantRevokeOneSitePermissionResponse BatchGrantRevokeOneSitePermission(1: request.BatchGrantRevokeOneSitePermissionRequest req)

    response.GetCalendarWorkspacesByAppIdResponse GetCalendarWorkspacesByAppId(1: request.GetCalendarWorkspacesByAppIdRequest req)

    response.ListLarkChatGroupsResponse ListLarkChatGroups(1: request.ListLarkChatGroupsRequest req)

    // 错误码配置相关
    // 获取所有错误码配置
    error_config.GetErrorConfigListResponse GetErrorConfigList(1: error_config.GetErrorConfigListRequest req)
    // 结束错误码配置相关
}

struct GetIAMAChildAccountListReq {
    1: required i32 parentId
}

struct IAMNodeIdStruct {
    1: required i32 id
}

struct IAMTokenStruct {
    1: required string token
}

struct IAMUserStruct {
    1: required string user
}
