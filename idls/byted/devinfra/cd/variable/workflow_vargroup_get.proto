syntax = "proto3";

package byted.devinfra.cd.variable;

import "idls/byted/api/api.proto";
import "idls/byted/bc/varstore/group.proto";
import "idls/byted/devinfra/cd/variable/scene.proto";
import "validate/validate.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/variablepb";

message GetWorkflowVarGroupReq {
  uint64 workflow_id = 1 [(api.path) = "workflowId"];
  // @required
  Scene scene = 2 [(validate.rules).enum = {
    defined_only: true,
  }];
  // @required
  uint64 space_id = 3 [(api.query) = "spaceId"];
}

message GetWorkflowVarGroupResp {
  // custom_groups的长度为1 或者为null， 为null代表未绑定任何变量组
  repeated bc.varstore.VarGroup custom_groups = 1;
  repeated bc.varstore.VarGroup system_groups = 2;
}