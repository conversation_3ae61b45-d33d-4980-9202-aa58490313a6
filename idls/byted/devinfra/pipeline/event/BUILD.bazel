load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "event_proto",
    srcs = ["event_service.proto"],
    visibility = ["//visibility:public"],
    deps = ["//idls/byted/devinfra/infra/rpc:rpc_proto"],
)

go_proto_library(
    name = "event_go_proto_xrpc_and_kitex_PipelineEventService",
    compilers = [
        "@io_bazel_rules_go//proto:xrpc_plugin_go",
        "@io_bazel_rules_go//proto:go_kitex_proto_service",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/eventpb/pipelineeventservice",
    proto = ":event_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":event_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
    ],
)

go_proto_library(
    name = "event_go_proto_mockgen_PipelineEventService",
    compilers = ["@io_bazel_rules_go//proto:go_kitex_proto_mockgen"],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/eventpb/pipelineeventservice_mock",
    proto = ":event_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":event_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
    ],
)

go_proto_library(
    name = "event_go_proto_kitexcli_PipelineEventService",
    compilers = ["@io_bazel_rules_go//proto:kitexcli_plugin_go"],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/rpc/bits/pipelineeventrpc",
    proto = ":event_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":event_go_proto_xrpc_and_kitex_PipelineEventService",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
    ],
)

go_proto_library(
    name = "event_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_kitex_proto",
        "@io_bazel_rules_go//proto:xhertz_plugin_go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/eventpb",
    proto = ":event_proto",
    visibility = ["//visibility:public"],
    deps = ["//idls/byted/devinfra/infra/rpc:rpc_go_proto"],
)
