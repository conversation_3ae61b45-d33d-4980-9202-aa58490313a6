load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "user_confirm_proto",
    srcs = ["plugin_service.proto"],
    visibility = ["//visibility:public"],
    deps = ["@com_google_protobuf//:timestamp_proto"],
)

go_proto_library(
    name = "user_confirm_go_proto_xrpc_and_kitex_PluginService",
    compilers = [
        "@io_bazel_rules_go//proto:xrpc_plugin_go",
        "@io_bazel_rules_go//proto:go_kitex_proto_service",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanage/atoms/user_confirm/pluginservice",
    proto = ":user_confirm_proto",
    visibility = ["//visibility:public"],
    deps = [":user_confirm_go_proto"],
)

go_proto_library(
    name = "user_confirm_go_proto_mockgen_PluginService",
    compilers = ["@io_bazel_rules_go//proto:go_kitex_proto_mockgen"],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanage/atoms/user_confirm/pluginservice_mock",
    proto = ":user_confirm_proto",
    visibility = ["//visibility:public"],
    deps = [":user_confirm_go_proto"],
)

go_proto_library(
    name = "user_confirm_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_kitex_proto",
        "@io_bazel_rules_go//proto:xhertz_plugin_go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanage/atoms/user_confirm",
    proto = ":user_confirm_proto",
    visibility = ["//visibility:public"],
)
