syntax = "proto3";

package byted.bc.change_item;

import "idls/byted/bc/shared/shared.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/changeitempb";

// clang-format off
// 变更项信息，这个结构不用于前端交互，用于后端业务逻辑
message ChangeItem {
  // 项目唯一 ID
  // @required
  string project_unique_id = 1;
  // 项目类型
  // @required
  shared.ProjectType project_type = 2;
  // 变更项内容
  Content content = 3;
  // 依赖信息
  // @required
  // clang-format off
  repeated DependencyItem dependency = 4;
  // 项目名称
  string project_name = 5;
  // 控制面
  shared.ControlPlane control_plane = 6;
  // BOE 泳道名称
  string boe_env_name = 7;
  // PPE 泳道名称
  string ppe_env_name = 8;
}

message Content {
  // @required
  repeated SCMInfo scm_list = 1;
  repeated GeckoItem gecko_items = 4;

  // 用户自定义变量的assignmentID
  uint64 user_custom_var_assignment_id = 5;

  // @deprecated
  repeated GeckoItem beta_gecko_items = 2 [deprecated = true];
  repeated GeckoItem prod_gecko_items = 3 [deprecated = true];
}

message GeckoItem {
  // 内测 channel ID
  // @optional
  uint64 beta_channel_id = 1;
  // 内测部署环境 id
  // @optional
  uint64 beta_deploy_env_id = 2;
  // 内测泳道名称
  // @optional
  string channel_name = 3;
  // 生产 channel ID
  // @optional
  uint64 prod_channel_id = 4;
  // 生产部署环境 id
  // @optional
  uint64 prod_deploy_env_id = 5;
  // gecko 控制面
  string region = 6;
  // gecko 应用 ID
  uint64 app_id = 7;
  // gecko 应用名称
  string app_name = 8;
  // 发布离线包/在线包
  repeated string gecko_package_types = 9;
  // 发布包的目标操作系统
  string target_os = 10;
  // beta_channel_id 和 prod_channel_id 永远有一个为空，另一个不为空。使用 uni_channel_id 来存储非空的 channel_id
  uint64 uni_channel_id = 11;
  // 发包的设备 id
  string device_id = 12;
}

message SCMInfo {
  // SCM 仓库 ID
  // @required
  uint64 id = 1;
  // SCM 仓库名称
  // @required
  string name = 2;
  // 发版方式
  // @required
  shared.SCMPubBase pub_base = 3;
  // 发版内容
  // @required
  string revision = 4;
  // Git 仓库名称
  // @required
  string git_repo_name = 5;
  // 是否是主仓
  bool is_main = 6;
}

message DependencyItem {
  // 依赖的项目唯一 ID
  // @required
  string project_unique_id = 1;
  // 依赖的项目类型
  // @required
  shared.ProjectType project_type = 2;
  // 依赖的项目名称
  string project_name = 3;
}

message Dependency {
  // 项目唯一 ID
  string project_unique_id = 1;
  // 项目类型
  shared.ProjectType project_type = 2;
  // 项目名称
  string project_name = 3;
  // 依赖信息
  repeated DependencyItem dependency_item_list = 4;
}
// clang-format on