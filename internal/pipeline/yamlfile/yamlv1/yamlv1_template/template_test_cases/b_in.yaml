template: test
services_for_test:
  - id: redis
    image: hub.byted.org/ee/redis
  - id: mysql
    image: hub.byted.org/ee/mysql:5.7
    envs:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: test
pre_test:
  name: Init MySQL
  uses: actions/mysql-scripts@v1
  inputs:
    db_name: test
    host: mysql
    path: "docs/sql/*.sql"
    user: root
    password: password
coverage_file: coverage.out
mysql_host: mysql
coverage_config:
  threshold: 10%
threshold: "20%"
trigger_branch:
  - dev
  - test
unpack_envs:
  - E2=V2
  - E3=V3
paths:
  - "*.json"
  - "*.sh"
