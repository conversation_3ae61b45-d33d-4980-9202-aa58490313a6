package limiter

import (
	"context"
	"fmt"

	v2 "code.byted.org/iesarch/simple_rate_limit/v2"
)

var keyLimiter v2.KeyLimiter

func MustInit() {
	keyLimiter = v2.NewKeyLimiterWithTCC(context.Background())
}

func GetKeyLimiter() v2.KeyLimiter {
	return keyLimiter
}

func GenLimitRateKey(psm string, fromService string) string {
	if psm == "" || fromService == "" {
		return ""
	}

	return fmt.Sprintf("%s.limiter.%s", psm, fromService)
}
