load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "pwebhook",
    srcs = [
        "do.go",
        "entity.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/internal/pipeline/pwebhook",
    visibility = ["//:__subpackages__"],
    deps = ["@io_gorm_gorm//:gorm"],
)

go_test(
    name = "pwebhook_test",
    srcs = [
        "do_test.go",
        "entity_test.go",
    ],
    embed = [":pwebhook"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@io_gorm_driver_sqlite//:sqlite",
        "@io_gorm_gorm//:gorm",
    ],
)
