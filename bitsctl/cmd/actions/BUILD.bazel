load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "actions",
    srcs = [
        "branch_traversal.go",
        "checkout_branch.go",
        "clean_branches.go",
        "continue.go",
        "create_branch.go",
        "current_branch_onto.go",
        "delete.go",
        "delete_branch.go",
        "depend.go",
        "edit_branch.go",
        "edit_downstack.go",
        "feature_trunk.go",
        "fold_branch.go",
        "get.go",
        "log.go",
        "log_short_classic.go",
        "login.go",
        "logout.go",
        "oncall.go",
        "persist_continuation.go",
        "pre_initialized.go",
        "print_conflict_status.go",
        "rename_branch.go",
        "reset_trunk.go",
        "restack.go",
        "show_branch.go",
        "split.go",
        "squash.go",
        "stack_edit_file.go",
        "survey.go",
        "sync.go",
        "sync_pr_info.go",
        "test.go",
        "track_branch.go",
        "trunk.go",
        "trunk_checker.go",
        "undo.go",
        "untrack_branch.go",
        "update.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/bitsctl/cmd/actions",
    visibility = ["//visibility:public"],
    deps = [
        "//bitsctl/cmd/actions/bits_dev/sync",
        "//bitsctl/cmd/actions/feature_trunk_conflict",
        "//bitsctl/cmd/actions/restack_hook",
        "//bitsctl/cmd/lib/api",
        "//bitsctl/cmd/lib/archive_log",
        "//bitsctl/cmd/lib/bitsapi",
        "//bitsctl/cmd/lib/checker",
        "//bitsctl/cmd/lib/context",
        "//bitsctl/cmd/lib/engine",
        "//bitsctl/cmd/lib/errors",
        "//bitsctl/cmd/lib/git",
        "//bitsctl/cmd/lib/logger",
        "//bitsctl/cmd/lib/preconditions",
        "//bitsctl/cmd/lib/prompt",
        "//bitsctl/cmd/lib/session",
        "//bitsctl/cmd/lib/spiffy",
        "//bitsctl/cmd/lib/sso",
        "//bitsctl/cmd/lib/utils",
        "//bitsctl/cmd/version",
        "@com_github_alecaivazis_survey_v2//:survey",
        "@com_github_alecaivazis_survey_v2//terminal",
        "@com_github_google_uuid//:uuid",
        "@com_github_mgutz_ansi//:ansi",
        "@com_github_mitchellh_go_homedir//:go-homedir",
        "@in_gopkg_resty_v1//:resty_v1",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_facility//slice",
        "@org_byted_code_gopkg_lang_v2//setx",
        "@org_byted_code_gopkg_lang_v2//slicex",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "actions_test",
    srcs = ["oncall_test.go"],
    embed = [":actions"],
)
