# 对应 app/aiapi 目录
template: bazel
name: bits.devopsai.rpc
spec: m1.8xlarge
image: hub.byted.org/base/bazel.codebase_ci_base:latest
env: boe
test_execution_version: v2
enable_proxy: true
enable_byteview: true
enable_codecov: true
enable_profile_upload: true
enable_mongo: true
enable_redis: true
enable_mysql: true
mysql_database: halo_test
mysql_password: root
doas_psm: "bits.oreo.rpc"
go_version: "1.21"

caches:
  - backend: ebs
    key: hagrid-bazel-7_4_0-ci-devops_ai
    size: 1024
    git_clean: "-dfx"

trigger:
  change:
    # 对于目标分支是feat/xxx的MR，不跑 ci
    # 一般来说，大家开发时，源分支是feat/xxx, 目标分支是main或者release/**
    # 目标分支通常不会是feat/xxx
    # 但是由于其他原因，有的feat/xxx会常驻主干，并且有main --> feat/xxx 的open MR
    # 这个MR是不需要跑CI的。
    source-branches: [ "!main" ]
    paths: &paths
      - ".bazelrc"
      - ".bazelisk"
      - "app/aiapi/**"
      - ".codebase/pipelines/devops_ai.yml"

pre_steps:
  - name: "Make sure that git working directory is clean"
    commands:
      - git diff-index --quiet HEAD --
  - name: "Install packages"
    commands:
      # This is needed to support https://github.com/lestrrat/go-pcre2
      - apt-get update && apt-get install -y libpcre2-dev
  - name: "Kitex gen"
    commands:
      - ./scripts/kitex_gen_rule.sh copy
  - name: "Gazelle Update Check"
    commands:
      - ./scripts/gazelle-update-test.sh

commands:
  - go version
  - bazel coverage --test_timeout=120 --cache_test_results=yes --test_env=CI_WORKSPACE --config=ci //app/aiapi/... --test_tag_filters="-known-to-fail,-originally-unchecked,-known-build-fail --@io_bazel_rules_go//go/config:gc_goopts="-l,-N""

coverage_config:
  include_files: *paths
  exclude_files:
    - "app/aiapi/ai_api_impl.go"
    - "app/aiapi/testfactory/**"
    - "**/init.go"
    - "**/main.go"
    - "**/*.gen.go"
    - "**/*mock.go"
    - "**/query/gen.go"
  diff_percent: 60
  diff_lines_minimum: 5

codecov_subproject_name: "bits.devopsai.rpc"
