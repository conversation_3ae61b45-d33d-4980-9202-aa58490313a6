load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "rule_manage",
    srcs = [
        "rule.go",
        "rule_config.go",
        "rule_set.go",
        "scan_plan.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/rule_manage",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/bits-api/kitex_gen/bytedance/bits/rule_management",
        "//gateway/bits-api/pkg/rpc",
        "//gateway/bits-api/util",
        "//libs/bits_err",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_hallokael_httpr//:httpR",
        "@org_byted_code_gin_ginex//:ginex",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
