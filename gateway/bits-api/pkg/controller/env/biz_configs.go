package env

import (
	cd "code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bits/devops/continuous_deployment"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/gin/ginex"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func GetEnvBizConfigs(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := new(cd.GetBizEnvConfigReq)
	err := c.ShouldBindQuery(&param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}
	param.Operator = util.GetUserName(c)

	resp, err := rpc.ContinuousDeploymentClient.GetBizEnvConfig(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp)
}
