package consts

const OPTIMUS_CONFLICTED = "conflicted"

const OPTIMUS_OPENED = "opened"
const OPTIMUS_CLOSED = "closed"
const OPTIMUS_MERGED = "merged"
const OPTIMUS_LOCKED = "locked"

const OPTIMUS_PART_CLOSED = "part_closed"
const OPTIMUS_PART_MERGED = "part_merged"

const OPTIMUS_PUBLISHING = "version_publishing"
const OPTIMUS_PUBLISH_VERSION_SUCCEEDED = "version_published"
const OPTIMUS_PUBLISH_VERSION_FAILED = "version_publish_failed"

const OPTIMUS_INTERATING = "integrating"
const OPTIMUS_INTERATION_SUCCEEDED = "integrated"
const OPTIMUS_INTERATION_FAILED = "integration_failed"

const OPTIMUS_INTETRATION_STATE_PENDING = "pending"
const OPTIMUS_INTETRATION_STATE_RUNNING = "running"
const OPTIMUS_INTETRATION_STATE_FAILED = "failed"
const OPTIMUS_INTETRATION_STATE_SUCCEEDED = "succeeded"

const OPTIMUS_PUBLISH_PENDING = "pending"
const OPTIMUS_PUBLISH_RUNNING = "running"
const OPTIMUS_PUBLISH_FAILED = "failed"
const OPTIMUS_PUBLISH_SUCCEEDED = "succeeded"

const OPTIMUS_MR_MODE_MULTIPLE_MASTER_PARENT = "multiple_master_mr_parent"
const OPTIMUS_MR_MODE_MULTIPLE_MASTER_CHILD = "multiple_master_mr_child"
const OPTIMUS_MR_MODE_MULTIPLE_COMMON = "multiple_mr_common"
const OPTIMUS_MR_MODE_SINGLE_MASTER_PARENT = "single_master_mr_parent"
const OPTIMUS_MR_MODE_SINGLE_MASTER_CHILD = "single_master_mr_child"
const OPTIMUS_MR_MODE_ISOLATE_MASTER = "isolate_master"

const OPTIMUS_CONFLICTED_UNCHECKED = "unchecked"
const OPTIMUS_CONFLICTED_CONFLICTED = "conflicted"
const OPTIMUS_CONFLICTED_NOT_CONFLICTED = "not_conflicted"
const OPTIMUS_CONFLICTED_CHECKING = "checking"
const OPTIMUS_CONFLICTED_CHECK_FAILED = "check_failed"

const OPTIMUS_SERVER_BASE_URL = "https://optimus.bytedance.net"

const OPTIMUS_RERUN_PATH = "/api/v1/rerun"
const RERUN_TYPE_JOB = "job"

const CONY_PSM = "bits.devops.cony_stable"
const OPTIMUS_PSM = "douyin.tech.optimus"

const OptimusBitsErrorSplitMark = "::"

const (
	UrlCreateMr                = "/api/cony/workflow/merge/request/create"
	UrlCreateMrBoe             = "/c_int/api/workflow/merge/request/create"
	UrlCloseMr                 = "/api/v2/projects/%v/merge_requests/%v/close"
	UrlForceMerge              = "/api/v2/projects/%v/merge_requests/%v/force_merge"     // 强合
	UrlSkipReview              = "/api/v2/projects/merge_requests/skip_code_review_task" // skip review
	UrlLock                    = "/api/v1/locks"                                         // 加锁： 超级Mr
	UrlEditBranch              = "/api/v3/merge_requests/%v"
	UrlEditVersionDependency   = "/api/v3/version_dependencies/%v"
	UrlEditCommonField         = "/api/v3/merge_requests/%d/common_fields"
	UrlAddMrDependency         = "/api/v3/merge_requests/%v/mr_dependency/add/"
	UrlRemoveMrDependency      = "/api/v3/merge_requests/%v/mr_dependency/remove/"
	UrlAddVersionDependency    = "/api/v3/merge_requests/%v/version_dependency/add/"
	UrlRemoveVersionDependency = "/api/v3/merge_requests/%v/version_dependency/remove/"
	UrlAddCommonDependency     = "/api/v3/merge_requests/%v/common_dependency_mr/add/"
	UrlRemoveCommonDependency  = "/api/v3/merge_requests/%v/common_dependency_mr/remove/"
	UrlAddHostMr               = "/api/v3/merge_requests/%v/host_mr/add/"
	UrlRemoveHostMr            = "/api/v3/merge_requests/%v/host_mr/remove/"

	DependencyType_MrDependency      = "mr_dependency"
	DependencyType_VersionDependency = "version_dependency"
	DependencyType_CommonDependency  = "common_dependency"
	DependencyType_HostMr            = "host_mr"
)
