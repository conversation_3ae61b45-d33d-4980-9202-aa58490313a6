package optimus

import "time"

type WebHookSubscribeRecord struct {
	ID         int64      `gorm:"column:id" json:"id"`
	GroupName  string     `gorm:"column:group_name" json:"group_name"`
	EventID    string     `gorm:"column:event_id" json:"event_id"`
	URL        string     `gorm:"column:url" json:"url"`
	Author     string     `gorm:"column:author" json:"author"`
	CreateTime *time.Time `gorm:"column:create_time" json:"create_time"`
	UpdateTime *time.Time `gorm:"column:update_time" json:"update_time"`
}

func (c *WebHookSubscribeRecord) TableName() string {
	return "webhook_subscribe_record"
}
