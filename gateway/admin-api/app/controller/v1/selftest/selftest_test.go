package selftest

import (
	"code.byted.org/devinfra/hagrid/libs/sdk/jwt"
	"code.byted.org/gopkg/logs/v2/log"
	"context"
	"fmt"
	websocketClient "github.com/gorilla/websocket"
	"net/http"
	"testing"
)

func TestNewClient(t *testing.T) {
	t.<PERSON><PERSON>ow()
	c := context.Background()
	logUrl := "ws://bytesuite-core.bytedance.net/api/v1/sessions/666a8c569ed43e87909ef282/attach"
	url := "wss://ide.byted.org/ide/api/v1/users/proxy/bytecloud"
	username := ""
	token, err := jwt.NewClient().GetUserJWT(c, username)
	if err != nil {
		t.Error(err)
		return
	}
	header := http.Header{}
	header.Add("ide-authorization", fmt.Sprintf("user-auth-jwt %s", token))
	header.Add("ide-authorization-key", "x-jwt-token")
	header.Add("ide-destination-url", logUrl)
	log.V2.Info().With(c).Str("check req").KVs("url", url, "header", header).Emit()
	conn, _, err := websocketClient.DefaultDialer.Dial(url, header)
	if err != nil {
		log.V2.Error().With(c).Error(err).Emit()
		return
	}
	defer conn.Close()
}
