package webhook

import (
	"context"
	"strconv"
	"time"

	optimusModel "code.byted.org/devinfra/hagrid/gateway/admin-api/app/model/optimus"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/service/db/optimus"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/utils"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/utils/consts"
	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	hutils "code.byted.org/middleware/hertz/pkg/common/utils"
	"code.byted.org/middleware/hertz_ext/v2/binding"
)

func GetEvent(c context.Context, ctx *app.RequestContext) {
	query := struct {
		Page       int   `query:"page" binding:"required"`
		PageSize   int   `query:"page_size" binding:"required"`
		CategoryID int64 `query:"category_id"`
	}{}
	if err := binding.BindAndValidate(ctx, &query); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}
	var total int64
	answer := make([]*optimusModel.WebHookEvent, 0)
	if query.CategoryID != 0 {
		answer, _ = optimus.GetWebhookEventsByCategoryID(c, query.CategoryID, query.Page, query.PageSize)
		total, _ = optimus.CountWebhookEventsByCategoryID(c, query.CategoryID)
	} else {
		answer, _ = optimus.GetWebhookEvents(c, query.Page, query.PageSize)
		total, _ = optimus.CountWebhookEvents(c)
	}

	type eventData struct {
		ID            int64      `json:"id"`
		Name          string     `json:"name"`
		Title         string     `json:"title"`
		CategoryID    int64      `json:"category_id"`
		CategoryName  string     `json:"category_name"`
		CategoryTitle string     `json:"category_title"`
		Params        string     `json:"params"`
		CreateTime    *time.Time `json:"create_time"`
		UpdateTime    *time.Time `json:"update_time"`
	}
	list := make([]*eventData, 0)
	for _, event := range answer {
		category, _ := optimus.GetWebhookCategoryByID(c, event.CategoryID)
		if category == nil {
			continue
		}
		list = append(list, &eventData{
			ID:            event.ID,
			Name:          event.Name,
			Title:         event.Title,
			CategoryID:    event.CategoryID,
			CategoryName:  category.Name,
			CategoryTitle: category.Title,
			CreateTime:    event.CreatedAt,
			UpdateTime:    event.UpdatedAt,
			Params:        event.Params,
		})
	}
	utils.Response(c, ctx, consts.ResponseSuccess, hutils.H{
		"list":  list,
		"total": total,
	})
}
func CreateEvent(c context.Context, ctx *app.RequestContext) {
	body := struct {
		Name        string `json:"name"`
		Title       string `json:"title"`
		CategoryID  int64  `json:"category_id"`
		Description string `json:"description"`
		Params      string `json:"params"`
	}{}
	if err := binding.BindAndValidate(ctx, &body); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}
	// 判断category是否存在
	if category, _ := optimus.GetWebhookCategoryByID(c, body.CategoryID); category == nil {
		utils.Response(c, ctx, consts.ResponseError, "`category_id` not exists")
		return
	}
	// 判断是否重名
	if event, _ := optimus.GetWebhookEventsByName(c, body.Name); event != nil {
		utils.Response(c, ctx, consts.ResponseError, "name already exists, don't create again")
		return
	}
	insertID, err := optimus.CreateWebhookEvent(c, &optimusModel.WebHookEvent{
		Name:        body.Name,
		Title:       body.Title,
		CategoryID:  body.CategoryID,
		Description: body.Description,
		Params:      body.Params,
		IsDelete:    0,
	})
	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to create")
		logs.CtxError(c, err.Error())
		return
	}
	utils.Response(c, ctx, consts.ResponseSuccess, insertID)
}
func UpdateEvent(c context.Context, ctx *app.RequestContext) {
	body := struct {
		Name        string `json:"name"`
		Title       string `json:"title"`
		CategoryID  int64  `json:"category_id"`
		Description string `json:"description"`
		Params      string `json:"params"`
	}{}
	idStr := ctx.Param("id")
	ID, _ := strconv.ParseInt(idStr, 10, 64)
	if err := binding.BindAndValidate(ctx, &body); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}
	// 判断category是否存在
	if category, _ := optimus.GetWebhookCategoryByID(c, body.CategoryID); category == nil {
		utils.Response(c, ctx, consts.ResponseError, "`category_id` not exists")
		return
	}
	// 判断是否重名
	if event, _ := optimus.GetWebhookEventsByName(c, body.Name); event != nil && event.ID != ID {
		utils.Response(c, ctx, consts.ResponseError, "name already exists, don't create again")
		return
	}
	err := optimus.UpdateWebhookEvent(c, &optimusModel.WebHookEvent{
		Name:        body.Name,
		Title:       body.Title,
		CategoryID:  body.CategoryID,
		Description: body.Description,
		Params:      body.Params,
	}, ID)
	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to create")
		return
	}
	utils.Response(c, ctx, consts.ResponseSuccess, nil)
}
func DeleteEvent(c context.Context, ctx *app.RequestContext) {
	idStr := ctx.Param("id")
	ID, _ := strconv.ParseInt(idStr, 10, 64)
	// 判断event是否存在
	if category, _ := optimus.GetWebhookEventByID(c, ID); category == nil {
		utils.Response(c, ctx, consts.ResponseError, "`event` not exists")
		return
	}
	err := optimus.DeleteWebhookEvent(c, ID)
	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to delete")
		return
	}
	utils.Response(c, ctx, consts.ResponseSuccess, nil)
}
