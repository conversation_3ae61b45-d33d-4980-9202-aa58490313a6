load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "meego",
    srcs = [
        "api.go",
        "authen.go",
        "business.go",
        "client.go",
        "datastruct.go",
        "field.go",
        "group.go",
        "options.go",
        "projects.go",
        "response.go",
        "template.go",
        "tenancy.go",
        "user.go",
        "view_conf.go",
        "work_item.go",
        "workflow.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/meego",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/bits_err",
        "//libs/compatibletenancy/mtctx",
        "//libs/compatibletenancy/tenancies",
        "//libs/middleware/restymw",
        "//libs/thirdparty-sdks/meego/internal",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_code_hex_go_generics_cache//:go-generics-cache",
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_google_go_querystring//query",
        "@com_github_pkg_errors//:errors",
        "@com_github_tidwall_gjson//:gjson",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_lang_gg//optional",
    ],
)

go_test(
    name = "meego_test",
    srcs = [
        "authen_test.go",
        "business_test.go",
        "field_test.go",
        "group_test.go",
        "projects_test.go",
        "template_test.go",
        "user_test.go",
        "view_conf_test.go",
        "work_item_test.go",
        "workflow_test.go",
    ],
    embed = [":meego"],
    deps = [
        "//libs/compatibletenancy/mtctx",
        "//libs/compatibletenancy/tenancies",
        "//libs/thirdparty-sdks/meego/internal",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
    ],
)
