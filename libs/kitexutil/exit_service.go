package kitexutil

import (
	"context"
	"os"
	"path/filepath"

	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/devinfra/hagrid/libs/kitexutil/kitex_gen/libs/kitexutil"
	"code.byted.org/devinfra/hagrid/libs/kitexutil/kitex_gen/libs/kitexutil/exitservice"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/server"
)

const (
	maxGetFileNum  = 10
	maxListFileNum = 1000
)

type ExitServiceImpl struct{}

var exitChannel = make(chan bool)

func (s *ExitServiceImpl) KitexGracefulExit(ctx context.Context, req *kitexutil.ExitRequest) (*kitexutil.ExitResponse, error) {
	log.V2.Info().Str("KitexGracefulExit()").Emit()
	exitChannel <- true
	return &kitexutil.ExitResponse{}, nil
}

func (s *ExitServiceImpl) GetFile(ctx context.Context, req *kitexutil.FileRequest) (*kitexutil.FileResponse, error) {
	log.V2.Info().Str("GetFile()").Emit()
	switch req.Action {
	case kitexutil.FileRequest_ACTION_GET:
		files, err := listFiles(req.WildFilePath, maxGetFileNum)
		if err != nil {
			return nil, err
		}
		c := map[string][]byte{}
		for _, file := range files {
			data, err := os.ReadFile(file)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to read file %s", file)
			}
			c[file] = data
		}
		return &kitexutil.FileResponse{Contents: c}, nil
	case kitexutil.FileRequest_ACTION_LIST:
		files, err := listFiles(req.WildFilePath, maxListFileNum)
		if err != nil {
			return nil, err
		}
		c := map[string][]byte{}
		for _, file := range files {
			c[file] = []byte("ACTION_LIST")
		}
		return &kitexutil.FileResponse{
			Contents: c,
		}, nil
	case kitexutil.FileRequest_ACTION_DELETE:
		c := map[string][]byte{}
		if err := os.Remove(req.WildFilePath); err != nil {
			return nil, errors.Wrapf(err, "failed to remove file %s", req.WildFilePath)
		}
		c[req.WildFilePath] = []byte("ACTION_DELETE")
		return &kitexutil.FileResponse{Contents: c}, nil
	default:
		log.V2.Error().KV("action", req.Action).Emit()
		return nil, errors.Newf("unknown action: %v", req.Action)
	}
}

func RegisterExitService(svr server.Server) {
	if err := svr.RegisterService(exitservice.NewServiceInfo(), new(ExitServiceImpl)); err != nil {
		panic(err)
	}
	go func(svr server.Server) {
		<-exitChannel
		log.V2.Info().Str("Exit triggered by KitexGracefulExit()").Emit()
		if err := svr.Stop(); err != nil {
			log.V2.Error().Str("failed to stop server").Error(err).Emit()
		}
	}(svr)
}

func listFiles(wildFilePath string, limit int) ([]string, error) {
	files, err := filepath.Glob(wildFilePath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to glob [%s]", wildFilePath)
	}
	if len(files) > limit {
		return nil, errors.Newf("too many files [%d > %d] match with [%s]", len(files), limit, wildFilePath)
	}
	return files, nil
}
