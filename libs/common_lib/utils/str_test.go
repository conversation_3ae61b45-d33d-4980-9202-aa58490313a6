package utils

import (
	"regexp"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestUpperCamelToUnderscore(t *testing.T) {
	Convey("Given an empty string, when UpperCamelToUnderscore is called, then it returns an empty string", t, func() {
		input := ""
		expected := ""

		result := UpperCamelToUnderscore(input)

		So(result, ShouldEqual, expected)
	})
	Convey("Given a string with no uppercase letters, when UpperCamelToUnderscore is called, then it returns the same string", t, func() {
		input := "lowercase"
		expected := "lowercase"

		result := UpperCamelToUnderscore(input)

		So(result, ShouldEqual, expected)

	})
	Convey("Given a string with uppercase letters, when UpperCamelToUnderscore is called, then it returns the string with underscores", t, func() {
		input := "UpperCase"
		expected := "upper_case"

		result := UpperCamelToUnderscore(input)

		So(result, ShouldEqual, expected)
	})
}

func TestGenerateRandomBytes(t *testing.T) {
	Convey("Given a negative length, when GenerateRandomBytes is called, then it returns an error", t, func() {
		length := -1

		_, err := GenerateRandomBytes(length)

		So(err, ShouldNotBeNil)
	})
	Convey("Generate random bytes", t, func() {
		length := 10

		s, err := GenerateRandomBytes(length)
		So(err, ShouldBeNil)
		So(s, ShouldHaveLength, length)
	})
}

func TestGenerateRandomString(t *testing.T) {
	Convey("Given a negative length, when GenerateRandomString is called, then it returns an error", t, func() {
		length := -1

		_, err := GenerateRandomString(length)

		So(err, ShouldNotBeNil)
	})
	Convey("Generate random string", t, func() {
		length := 10

		s, err := GenerateRandomString(length)
		So(err, ShouldBeNil)
		So(s, ShouldHaveLength, length)

	})
}

func TestGenerateRandomStringPrue(t *testing.T) {
	allowRegexp := regexp.MustCompile(`^[0-9a-zA-Z]+$`)
	Convey("Given a length, when GenerateRandomString is called, then it generates a string of the correct length", t, func() {
		for length := 1; length < 100; length++ {
			result := GenerateRandomStringPrue(length)
			So(len(result), ShouldEqual, length)
			So(allowRegexp.MatchString(result), ShouldBeTrue)
		}
	})
	Convey("Given a length of zero, when GenerateRandomStringPrue is called, then it returns an empty string", t, func() {
		length := 0
		expected := ""

		result := GenerateRandomStringPrue(length)

		So(result, ShouldEqual, expected)
	})
}

func TestMaxLengthString(t *testing.T) {
	Convey("Given a string with a length less than the max length, when MaxLengthString is called, then it returns the same string", t, func() {
		s := "short"
		maxLength := 10

		result := MaxLengthString(s, maxLength)

		So(result, ShouldEqual, s)
	})
	Convey("Given a string with a length greater than the max length, when MaxLengthString is called, then it returns a substring of the string", t, func() {
		s := "this is a long string"
		maxLength := 10
		expected := "this is a "

		result := MaxLengthString(s, maxLength)

		So(result, ShouldEqual, expected)
	})
	Convey("n < 0", t, func() {
		s := "aabbcc"
		maxLength := -1
		expected := ""
		result := MaxLengthString(s, maxLength)

		So(result, ShouldEqual, expected)
	})
}
