package redis

import (
	"context"
	"errors"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kv/goredis"
)

func WaitUntilGetNxLock(ctx context.Context, client *goredis.Client, key string, value interface{}, expirationTime time.Duration) (err error) {
	if client == nil {
		err = errors.New("client == nil")
		logs.CtxError(ctx, err.Error())
		return
	}

	var redisErrCount = 0
	for {
		getLock, redisErr := client.SetNX(key, value, expirationTime).Result()
		if redisErr != nil {
			logs.CtxError(ctx, redisErr.Error())
			redisErrCount += 1
		}
		if redisErrCount >= 2 {
			return redisErr
		}
		if getLock {
			return nil
		}
		time.Sleep(time.Millisecond * 100)
	}
}
