package constdef

type Permission = string

const (
	// https://bytedance.feishu.cn/wiki/wikcnIk4spcsnHQLx1XvsQWshrh?sheet=oTt7kA
	EditProcessSetting Permission = "bits.appSpace.editProcessSetting"
	EditFeatureSetting Permission = "bits.appSpace.editFeatureSetting"
	AddRepo            Permission = "bits.appSpace.addRepo"
	EditRepoSetting    Permission = "bits.appRepo.editSetting"
	EditRepoDirectory  Permission = "bits.appRepo.editDirectory"
	CreateTask         Permission = "bits.appSpace.createTask"
	EditSchedule       Permission = "bits.appSpace.editSchedule"

	EditTask         Permission = "bits.appTask.edit"
	EditTaskAdvanced Permission = "bits.appTask.advancedEdit"
	CreateVersion    Permission = "bits.appSpace.createVersion"
	EditVersion      Permission = "bits.appVersion.edit"
	ViewVersion      Permission = "bits.appVersion.view"
	EditVersionUser  Permission = "bits.appVersion.editMember"

	// onesite space dev config
	// https://bytedance.feishu.cn/wiki/Zi9bwbDF8iWoyzkMldqcxDtOnab 管理模块权限
	ManageDevTaskTemplate        = "bits.space.manageDevTaskTemplate"
	ManageDeploymentDependencies = "bits.space.manageDeploymentDependencies"
	ManageReleaseTicketTemplate  = "bits.space.manageReleaseTicketTemplate"

	MessageConfigBindGroups Permission = "bits.space.bindGroup"
	MessageConfigManagement Permission = "bits.space.manageNotification"

	// 设置发布计划的权限
	SetReleasePlan Permission = "bits.space.set_release_plan"

	// BC 2.1 permission
	CreateDevelopmentTask   Permission = "bits.space.createTask"
	MergeDevelopmentTask    Permission = "bits.devTask.merge"
	EditDevelopmentTask     Permission = "bits.devTask.edit"
	CompleteDevelopmentTask Permission = "bits.developmentStage.complete"
	BindReleaseTicket       Permission = "bits.devTask.bindReleaseTicket"
	DevTaskRevert           Permission = "bits.devTask.revert"
	CloseDevTask            Permission = "bits.devTask.cancel"

	LockIntegration          Permission = "bits.integration.lock"
	UnLockIntegration        Permission = "bits.integration.unlock"
	ManageIntegrationDevTask Permission = "bits.integration.manageDevTask"
	SyncBranch               Permission = "bits.releaseTicket.synchronousBranch"
	IntegrationForceMerge    Permission = "bits.integration.forceMerge"

	EditWorkItemSpaceSettings Permission = "bits.serviceSpace.editWorkitemSpaceSettings" // 编辑工作项空间设置
	EditReleaseTicket         Permission = "bits.releaseTicket.edit"                     // 编辑发布单
	CreateReleaseTicket       Permission = "bits.space.createTicket"                     // 创建发布单

)
